package currencies

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	router *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	currencyService services.CurrencyService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	authenticatedAPI := router.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		authenticatedAPI.GET("/currencies", filterCurrencies(transactionsDB, currencyService))
		authenticatedAPI.GET("/currencies/:id", getCurrency(transactionsDB, currencyService))
	}
}
