APP_VERSION := 1.3.35
APP_NAME := optimus-etims-pos-backend-prod_v$(APP_VERSION).sh


build:
	gox -osarch="windows/386" 

compile:	
	GOOS=windows GOARCH=386 go build -o struts-pos-backend_Ws_v$(APP_VERSION).exe main.go

compile-fixed: 	
	GOOS=windows GOARCH=386 go build -o struts-pos-backend_Ws_v1.0.0.exe main.go
	scp .\target\struts-etims-vscu-$(APP_VERSION).jar gidemn@**************:/home/<USER>/Apps/ETIMS_VSCU
	scp P051468536N_00.zip gidemn@**************:/home/<USER>/AppData

compile-prod: 
	GOOS=linux GOARCH=amd64 go build -o $(APP_NAME) main.go	
	cp $(APP_NAME) ../$(APP_NAME)
	cp -r app/database/migrations/ ../app/database/
	cp -r templates/ ../
	rm $(APP_NAME)

migrate: 
	goose -dir app/db/migrations create $(name) sql 

up: 
	docker-compose -f docker-compose.yml up --build --detach		

run: 
	go run main.go 

server: 
	go run main.go 

test: 
	go test ...\.. 
