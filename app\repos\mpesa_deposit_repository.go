package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
)

const (
	countMPesaDepositsSQL = `SELECT COUNT(*) AS count FROM mpesa_deposits WHERE 1=1`

	createMPesaDepositSQL = `INSERT INTO mpesa_deposits (bill_ref_number, business_short_code, first_name, invoice_number, last_name, 
		middle_name, msisdn, org_account_balance, organization_id, phone_number_id, third_party_trans_id, transaction_amount, 
		transaction_id, transaction_time, transaction_type, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17) RETURNING id`

	selectMPesaDepositByIDSQL = selectMPesaDepositSQL + ` AND id=$1`

	selectMPesaDepositByTransactionIDSQL = selectMPesaDepositSQL + ` AND transaction_id=$1`

	selectMPesaDepositSQL = `SELECT id, bill_ref_number, business_short_code, first_name, invoice_number, last_name, 
		middle_name, msisdn, org_account_balance, organization_id, phone_number_id, third_party_trans_id, transaction_amount, 
		transaction_id, transaction_time, transaction_type, created_at, updated_at FROM mpesa_deposits WHERE 1=1`

	updateMPesaDepositSQL = `UPDATE mpesa_deposits SET phone_number_id=$1, updated_at=$2 WHERE id=$3`
)

type (
	MPesaDepositRepository interface {
		CountMPesaDeposits(ctx context.Context, filter *entities.PaginationFilter) (int, error)
		FilterMPesaDeposits(ctx context.Context, db txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) ([]*entities.MPesaDeposit, error)
		FindByID(ctx context.Context, MPesaDepositID int64) (*entities.MPesaDeposit, error)
		FindByTransactionID(ctx context.Context, key string) (*entities.MPesaDeposit, error)
		Save(ctx context.Context, MPesaDeposit *entities.MPesaDeposit) error
	}

	AppMPesaDepositRepository struct {
		db *sql.DB
	}
)

func NewMPesaDepositRepository(db *sql.DB) MPesaDepositRepository {
	return &AppMPesaDepositRepository{db: db}
}

func (r *AppMPesaDepositRepository) CountMPesaDeposits(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countMPesaDepositsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppMPesaDepositRepository) FilterMPesaDeposits(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.MPesaDeposit, error) {

	mpesaDeposits := make([]*entities.MPesaDeposit, 0)

	args := make([]interface{}, 0)
	query := selectMPesaDepositSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return mpesaDeposits, err
	}

	defer rows.Close()

	for rows.Next() {
		mpesaDeposit, err := r.scanRowIntoMPesaDeposit(rows)
		if err != nil {
			return mpesaDeposits, err
		}

		mpesaDeposits = append(mpesaDeposits, mpesaDeposit)
	}

	return mpesaDeposits, rows.Err()
}

func (r *AppMPesaDepositRepository) FindByID(
	ctx context.Context,
	MPesaDepositID int64,
) (*entities.MPesaDeposit, error) {
	row := r.db.QueryRow(selectMPesaDepositByIDSQL, MPesaDepositID)
	return r.scanRowIntoMPesaDeposit(row)
}

func (r *AppMPesaDepositRepository) FindByTransactionID(
	ctx context.Context,
	key string,
) (*entities.MPesaDeposit, error) {
	row := r.db.QueryRow(selectMPesaDepositByTransactionIDSQL, key)
	return r.scanRowIntoMPesaDeposit(row)
}

func (r *AppMPesaDepositRepository) Save(
	ctx context.Context,
	mpesaDeposit *entities.MPesaDeposit,
) error {

	mpesaDeposit.Timestamps.Touch()
	var err error

	if mpesaDeposit.IsNew() {

		err = r.db.QueryRow(
			createMPesaDepositSQL,
			mpesaDeposit.BillRefNumber,
			mpesaDeposit.BusinessShortCode,
			mpesaDeposit.FirstName,
			mpesaDeposit.InvoiceNumber,
			mpesaDeposit.LastName,
			mpesaDeposit.MiddleName,
			mpesaDeposit.MSISDN,
			mpesaDeposit.OrgAccountBalance,
			mpesaDeposit.OrganizationID,
			mpesaDeposit.PhoneNumberID,
			mpesaDeposit.ThirdPartyTransID,
			mpesaDeposit.TransactionAmount,
			mpesaDeposit.TransactionID,
			mpesaDeposit.TransactionTime,
			mpesaDeposit.TransactionType,
			mpesaDeposit.CreatedAt,
			mpesaDeposit.UpdatedAt,
		).Scan(&mpesaDeposit.ID)

	} else {

		_, err = r.db.Exec(
			updateMPesaDepositSQL,
			mpesaDeposit.PhoneNumberID,
			mpesaDeposit.UpdatedAt,
			mpesaDeposit.ID,
		)
	}

	if err != nil {
		log.Printf("error saving api_key, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppMPesaDepositRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.UserID > 0 {
		query += fmt.Sprintf(" AND user_id = $%d", currentIndex)
		args = append(args, filter.UserID)
		currentIndex += 1
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(api_key) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppMPesaDepositRepository) scanRowIntoMPesaDeposit(
	rowScanner txns_db.RowScanner,
) (*entities.MPesaDeposit, error) {

	var mpesaDeposit entities.MPesaDeposit

	err := rowScanner.Scan(
		&mpesaDeposit.ID,
		&mpesaDeposit.BillRefNumber,
		&mpesaDeposit.BusinessShortCode,
		&mpesaDeposit.FirstName,
		&mpesaDeposit.InvoiceNumber,
		&mpesaDeposit.LastName,
		&mpesaDeposit.MiddleName,
		&mpesaDeposit.MSISDN,
		&mpesaDeposit.OrgAccountBalance,
		&mpesaDeposit.OrganizationID,
		&mpesaDeposit.PhoneNumberID,
		&mpesaDeposit.ThirdPartyTransID,
		&mpesaDeposit.TransactionAmount,
		&mpesaDeposit.TransactionID,
		&mpesaDeposit.TransactionTime,
		&mpesaDeposit.TransactionType,
		&mpesaDeposit.CreatedAt,
		&mpesaDeposit.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning mpesaDeposit,  err=[%v]\n", err.Error())
		return &mpesaDeposit, err
	}

	return &mpesaDeposit, nil
}
