package analytics

import (
	"struts-pos-backend/app/database"

	"struts-pos-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
) {

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/analytics", getAnalytics(transactionsDB, analyticsService))
	}
}
