package services

import (
	"context"
	"log"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/ctxhelper"
	"time"

	"gopkg.in/guregu/null.v3"
)

type AnalyticsService interface {
	GetDashboardAnalytics(ctx context.Context, operations txns_db.TransactionsSQLOperations, storeId int64) (*entities.DashboardAnalytics, error)
}

type AppAnalyticsService struct {
	categoryRepository          repos.CategoryRepository
	customerRepository          repos.CustomerRepository
	dashboardAnalyticRepository repos.DashboardAnalyticRepository
	excelFileUploadRepository   repos.ExcelFileUploadRepository
	itemRepository              repos.ItemRepository
	invoiceRepository           repos.InvoiceRepository
	organizationRepository      repos.OrganizationRepository
	saleRepository              repos.SaleRepository
	storeRepository             repos.StoreRepository
	userRepository              repos.UserRepository
}

func NewAnalyticsService(
	categoryRepository repos.CategoryRepository,
	customerRepository repos.CustomerRepository,
	dashboardAnalyticRepository repos.DashboardAnalyticRepository,
	excelFileUploadRepository repos.ExcelFileUploadRepository,
	itemRepository repos.ItemRepository,
	invoiceRepository repos.InvoiceRepository,
	organizationRepository repos.OrganizationRepository,
	saleRepository repos.SaleRepository,
	storeRepository repos.StoreRepository,
	userRepository repos.UserRepository,
) AnalyticsService {
	return &AppAnalyticsService{
		categoryRepository:          categoryRepository,
		customerRepository:          customerRepository,
		dashboardAnalyticRepository: dashboardAnalyticRepository,
		excelFileUploadRepository:   excelFileUploadRepository,
		itemRepository:              itemRepository,
		invoiceRepository:           invoiceRepository,
		organizationRepository:      organizationRepository,
		saleRepository:              saleRepository,
		storeRepository:             storeRepository,
		userRepository:              userRepository,
	}
}

func (s *AppAnalyticsService) GetDashboardAnalytics(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeId int64,
) (*entities.DashboardAnalytics, error) {

	dashboardAnalytics := &entities.DashboardAnalytics{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		log.Printf("failed to find user by id=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return dashboardAnalytics, err
	}

	organization, err := s.organizationRepository.GetOrganizationByID(user.OrganizationID)
	if err != nil {
		log.Printf("failed to retrieve organization for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return dashboardAnalytics, err
	}

	defaultStore, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	if err != nil && utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return dashboardAnalytics, err
	}

	dashboardAnalytic, err := s.dashboardAnalyticRepository.FindByStoreID(ctx, defaultStore.ID)
	if err != nil && !utils.IsErrNoRows(err) {
		log.Printf("error finding dashboard analytics for store_id=%v: %v", defaultStore.ID, err)
		return dashboardAnalytics, err
	}

	if dashboardAnalytic != nil {
		dashboardAnalytics.SignedInvoicesCount = int(dashboardAnalytic.InvoicesCount)
		dashboardAnalytics.TotalInvoicesCount = int(dashboardAnalytic.InvoicesCount)
		dashboardAnalytics.TotalCreditNotesCount = int(dashboardAnalytic.CreditNotesCount)
		dashboardAnalytics.FailedInvoicesCount = 0
		dashboardAnalytics.StoresCount = int(dashboardAnalytic.StoresCount)
	}

	filter := &entities.PaginationFilter{
		OrganizationID: organization.ID,
		SearchCriteria: "",
		StoreID:        defaultStore.ID,
		UserID:         tokenInfo.UserID,
	}

	chartSummary := []entities.ChartSummary{}
	months := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12}
	filter.Show = ""
	if filter.Year == 0 {
		filter.Year = time.Now().Year()
	}

	for _, month := range months {

		// TODO: Save the below data periodically in a single table for analytics and fetch the data from there

		chartSummaryData := entities.ChartSummary{}
		chartSummaryData.Month = month
		filter.Month = month

		currentDate := time.Date(filter.Year, time.Month(month), 1, 0, 0, 0, 0, utils.LocKenya)
		filter.StartDate = null.TimeFrom(currentDate)
		filter.EndDate = null.TimeFrom(currentDate.AddDate(0, 1, 0))

		// All invoices count
		allInvoicesCount, err := s.invoiceRepository.CountInvoices(ctx, filter)
		if err != nil {
			return dashboardAnalytics, utils.NewDatabaseError(
				err,
				"Failed to count invoices for dashboard chart report with filter=[%v]",
				filter,
			)
		}

		chartSummaryData.TotalInvoices = allInvoicesCount

		customersCount, err := s.customerRepository.CountCustomers(ctx, filter, storeId)
		if err != nil {
			return dashboardAnalytics, utils.NewDatabaseError(
				err,
				"Failed to count customers for dashboard chart report with filter=[%v]",
				filter,
			)
		}

		chartSummaryData.CustomersCount = customersCount

		// Success
		filter.SearchCriteria = "signed_invoices"
		successInvoicesCount, err := s.invoiceRepository.CountInvoices(ctx, filter)
		if err != nil {
			return dashboardAnalytics, utils.NewDatabaseError(
				err,
				"Failed to count invoices for dashboard chart report with filter=[%v]",
				filter,
			)
		}

		chartSummaryData.ProcessedSuccessfully = successInvoicesCount

		// Failed
		filter.SearchCriteria = "invoices_failed_to_sign"
		failedInvoicesCount, err := s.invoiceRepository.CountInvoices(ctx, filter)
		if err != nil {
			return dashboardAnalytics, utils.NewDatabaseError(
				err,
				"Failed to count invoices for dashboard chart report with filter=[%v]",
				filter,
			)
		}

		chartSummaryData.Failed = failedInvoicesCount

		totalSales, err := s.saleRepository.CountSales(ctx, filter, storeId)
		if err != nil {
			return dashboardAnalytics, utils.NewDatabaseError(
				err,
				"Failed to count sales for dashboard chart report with filter=[%v]",
				filter,
			)
		}

		chartSummaryData.TotalSales = totalSales

		chartSummary = append(chartSummary, chartSummaryData)
		filter.SearchCriteria = ""
	}

	dashboardAnalytics.ChartSummary = chartSummary

	return dashboardAnalytics, nil
}
