package excel_file_uploads

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	excelFileUploadService services.ExcelFileUploadService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/excel_file_uploads", createExcelFileUpload(transactionsDB, excelFileUploadService))
		protectedAPI.GET("/excel_file_uploads", filterExcelFileUploads(transactionsDB, excelFileUploadService))
		protectedAPI.GET("/excel_file_uploads/:id", getExcelFileUpload(transactionsDB, excelFileUploadService))
		protectedAPI.PUT("/excel_file_uploads/:id", updateExcelFileUpload(transactionsDB, excelFileUploadService))
		protectedAPI.DELETE("/excel_file_uploads/:id", deleteExcelFileUpload(transactionsDB, excelFileUploadService))
		protectedAPI.POST("/excel_file_uploads_t", testExcelFileUpload(transactionsDB, excelFileUploadService))
	}
}
