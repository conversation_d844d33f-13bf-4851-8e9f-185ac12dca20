package etims_stock

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	etimsStockService services.EtimsStockService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyApiKeyUser(transactionsDB, apiKeyRepository))
	{
		protectedAPI.POST("/etims_vscu/stock", createEtimsStock(transactionsDB, etimsStockService))
		protectedAPI.GET("/etims_vscu/stock", filterEtimsStock(transactionsDB, etimsStockService))
		protectedAPI.GET("/etims_vscu/stock/:id", getEtimsStock(transactionsDB, etimsStockService))
	}
}
