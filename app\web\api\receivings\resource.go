package receivings

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/webutils"
)

func createReceiving(
	transactionsDB *database.AppTransactionsDB,
	receivingService services.ReceivingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var receivingForm forms.CreateReceivingForm
		err := ctx.Bind(&receivingForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind receiving form while creating receiving",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		receiving, err := receivingService.CreateReceiving(ctx.Request.Context(), transactionsDB, &receivingForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to sign receiving. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, receiving)
	}
}

func downloadReceivingsExcel(
	transactionsDB *database.AppTransactionsDB,
	receivingService services.ReceivingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering receivings",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		t := time.Now()
		dateString := t.Format("Receivings-2006-01-02-150405")
		outputFolder := os.Getenv("OUTPUT_FOLDER")
		excelFileName := fmt.Sprintf("%v.xlsx", dateString)
		excelFilePath := fmt.Sprintf("%v//%v", outputFolder, excelFileName)

		_, err = receivingService.DownloadReceivings(context.Background(), transactionsDB, filter, excelFilePath)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to download receivings",
			})
			return
		}

		// ctx.JSON(http.StatusOK, receivingList)
		file, err := os.Open(excelFilePath)
		if err != nil {
			ctx.String(http.StatusInternalServerError, fmt.Sprintf("Error opening file: %s", err))
			return
		}
		defer file.Close()

		// Set the headers for the response
		ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		ctx.Header("Content-Disposition", "attachment; filename="+excelFileName)

		// Copy the file content to the response writer
		_, err = io.Copy(ctx.Writer, file)
		if err != nil {
			ctx.String(500, fmt.Sprintf("Error copying file to response: %s", err))
			return
		}
	}
}

func filterReceivings(
	transactionsDB *database.AppTransactionsDB,
	receivingService services.ReceivingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering receivings",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		receivingList, err := receivingService.FilterReceivings(context.Background(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter receivings",
			})
			return
		}

		ctx.JSON(http.StatusOK, receivingList)
	}
}

func getReceiving(
	transactionsDB *database.AppTransactionsDB,
	receivingService services.ReceivingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		receivingIDStr := ctx.Param("id")
		receivingID, err := strconv.ParseInt(receivingIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse receivingID=[%v], err=[%v]\n", receivingIDStr, err)
		}

		receiving, err := receivingService.FindReceivingByID(ctx.Request.Context(), receivingID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to get receiving",
			})
			return
		}

		ctx.JSON(http.StatusOK, receiving)
	}
}

func updateReceiving(
	transactionsDB *database.AppTransactionsDB,
	receivingService services.ReceivingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		receivingIDStr := ctx.Param("id")
		receivingID, err := strconv.ParseInt(receivingIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse receivingID=[%v], err=[%v]\n", receivingIDStr, err)
		}

		var form forms.UpdateReceivingForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind receiving form while updating receiving",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		receiving, err := receivingService.UpdateReceiving(ctx.Request.Context(), transactionsDB, receivingID, &form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to update receiving",
			})
			return
		}

		ctx.JSON(http.StatusOK, receiving)
	}
}
