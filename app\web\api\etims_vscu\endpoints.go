package etims_vscu

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	etimsVSCUService services.EtimsVSCUService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyApiKeyUser(transactionsDB, apiKeyRepository))
	{
		protectedAPI.POST("/etims_vscu/invoices", signInvoice(transactionsDB, etimsVSCUService))
		protectedAPI.GET("/etims_vscu/invoices", filterInvoices(transactionsDB, apiKeyService))
		protectedAPI.GET("/etims_vscu/invoices/:id", getInvoice(transactionsDB, apiKeyService, etimsVSCUService))
	}
}
