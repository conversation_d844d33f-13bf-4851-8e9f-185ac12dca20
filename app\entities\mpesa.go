package entities

type (
	MPesaAccountBalanceResponse struct {
		OriginatorConversationID string `json:"OriginatorConversationID"`
		ConversationID           string `json:"ConversationID"`
		ResponseCode             string `json:"ResponseCode"`
		ResponseDescription      string `json:"ResponseDescription"`
	}

	MPesaAuthentication struct {
		AccessToken string `json:"access_token"`
		ExpiresIn   string `json:"expires_in"`
	}

	MPesaPhoneNumberHashDecoderResponse struct {
		Hash        string  `json:"hash"`
		PhoneNumber string  `json:"phone_number"`
		Provider    string  `json:"provider"`
		NewBalance  float64 `json:"new_balance"`
	}

	PhoneNumberHashTopupResponse struct {
		AccountNumber string  `json:"account_number"`
		Amount        float64 `json:"amount"`
		PhoneNumber   string  `json:"phone_number"`
		CreditBalance float64 `json:"credit_balance"`
	}

	MPesaRegisterURLErrorResponse struct {
		RequestId    string `json:"requestId"`
		ErrorCode    string `json:"errorCode"`
		ErrorMessage string `json:"errorMessage"`
	}

	MPesaRegisterURLResponse struct {
		OriginatorCoversationID string `json:"OriginatorCoversationID"`
		ResponseCode            string `json:"ResponseCode"`
		ResponseDescription     string `json:"ResponseDescription"`
	}

	MPesaSTKPushResponse struct {
		MerchantRequestID   string `json:"MerchantRequestID"`
		CheckoutRequestID   string `json:"CheckoutRequestID"`
		ResponseCode        string `json:"ResponseCode"`
		ResponseDescription string `json:"ResponseDescription"`
		CustomerMessage     string `json:"CustomerMessage"`
	}

	MPesaReversalResponse struct {
		OriginatorConversationID string `json:"OriginatorConversationID"`
		ConversationID           string `json:"ConversationID"`
		ResponseCode             string `json:"ResponseCode"`
		ResponseDescription      string `json:"ResponseDescription"`
	}

	MPesaTransactionStatusResponse struct {
		OriginatorConversationID string `json:"OriginatorConversationID"`
		ConversationID           string `json:"ConversationID"`
		ResponseCode             string `json:"ResponseCode"`
		ResponseDescription      string `json:"ResponseDescription"`
	}
)
