package router

import (
	"strings"
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/services"

	"os"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-contrib/static"
	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/web/auth"
	esdWsRouter "struts-pos-backend/app/web/router"
)

func SetupRouter(
	transactionsDB *database.AppTransactionsDB,
	sessionAuthenticator auth.SessionAuthenticator,
	activityRepository repos.ActivityRepository,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	creditNoteService services.CreditNoteService,
	etimsItemService services.EtimsItemService,
	etimsStockService services.EtimsStockService,
	etimsVSCUService services.EtimsVSCUService,
	excelFileUploadService services.ExcelFileUploadService,
	userRepository repos.UserRepository,
	analyticsService services.AnalyticsService,
	categoryService services.CategoryService,
	currencyService services.CurrencyService,
	customerService services.CustomerService,
	invoiceService services.InvoiceService,
	itemService services.ItemService,
	paymentService services.PaymentService,
	paymentTransactionsService services.PaymentTransactionsService,
	receivingService services.ReceivingService,
	saleService services.SaleService,
	settingService services.SettingService,
	sessionService services.SessionService,
	storeRepository repos.StoreRepository,
	storeService services.StoreService,
	supplierService services.SupplierService,
	userService services.UserService,
) *gin.Engine {

	environment := os.Getenv("ENVIRONMENT")
	router := createAndSetUpRouter(sessionAuthenticator, environment)

	router.Static("/downloads", "./downloads")
	router.Static("/assets", "./assets")
	router.LoadHTMLGlob("templates/*.html")
	router.Use(static.Serve("/", static.LocalFile("./web", true)))
	router.NoRoute(func(ctx *gin.Context) {
		if !strings.HasPrefix(ctx.Request.RequestURI, "/api") {
			ctx.File("./web")
		}
	})

	esdWsRouter.AddStrutsOptimusEndpoints(
		router,
		transactionsDB,
		sessionAuthenticator,
		activityRepository,
		apiKeyRepository,
		apiKeyService,
		creditNoteService,
		etimsItemService,
		etimsStockService,
		etimsVSCUService,
		excelFileUploadService,
		userRepository,
		analyticsService,
		categoryService,
		currencyService,
		customerService,
		invoiceService,
		itemService,
		paymentService,
		paymentTransactionsService,
		receivingService,
		saleService,
		settingService,
		sessionService,
		storeRepository,
		storeService,
		supplierService,
		userService,
	)

	return router
}

func createAndSetUpRouter(
	sessionAuthenticator auth.SessionAuthenticator,
	environment string,
) *gin.Engine {

	switch environment {
	case "dev":
		gin.SetMode(gin.DebugMode)
	case "development":
		gin.SetMode(gin.DebugMode)
	case "production":
		gin.SetMode(gin.ReleaseMode)
	case "stage":
		gin.SetMode(gin.ReleaseMode)
	default:
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	router.Use(middleware.Secure())
	router.Use(middleware.GzipCompression())
	router.Use(middleware.CORSMiddleware())

	router.Use(middleware.SetRequestId())
	router.Use(middleware.HealthCheck())
	router.Use(middleware.SetupAppContext(sessionAuthenticator))

	store := cookie.NewStore([]byte("secret"))
	router.Use(sessions.Sessions("mysession", store))

	router.SetTrustedProxies(nil)

	return router
}
