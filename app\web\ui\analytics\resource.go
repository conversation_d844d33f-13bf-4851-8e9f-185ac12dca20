package analytics

import (
	"context"
	"net/http"
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/globals"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

func getAnalytics(
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		session := sessions.Default(ctx)
		user := session.Get(globals.Userkey)

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		dashboardAnalytics, err := analyticsService.GetDashboardAnalytics(context.Background(), transactionsDB, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to retrieve dashboard analytics",
			})
			return
		}

		ctx.HTML(http.StatusOK, "analytics.html", gin.H{
			"content":            "Analytics",
			"user":               user,
			"dashboardAnalytics": dashboardAnalytics,
		})

	}
}
