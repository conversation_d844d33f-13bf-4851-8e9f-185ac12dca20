package entities

import "time"

type (
	Invoice struct {
		SequentialIdentifier
		AmountPaidInUSD                 float64               `json:"amt_paid_in_usd,omitempty"`
		CanSign                         bool                  `json:"can_sign,omitempty"`
		CreditNoteNumber                string                `json:"credit_note_number,omitempty"`
		CuNumber                        string                `json:"cu_number,omitempty"`
		CurrentReceiptNumber            int64                 `json:"current_receipt_number,omitempty"`
		CustomerIDTypeStr               CustomerIDTypeStr     `json:"customer_identifier_type,omitempty"`
		CustomerIDTypeValue             string                `json:"customer_identifier_value,omitempty"`
		CustomerName                    string                `json:"customer_name,omitempty"`
		CustomerEmail                   string                `json:"customer_email,omitempty"`
		CustomerNumber                  string                `json:"customer_number,omitempty"`
		CustomerPhone                   string                `json:"customer_phone,omitempty"`
		CustomerTIN                     string                `json:"customer_tin,omitempty"`
		CustomerVRN                     string                `json:"customer_vrn,omitempty"`
		DateCreated                     time.Time             `json:"date_created,omitempty"`
		DocumentTypeCode                *string               `json:"document_type_code,omitempty"` // S - Sale, R - Credit Note
		DC                              int                   `json:"dc,omitempty"`
		EtimsInternalData               *string               `json:"etims_internal_data,omitempty"`
		EtimsItems                      []*EtimsItem          `json:"etims_items,omitempty"`
		EtimsMrcNumber                  *string               `json:"etims_mrc_number,omitempty"`
		EtimsReceiptNumber              *int64                `json:"etims_receipt_number,omitempty"`
		EtimsReceiptSignature           *string               `json:"etims_receipt_signature,omitempty"`
		EtimsResultCode                 *string               `json:"etims_result_code,omitempty"`
		EtimsResultDateTime             *string               `json:"etims_result_date_time,omitempty"`
		EtimsResultMessage              *string               `json:"etims_result_message,omitempty"`
		EtimsSDCID                      *string               `json:"etims_sdcid,omitempty"`
		EtimsTotalReceiptNumber         *int64                `json:"etims_total_receipt_number,omitempty"`
		EtimsVSCUReceiptPublicationDate *string               `json:"etims_vscu_receipt_publication_date,omitempty"`
		ExchangeRate                    float64               `json:"exchange_rate,omitempty"`
		FileName                        string                `json:"file_name,omitempty"`
		GC                              int                   `json:"gc,omitempty"`
		GrandTotal                      float64               `json:"grand_total,omitempty"`
		GrossAmount                     float64               `json:"gross_amount,omitempty"`
		GrossAmountStr                  string                `json:"gross_amount_str,omitempty"`
		HasLevy                         bool                  `json:"has_levy,omitempty"`
		HasVAT                          bool                  `json:"has_vat,omitempty"`
		InvoiceDate                     string                `json:"invoice_date,omitempty"`
		InvoiceItems                    []*InvoiceItem        `json:"invoice_items,omitempty"`
		InvoiceNumber                   string                `json:"invoice_number,omitempty"`
		InvoiceStrContent               string                `json:"invoice_str_content,omitempty"`
		IsCreditNote                    bool                  `json:"is_credit_note,omitempty"`
		IsEmailSent                     bool                  `json:"is_email_sent,omitempty"`
		IsZReported                     bool                  `json:"is_z_reported,omitempty"`
		Items                           []*Item               `json:"items,omitempty"`
		ItemsString                     string                `json:"items_string,omitempty"`
		ItemThatHasVAT                  string                `json:"item_that_has_vat,omitempty"`
		NetAmount                       float64               `json:"net_amount,omitempty"`
		OrganizationID                  int64                 `json:"organization_id,omitempty"`
		OriginalFileName                string                `json:"original_file_name,omitempty"`
		OriginalInvoiceNumber           string                `json:"original_invoice_number,omitempty"`
		ReceiptCode                     string                `json:"receipt_code,omitempty"`
		Rctvnum                         string                `json:"rctvnum,omitempty"`
		Signature                       string                `json:"signature,omitempty"`
		SignedDate                      string                `json:"signed_date,omitempty"`
		SignaturePositioning            *SignaturePositioning `json:"-"`
		StoreID                         *int64                `json:"store_id,omitempty"`
		TaxableAmount                   float64               `json:"taxable_amount,omitempty"`
		TextData                        string                `json:"text_data,omitempty"`
		TotalTaxFloat                   float64               `json:"total_tax_float,omitempty"`
		TRAVFDItems                     []*TRAVFDItem         `json:"tra_vfd_items,omitempty"`
		Vat                             float64               `json:"vat,omitempty"`
		VatA                            float64               `json:"vat_a,omitempty"` // Ex
		VatB                            float64               `json:"vat_b,omitempty"` // 16%
		VatC                            float64               `json:"vat_c,omitempty"` // 0%
		VatE                            float64               `json:"vat_e,omitempty"` // 8%
		VatAmtStr                       string                `json:"vat_amt_str,omitempty"`
		VerificationURL                 string                `json:"verification_url,omitempty"`
		Timestamps
	}

	InvoiceList struct {
		Invoices   []*Invoice  `json:"invoices"`
		Pagination *Pagination `json:"pagination"`
	}

	// InvoicesRepsApiReport struct
	InvoicesRepsApiReport struct {
		Invoices      []Invoice `json:"invoices"`
		InvoicesCount int64     `json:"invoices_count"`
		Limit         int64     `json:"limit"`
		NumPages      int64     `json:"num_pages"`
		Offset        int64     `json:"offset"`
		Search        string    `json:"search"`
	}

	InvoiceSigningResult struct {
		ETIMSSDCID                      *string `json:"etims_sdcid"`
		ETIMSInternalData               *string `json:"etims_internal_data"`
		EtimsMrcNumber                  *string `json:"etims_mrc_number"`
		EtimsTotalReceiptNumber         *int64  `json:"etims_total_receipt_number"`
		EtimsVSCUReceiptPublicationDate *string `json:"etims_vscu_receipt_publication_date"`
		ETIMSReceiptSignature           *string `json:"etims_receipt_signature"`
		EtimsResultDateTime             *string `json:"etims_result_date_time"`
		InvoiceNumber                   string  `json:"invoice_number"`
		VerificationURL                 string  `json:"verification_url"`
	}

	// Result - db result
	Result struct {
		Total float64 `json:"total"`
	}

	// InvoiceReport struct
	InvoiceReport struct {
		GrandTotal    float64 `json:"grand_total"`
		GrossAmount   float64 `json:"gross_amount"`
		InvoiceDate   string  `json:"invoice_date"`
		InvoiceNumber string  `json:"invoice_number"`
		Signature     string  `json:"signature"`
		Vat           float64 `json:"vat"`
	}

	// SummaryReport struct
	SummaryReport struct {
		AllInvoicesCount              int64   `json:"all_invoices_count"`
		AllInvoicesTotalSum           float64 `json:"all_invoices_total_sum"`
		AllInvoicesVatSum             float64 `json:"all_invoices_vat_sum"`
		InvoicesThatFailedToSignCount int64   `json:"invoices_failed_to_sign_count"`
	}
)
