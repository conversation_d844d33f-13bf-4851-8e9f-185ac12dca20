package etims_items

import (
	"context"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/webutils"
)

func createEtimsItem(
	transactionsDB *database.AppTransactionsDB,
	etimsItemService services.EtimsItemService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {
		var form forms.ETIMSInvoiceItemForm
		err := ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind form while signing invoice",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		invoice, err := etimsItemService.CreateEtimsItem(ctx.Request.Context(), transactionsDB, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to crete item on KRA ETIMS name=[%v]",
				form.ItemName,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, invoice)
	}
}

func filterEtimsItems(
	transactionsDB *database.AppTransactionsDB,
	etimsItemService services.EtimsItemService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering ETIMS Items",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		apiKeyList, err := etimsItemService.FilterEtimsItems(context.Background(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter ETIMS Items",
			})
			return
		}

		ctx.JSON(http.StatusOK, apiKeyList)
	}
}

func getEtimsItem(
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
	etimsItemService services.EtimsItemService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		apiKeyIDStr := ctx.Param("id")
		apiKeyID, err := strconv.ParseInt(apiKeyIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse apiKeyID=[%v], err=[%v]\n", apiKeyIDStr, err)
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		apiKey, err := apiKeyService.FindAPIKeyByID(context.Background(), apiKeyID, storeId)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find apiKey by id=[%v]",
				apiKeyIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, apiKey)
	}
}
