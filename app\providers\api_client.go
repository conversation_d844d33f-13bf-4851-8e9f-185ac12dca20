// Package providers contains service providers for the application
package providers

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"strings"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

const (
	// DefaultTimeout is the default timeout for HTTP requests
	DefaultTimeout = 30 * time.Second

	// DefaultBaseURL is the default base URL for the API
	DefaultBaseURL = "https://etims-api.kra.go.ke/etims-api"

	// ContentTypeJSON is the Content-Type header value for JSON
	ContentTypeJSON = "application/json"
)

// Common API error codes
const (
	ErrCodeBadRequest           = "891"
	ErrCodeHeaderCreation       = "892"
	ErrCodeBodyCreation         = "893"
	ErrCodeConnectionRefused    = "894"
	ErrCodeUnknown              = "895"
	ErrCodeBadResponseStatus    = "896"
	ErrCodeResponseParsing      = "897"
	ErrCodeInvalidConfiguration = "898"
	ErrCodeGeneral              = "899"
)

// ApiError represents an API error
type ApiError struct {
	Code    string
	Message string
	Err     error
}

// Error implements the error interface
func (e *ApiError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("API error %s: %s - %v", e.Code, e.Message, e.Err)
	}
	return fmt.Sprintf("API error %s: %s", e.Code, e.Message)
}

// Unwrap returns the underlying error
func (e *ApiError) Unwrap() error {
	return e.Err
}

// DeviceManager defines the interface for getting device information
// type DeviceManager interface {
// 	GetKey(keyType string, tinBhfPath string) (string, error)
// }

// Config defines the configuration for the API client
type Config struct {
	// BaseURL is the base URL for the API
	BaseURL string

	// Timeout is the timeout for HTTP requests
	Timeout time.Duration

	// DeviceManager is used to get device information
	DeviceManager DeviceManager

	// DataHistoryManager is used to record data history
	DataHistoryManager interface{} // Replace with actual interface

	// LogLevel sets the logging level for the API client
	LogLevel zerolog.Level
}

// ApiClientArg represents arguments for an API request
type ApiClientArg struct {
	// FolderPath represents the folder path components of the URI
	FolderPath []string

	// URI is the URI for the request
	URI string

	// ReqBodyClass represents the type of the request body
	ReqBodyClass interface{}
}

// InitInfoReq represents a request for initialization info
type InitInfoReq struct {
	Tin      string `json:"tin"`
	BhfId    string `json:"bhfId"`
	DvcSrlNo string `json:"dvcSrlNo"`
}

// ApiClient is a client for making API requests
type ApiClient struct {
	config Config
	client *http.Client
	logger zerolog.Logger
}

// NewApiClient creates a new API client with the given configuration
func NewApiClient(config Config) (*ApiClient, error) {
	// Validate configuration
	if config.BaseURL == "" {
		config.BaseURL = DefaultBaseURL
	}

	if config.Timeout == 0 {
		config.Timeout = DefaultTimeout
	}

	// if config.DeviceManager == nil {
	// 	return nil, &ApiError{
	// 		Code:    ErrCodeInvalidConfiguration,
	// 		Message: "DeviceManager is required",
	// 	}
	// }

	// Create HTTP client with proper timeout
	client := &http.Client{
		Timeout: config.Timeout,
	}

	// Create logger
	logger := log.With().Str("component", "ApiClient").Logger().Level(config.LogLevel)

	return &ApiClient{
		config: config,
		client: client,
		logger: logger,
	}, nil
}

// getRequestURL constructs the full request URL
func (a *ApiClient) getRequestURL(requestURI string) (string, error) {
	// Ensure the base URL doesn't end with a slash
	baseURL := strings.TrimSuffix(a.config.BaseURL, "/")

	// Ensure the request URI doesn't start with a slash
	uri := strings.TrimPrefix(requestURI, "/")

	// Combine the base URL and request URI
	fullURL := fmt.Sprintf("%s/%s", baseURL, uri)

	// Validate the URL
	_, err := url.Parse(fullURL)
	if err != nil {
		return "", &ApiError{
			Code:    ErrCodeBadRequest,
			Message: "Invalid request URL",
			Err:     err,
		}
	}

	return fullURL, nil
}

// GetClient makes a POST request to the API
func (a *ApiClient) GetClient(ctx context.Context, arg ApiClientArg, reqObj interface{}) (string, error) {
	return a.getClientWithHeaders(ctx, arg, reqObj, true)
}

// getClientWithHeaders makes a POST request to the API with optional headers
func (a *ApiClient) getClientWithHeaders(ctx context.Context, arg ApiClientArg, reqObj interface{}, includeHeaders bool) (string, error) {
	// Check context
	if ctx == nil {
		ctx = context.Background()
	}

	// Create request URL
	reqURL, err := a.getRequestURL(arg.URI)
	if err != nil {
		return "", err
	}

	a.logger.Debug().Str("url", reqURL).Msg("Making request")

	// Marshal request object to JSON
	jsonData, err := json.Marshal(reqObj)
	if err != nil {
		return "", &ApiError{
			Code:    ErrCodeBodyCreation,
			Message: "Failed to marshal request body",
			Err:     err,
		}
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, reqURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", &ApiError{
			Code:    ErrCodeBadRequest,
			Message: "Failed to create request",
			Err:     err,
		}
	}

	// Set content type
	req.Header.Set("Content-Type", ContentTypeJSON)

	// Extract TIN and BHF ID from request object for headers if needed
	if includeHeaders {
		err = a.addHeadersFromRequestObject(req, reqObj)
		if err != nil {
			return "", err
		}
	}

	// Special handling for initialization request
	if reqURL == "https://etims-api.kra.go.ke/etims-api/selectInitVsdcInfo" {
		initReq, err := a.extractInitInfoFields(reqObj)
		if err != nil {
			return "", err
		}

		// Replace the request body with the initialization request
		jsonData, err = json.Marshal(initReq)
		if err != nil {
			return "", &ApiError{
				Code:    ErrCodeBodyCreation,
				Message: "Failed to marshal init request body",
				Err:     err,
			}
		}
		req, err = http.NewRequestWithContext(ctx, http.MethodPost, reqURL, bytes.NewBuffer(jsonData))
		if err != nil {
			return "", &ApiError{
				Code:    ErrCodeBadRequest,
				Message: "Failed to create init request",
				Err:     err,
			}
		}
		req.Header.Set("Content-Type", ContentTypeJSON)
	}

	// Make the request
	resp, err := a.client.Do(req)
	if err != nil {
		return "", &ApiError{
			Code:    ErrCodeConnectionRefused,
			Message: "Connection to KRA API refused. Check connection",
			Err:     err,
		}
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return "", &ApiError{
			Code:    ErrCodeBadResponseStatus,
			Message: fmt.Sprintf("Request failed with status code: %d", resp.StatusCode),
		}
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", &ApiError{
			Code:    ErrCodeResponseParsing,
			Message: "Failed to read response body",
			Err:     err,
		}
	}

	return string(body), nil
}

// addHeadersFromRequestObject extracts TIN and BHF ID from request object for headers
func (a *ApiClient) addHeadersFromRequestObject(req *http.Request, reqObj interface{}) error {
	// Use reflection to extract fields
	val := reflect.ValueOf(reqObj)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return &ApiError{
			Code:    ErrCodeHeaderCreation,
			Message: "Request object must be a struct",
		}
	}

	// Extract TIN and BHF ID
	tinBhfPath := strings.Builder{}
	foundTin := false
	foundBhfId := false

	for i := 0; i < val.NumField(); i++ {
		fieldName := val.Type().Field(i).Name
		fieldLower := strings.ToLower(fieldName)

		if fieldLower == "tin" || fieldLower == "bhfid" {
			fieldValue := fmt.Sprintf("%v", val.Field(i).Interface())

			// Add to headers
			req.Header.Set(fieldName, fieldValue)

			// Build TIN_BHF path
			if fieldLower == "tin" {
				tinBhfPath.WriteString(fieldValue)
				tinBhfPath.WriteString("_")
				foundTin = true
			} else {
				tinBhfPath.WriteString(fieldValue)
				foundBhfId = true
			}
		}
	}

	// Only proceed if we found both TIN and BHF ID
	if !foundTin || !foundBhfId {
		return &ApiError{
			Code:    ErrCodeHeaderCreation,
			Message: "Request object must contain 'tin' and 'bhfId' fields",
		}
	}

	// Get CMC key
	cmcKey, err := a.config.DeviceManager.GetKey("cmcKey", tinBhfPath.String())
	if err != nil {
		return &ApiError{
			Code:    ErrCodeHeaderCreation,
			Message: "Failed to get CMC key",
			Err:     err,
		}
	}

	// Add CMC key to headers
	req.Header.Set("cmcKey", cmcKey)

	return nil
}

// extractInitInfoFields extracts fields for an initialization request
func (a *ApiClient) extractInitInfoFields(reqObj interface{}) (InitInfoReq, error) {
	// Initialize result
	result := InitInfoReq{}

	// Use reflection to extract fields
	val := reflect.ValueOf(reqObj)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return result, &ApiError{
			Code:    ErrCodeBodyCreation,
			Message: "Request object must be a struct",
		}
	}

	// Extract fields
	for i := 0; i < val.NumField(); i++ {
		fieldName := val.Type().Field(i).Name
		fieldLower := strings.ToLower(fieldName)
		fieldValue := fmt.Sprintf("%v", val.Field(i).Interface())

		switch fieldLower {
		case "tin":
			result.Tin = fieldValue
		case "bhfid":
			result.BhfId = fieldValue
		case "dvcsrlno":
			result.DvcSrlNo = fieldValue
		}
	}

	return result, nil
}

// GetClientWithGet makes a GET request to the API
func (a *ApiClient) GetClientWithGet(ctx context.Context, requestURI string, reqBody interface{}) (string, error) {
	// Check context
	if ctx == nil {
		ctx = context.Background()
	}

	// Create request URL
	reqURL, err := a.getRequestURL(requestURI)
	if err != nil {
		return "", err
	}

	a.logger.Debug().Str("url", reqURL).Msg("Making GET request")

	// Create query parameters if request body is provided
	if reqBody != nil {
		params := url.Values{}

		// Use reflection to extract fields
		val := reflect.ValueOf(reqBody)
		if val.Kind() == reflect.Ptr {
			val = val.Elem()
		}

		if val.Kind() != reflect.Struct {
			return "", &ApiError{
				Code:    ErrCodeBodyCreation,
				Message: "Request body must be a struct",
			}
		}

		// Extract fields
		for i := 0; i < val.NumField(); i++ {
			field := val.Type().Field(i)
			fieldValue := val.Field(i)

			// Skip nil fields
			if fieldValue.IsZero() {
				continue
			}

			// Add to query parameters
			params.Add(field.Name, fmt.Sprintf("%v", fieldValue.Interface()))
		}

		// Add query parameters to URL
		if len(params) > 0 {
			reqURL = fmt.Sprintf("%s?%s", reqURL, params.Encode())
		}
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, reqURL, nil)
	if err != nil {
		return "", &ApiError{
			Code:    ErrCodeBadRequest,
			Message: "Failed to create GET request",
			Err:     err,
		}
	}

	// Set content type
	req.Header.Set("Content-Type", ContentTypeJSON)

	// Make the request
	resp, err := a.client.Do(req)
	if err != nil {
		return "", &ApiError{
			Code:    ErrCodeConnectionRefused,
			Message: "Connection to API refused. Check connection",
			Err:     err,
		}
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return "", &ApiError{
			Code:    ErrCodeBadResponseStatus,
			Message: fmt.Sprintf("GET request failed with status code: %d", resp.StatusCode),
		}
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", &ApiError{
			Code:    ErrCodeResponseParsing,
			Message: "Failed to read GET response body",
			Err:     err,
		}
	}

	return string(body), nil
}

// ParseResponse parses the response body into the given struct
func ParseResponse(responseBody string, responseObj interface{}) error {
	if responseBody == "" {
		return errors.New("empty response body")
	}

	err := json.Unmarshal([]byte(responseBody), responseObj)
	if err != nil {
		return &ApiError{
			Code:    ErrCodeResponseParsing,
			Message: "Failed to parse response body",
			Err:     err,
		}
	}

	return nil
}
