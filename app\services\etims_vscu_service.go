package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"struts-pos-backend/app/apperr"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/providers"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/ctxhelper"
	"time"

	"github.com/google/uuid"
)

type EtimsVSCUService interface {
	FetchNoticesFromKRA(ctx context.Context, operations txns_db.TransactionsSQLOperations) error
	GetInvoice(ctx context.Context, operations txns_db.TransactionsSQLOperations) (*entities.Invoice, error)
	SignInvoice(ctx context.Context, transactionsDB txns_db.TransactionsDB, form *forms.ETIMSInvoiceForm) (*entities.InvoiceSigningResult, error)
}

type AppEtimsVSCUService struct {
	etimsProductionAPIURL string
	etimsSandboxAPIURL    string
	etimsVSCU             providers.EtimsVSCU
	// etimsVscuCoreWrapProvider providers.TrnsSalesExecute
	dashboardAnalyticsService DashboardAnalyticsService
	etimsItemRepository       repos.EtimsItemRepository
	etimsNoticeRepository     repos.EtimsNoticeRepository
	invoiceItemRepository     repos.InvoiceItemRepository
	invoiceRepository         repos.InvoiceRepository
	qrCodeFolder              string
	storeRepository           repos.StoreRepository
}

func NewEtimsVSCUService(
	etimsProductionAPIURL string,
	etimsSandboxAPIURL string,
	etimsVSCU providers.EtimsVSCU,
	// etimsVscuCoreWrapProvider providers.TrnsSalesExecute,
	dashboardAnalyticsService DashboardAnalyticsService,
	etimsItemRepository repos.EtimsItemRepository,
	etimsNoticeRepository repos.EtimsNoticeRepository,
	invoiceItemRepository repos.InvoiceItemRepository,
	invoiceRepository repos.InvoiceRepository,
	qrCodeFolder string,
	storeRepository repos.StoreRepository,
) EtimsVSCUService {
	return &AppEtimsVSCUService{
		etimsProductionAPIURL: etimsProductionAPIURL,
		etimsSandboxAPIURL:    etimsSandboxAPIURL,
		etimsVSCU:             etimsVSCU,
		// etimsVscuCoreWrapProvider: etimsVscuCoreWrapProvider,
		dashboardAnalyticsService: dashboardAnalyticsService,
		etimsItemRepository:       etimsItemRepository,
		etimsNoticeRepository:     etimsNoticeRepository,
		invoiceItemRepository:     invoiceItemRepository,
		invoiceRepository:         invoiceRepository,
		qrCodeFolder:              qrCodeFolder,
		storeRepository:           storeRepository,
	}
}

func (s *AppEtimsVSCUService) FetchNoticesFromKRA(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
) error {

	// Fetch from KRA
	noticesResponse, err := s.etimsVSCU.SelectNotices()
	if err != nil {
		utils.Log.Infof("failed to retrieve notices from KRA, err=[%v]\n", err)
	}

	noticesCount := len(noticesResponse.Data.NoticeList)
	utils.Log.Infof("noticesResponse.ResultCd=[%v], noticesCount=[%v]\n", noticesResponse.ResultCd, noticesCount)

	// Todo: Save to db
	etimsNotices := make([]*entities.EtimsNotice, 0)

	if noticesCount < 1 {
		utils.Log.Infof("etims notices returned empty\n")
		return nil
	}

	for _, notice := range noticesResponse.Data.NoticeList {
		etimsNotice := &entities.EtimsNotice{
			Content:              notice.Cont,
			DetailURL:            notice.DtlURL,
			NoticeNumber:         int64(notice.NoticeNo),
			RegistrationDateTime: notice.RegDt,
			RegistrationName:     notice.RegrNm,
			Title:                notice.Title,
		}

		etimsNotices = append(etimsNotices, etimsNotice)
	}

	err = s.etimsNoticeRepository.SaveMultiple(ctx, operations, etimsNotices)
	if err != nil {
		utils.Log.Infof("unable to save E-TIMS notices, err=[%v]\n", err)
	}

	return nil
}

func (s *AppEtimsVSCUService) GetInvoice(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
) (*entities.Invoice, error) {

	// Validate API Key

	invoice := &entities.Invoice{}
	return invoice, nil
}

func (s *AppEtimsVSCUService) SignInvoice(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
	form *forms.ETIMSInvoiceForm,
) (*entities.InvoiceSigningResult, error) {

	signingResult := &entities.InvoiceSigningResult{}
	tokenInfo := ctxhelper.TokenInfo(ctx)
	invoiceNumber := fmt.Sprintf("%v", form.InvoiceNumber)

	store, err := s.storeRepository.FindByID(ctx, tokenInfo.StoreID)
	if err != nil {
		return signingResult, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find configured store for API Key=[%v]",
			tokenInfo.APIKeyID,
		)
	}

	if !store.IsLicensed {
		utils.Log.Infof("failed to sign invoice for unlicensed store=[%v]\n", store.Name)
		return nil, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("failed to sign invoice for unlicensed store"),
			utils.ErrorCodeUnauthorized,
			"failed to sign invoice for unlicensed store",
			"cannot sign invoice=[%v] for unlicensed store",
			form.InvoiceNumber,
		)
	}

	// Validate customer PIN
	customerPIN := strings.TrimSpace(form.CustomerPIN)
	if customerPIN != "" {
		if len(customerPIN) != 11 {
			customerPIN = ""

			return nil, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("failed to sign invoice with invalid customer PIN"),
				utils.ErrorCodeInvalidArgument,
				"failed to sign invoice with invalid customer PIN",
				"cannot sign invoice=[%v] with invalid customer PIN",
				form.CustomerPIN,
			)
		}
	}

	invoice := &entities.Invoice{
		CustomerName:          form.CustomerName,
		CustomerTIN:           customerPIN,
		DocumentTypeCode:      utils.String("S"), // S for Sale, R for Credit Note
		GrandTotal:            form.Total,
		InvoiceNumber:         invoiceNumber,
		OriginalInvoiceNumber: invoiceNumber,
		StoreID:               &store.ID,
		Vat:                   form.Vat,
	}

	existingInvoice, err := s.invoiceRepository.GetInvoiceByInvoiceNumber(ctx, form.InvoiceNumber, store.ID)
	if err != nil && !utils.IsErrNoRows(err) {
		utils.Log.Infof("failed to find existing invoice by invoiceNumber=[%v], err=[%v]", invoiceNumber, err)
		return signingResult, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find existing invoice by invoiceNumber=[%v]",
			invoiceNumber,
		)
	}

	if err == nil && existingInvoice.ID > 0 && existingInvoice.EtimsReceiptSignature != nil {
		signingResult.InvoiceNumber = existingInvoice.InvoiceNumber
		invoice = existingInvoice
		invoice.StoreID = &store.ID

		signingResult.ETIMSSDCID = invoice.EtimsSDCID
		signingResult.ETIMSInternalData = invoice.EtimsInternalData
		signingResult.EtimsMrcNumber = invoice.EtimsMrcNumber
		signingResult.ETIMSReceiptSignature = invoice.EtimsReceiptSignature
		signingResult.EtimsResultDateTime = invoice.EtimsResultDateTime
		signingResult.EtimsTotalReceiptNumber = invoice.EtimsTotalReceiptNumber
		signingResult.EtimsVSCUReceiptPublicationDate = invoice.EtimsVSCUReceiptPublicationDate
		signingResult.InvoiceNumber = invoiceNumber
		signingResult.VerificationURL = invoice.VerificationURL

		return signingResult, nil
	}

	if len(form.InvoiceItems) == 0 {
		utils.Log.Infof("failed to sign invoice for empty item list=[%v]", form.InvoiceNumber)
		return nil, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("failed to sign invoice for empty item list"),
			utils.ErrorCodeInvalidArgument,
			"failed to sign invoice for empty item list",
			"cannot sign invoice=[%v] for empty item list",
			form.InvoiceNumber,
		)
	}

	for i, formInvoiceItem := range form.InvoiceItems {
		err = s.loadItem(
			invoice,
			i+1, // Populate ItemID from automated items count
			formInvoiceItem,
		)

		if err != nil {
			return signingResult, err
		}
	}

	if invoice.HasVAT && invoice.Vat == 0.00 {
		return signingResult, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("invalid tax amount"),
			utils.ErrorCodeResourceNotFound,
			"tax amount not found for invoice with Vatable 16% item=[%v]",
			invoice.ItemThatHasVAT,
		)
	}

	// Save data to DB
	err = s.saveInvoice(ctx, transactionsDB, invoice)
	if err != nil {
		return signingResult, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to save invoice invoiceNumber=[%v]",
			invoiceNumber,
		)
	}

	// Production signing
	utils.Log.Infof("signInvoiceViaKRAETIMS for store=[%v], id=[%v]", store.Name, store.ID)
	// Sign invoice via KRA E-Tims
	err = s.signInvoiceViaKRAETIMS(ctx, transactionsDB, store, invoice)
	if err != nil {
		utils.Log.Infof("unable to sign invoice via KRA ETIMS, err=[%v]", err)
	}

	signingResult.ETIMSSDCID = invoice.EtimsSDCID
	signingResult.ETIMSInternalData = invoice.EtimsInternalData
	signingResult.EtimsMrcNumber = invoice.EtimsMrcNumber
	signingResult.ETIMSReceiptSignature = invoice.EtimsReceiptSignature
	signingResult.EtimsResultDateTime = invoice.EtimsResultDateTime
	signingResult.EtimsTotalReceiptNumber = invoice.EtimsTotalReceiptNumber
	signingResult.EtimsVSCUReceiptPublicationDate = invoice.EtimsVSCUReceiptPublicationDate
	signingResult.InvoiceNumber = invoiceNumber
	signingResult.VerificationURL = invoice.VerificationURL

	// Increment dashboard analytics invoice count
	// Wrap in go routine
	go func() {
		err = s.dashboardAnalyticsService.IncrementInvoiceCount(ctx, store.ID)
		if err != nil {
			utils.Log.Infof("failed to increment invoice count for store=[%v]", store.Name)
		}
	}()

	return signingResult, nil
}

func (s *AppEtimsVSCUService) loadItem(
	invoice *entities.Invoice,
	itemID int,
	form forms.ETIMSInvoiceItemForm,
) error {

	if !entities.EtimsTaxType(form.TaxRate).IsValid() {
		return utils.NewErrorWithErrorCodeAndMessage(
			errors.New("invalid tax rate"),
			utils.ErrorCodeResourceNotFound,
			"invalid tax rate",
			"taxRate=[%v] not found",
			form.TaxRate,
		)
	}

	itemName := fmt.Sprintf("%v", form.ItemName)

	if form.TaxRate == entities.EtimsTaxTypeB.String() {
		invoice.HasVAT = true
		invoice.ItemThatHasVAT = itemName
	}

	var etimsItemCode string
	formEtimsItemCode := strings.TrimSpace(form.EtimsItemCode)
	if formEtimsItemCode != "" && len(formEtimsItemCode) >= 8 {
		etimsItemCode = formEtimsItemCode
	} else {
		etimsItemCode = utils.GenerateKRAItemCode(itemName, 20)
	}

	itemUUID := uuid.New().String()

	qty := form.ItemQty
	if qty == 0 {
		qty = 1
	}

	// Load ETIMS Item
	etimsItem := &entities.EtimsItem{
		AddInfo:     utils.GenerateItemCode(itemName, 7),
		DcAmt:       utils.RoundFloat64(form.DiscountAmount, 2),
		DcRt:        form.DiscountRate,
		ItemCd:      etimsItemCode,
		ItemClsCd:   utils.GenerateItemCode(itemName, 5),
		ItemNm:      itemName,
		ItemTyCd:    entities.EtimsProductTypeService.String(),
		ItemStdNm:   itemName,
		BhfID:       "00",
		DftPrc:      utils.RoundFloat64(form.UnitPrice, 2),
		IsrcAplcbYn: "N",
		ModrID:      "Admin",
		ModrNm:      "Admin",
		OrgnNatCd:   "KE",
		PkgUnitCd:   "BG",
		QtyUnitCd:   "U",
		RegrID:      "Admin",
		RegrNm:      "Admin",
		SftyQty:     qty,
		SplyAmt:     utils.RoundFloat64(form.TotalAmt, 2),
		TaxTyCd:     form.TaxRate,
		Tin:         invoice.CustomerTIN,
		UseYn:       "Y",
		UUID:        itemUUID,
	}
	etimsItem.Timestamps.Touch()

	invoice.EtimsItems = append(invoice.EtimsItems, etimsItem)

	// Load item
	item := &entities.InvoiceItem{
		Amount:         form.UnitPrice,
		DiscountAmount: form.DiscountAmount,
		DiscountRate:   form.DiscountRate,
		ItemID:         itemID,
		ItemName:       itemName,
		Name:           itemName,
		Quantity:       qty,
		Rate:           form.TaxRate,
		SerialNumber:   fmt.Sprintf("%v", itemID),
		Total:          form.TotalAmt,
		UUID:           itemUUID,
	}
	item.Timestamps.Touch()

	invoice.InvoiceItems = append(invoice.InvoiceItems, item)

	return nil
}

func (s *AppEtimsVSCUService) saveInvoice(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
	invoice *entities.Invoice,
) error {

	err := transactionsDB.InTransaction(ctx, func(ctx context.Context, operations txns_db.TransactionsSQLOperations) error {

		err := s.invoiceRepository.Save(ctx, operations, invoice)
		if err != nil {
			return err
		}

		// save invoice items
		for _, item := range invoice.InvoiceItems {
			// Attach invoice id to invoice item, the rest of the details are already in place
			item.InvoiceID = invoice.ID
		}

		err = s.invoiceItemRepository.SaveMultiple(ctx, operations, invoice.InvoiceItems)
		if err != nil {
			return err
		}

		// Save ETIMS Items
		err = s.etimsItemRepository.SaveMultiple(ctx, operations, invoice.EtimsItems)
		if err != nil {
			return err
		}

		return err
	})

	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to save invoice with invoiceNumber=[%v]",
			invoice.InvoiceNumber,
		)
	}

	return nil
}

func (s *AppEtimsVSCUService) signInvoiceViaKRAETIMS(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	store *entities.Store,
	invoice *entities.Invoice,
) error {

	utils.Log.Infof("Signing invoice=[%v] via etims...", invoice.InvoiceNumber)

	// Load the Africa/Nairobi timezone location
	location, err := time.LoadLocation("Africa/Nairobi")
	if err != nil {
		utils.Log.Infof("Error loading location, err=[%v]", err)
		return err
	}

	// Get the current time in the Nairobi timezone
	timeNow := time.Now().In(location)
	commonDate := utils.FormatTime(timeNow, "20060102030405") // yyyyMMddhhmmss

	itemsCount := len(invoice.InvoiceItems)

	customerName := invoice.CustomerName
	if len(customerName) >= 20 {
		customerName = customerName[:19]
	}

	storeName := store.Name
	if len(storeName) >= 20 {
		storeName = storeName[:19]
	}

	trimmedTraderName := strings.ReplaceAll(store.Name, " ", "")
	if len(trimmedTraderName) >= 20 {
		trimmedTraderName = trimmedTraderName[:19]
	}

	receiptNumber := utils.ConvertStringToInt64(invoice.InvoiceNumber)

	etimsReceiptForm := &forms.EtimsReceiptForm{
		Adrs:         "Nairobi,Kenya",
		BtmMsg:       store.Name,
		CustMblNo:    invoice.CustomerPhone,
		CustTin:      invoice.CustomerTIN,
		PrchrAcptcYn: "Y",
		RptNo:        receiptNumber,
		TopMsg:       storeName,
		TrdeNm:       customerName,
	}

	var etimsItems []*forms.EtimsItemForm
	itemSeqCounter := 1

	for _, etimsItem := range invoice.EtimsItems {

		var vatFloat64 float64

		if etimsItem.TaxTyCd == entities.EtimsTaxTypeB.String() {
			vat := etimsItem.DftPrc * 0.1379 * etimsItem.SftyQty
			vatStr := fmt.Sprintf("%.2f", vat)
			vatFloat64 = utils.ConvertStringToFloat64(vatStr)
		}

		taxableAmount := etimsItem.DftPrc * etimsItem.SftyQty
		taxableAmountStr := fmt.Sprintf("%.2f", taxableAmount)
		taxableAmountFloat64 := utils.ConvertStringToFloat64(taxableAmountStr)

		taxableAmountFloat64 = taxableAmountFloat64 - vatFloat64
		totalItemAmountFloat64 := taxableAmountFloat64

		etimsItem := &forms.EtimsItemForm{
			DcAmt:     etimsItem.DcAmt,
			DcRt:      etimsItem.DcRt,
			ItemSeq:   itemSeqCounter,
			ItemCd:    etimsItem.ItemCd,
			ItemClsCd: etimsItem.ItemClsCd,
			ItemNm:    etimsItem.ItemNm,
			Bcd:       etimsItem.Bcd,
			PkgUnitCd: etimsItem.PkgUnitCd,
			Pkg:       etimsItem.SftyQty,
			QtyUnitCd: etimsItem.QtyUnitCd,
			Qty:       etimsItem.SftyQty,
			Prc:       etimsItem.DftPrc,
			SplyAmt:   utils.RoundFloat64(taxableAmount, 2),
			IsrccCd:   "N",
			TaxTyCd:   etimsItem.TaxTyCd,
			TaxblAmt:  taxableAmountFloat64,
			TaxAmt:    vatFloat64,
			TotAmt:    totalItemAmountFloat64,
		}

		etimsItems = append(etimsItems, etimsItem)
		itemSeqCounter++
	}

	// invoiceNumber := invoice.InvoiceNumber
	invoiceNumber := utils.ConvertStringToInt64(invoice.InvoiceNumber)

	totalTaxableAmount := invoice.GrandTotal - invoice.Vat
	totalTaxableAmountStr := fmt.Sprintf("%.2f", totalTaxableAmount)
	totalTaxableAmountFloat64 := utils.ConvertStringToFloat64(totalTaxableAmountStr)

	totalTaxableAmountFloat64 = utils.RoundFloat64(totalTaxableAmountFloat64, 2)
	totalVat := utils.RoundFloat64(invoice.Vat, 2)

	taxAmountB := utils.RoundFloat64(invoice.GrandTotal, 2)

	// TODO: Clarify on changes with KRA on Total Taxable Amount B
	// var taxAmountB float64
	// if totalVat > 0 {
	// grandTotal := utils.RoundFloat64(invoice.GrandTotal, 2)

	// taxableAmount := grandTotal - totalVat
	// taxAmountB = utils.RoundFloat64(taxableAmount, 2)
	// }

	// Construct etims sale form
	form := &forms.CreateEtimsSalesForm{
		Tin:          store.PIN,
		BhfID:        store.EtimsBranch,
		TrdInvcNo:    invoiceNumber,
		InvcNo:       invoiceNumber,
		OrgInvcNo:    invoiceNumber,
		CustTin:      invoice.CustomerTIN,
		CustNm:       invoice.CustomerName,
		SalesTyCd:    "N",
		RcptTyCd:     "S", // S for Sale, R for Credit Note
		PmtTyCd:      entities.ETIMSPaymentMethodCash.String(),
		SalesSttsCd:  entities.TransactionProgressApproved.String(),
		CfmDt:        commonDate,
		SalesDt:      utils.FormatTime(timeNow, "20060102"), // yyyyMMdd
		StockRlsDt:   commonDate,
		TotItemCnt:   itemsCount,
		TaxblAmtA:    0.00,
		TaxblAmtB:    taxAmountB,
		TaxblAmtC:    0.00,
		TaxblAmtD:    0.00,
		TaxblAmtE:    0.00,
		TaxRtA:       0.00,
		TaxRtB:       16.00,
		TaxRtC:       0.00,
		TaxRtD:       0.00,
		TaxRtE:       8.00,
		TaxAmtA:      invoice.VatA,
		TaxAmtB:      totalVat,
		TaxAmtC:      invoice.VatC,
		TaxAmtD:      0.00,
		TaxAmtE:      invoice.VatE,
		TotTaxAmt:    totalVat,
		TotTaxblAmt:  totalTaxableAmountFloat64,
		TotAmt:       invoice.GrandTotal,
		TrdeNm:       trimmedTraderName,
		PrchrAcptcYn: "N",
		Remark:       fmt.Sprintf("Sale invoice number %v", invoice.InvoiceNumber),
		RegrID:       "11999",
		RegrNm:       trimmedTraderName,
		ModrID:       "45678",
		ModrNm:       trimmedTraderName,
		Receipt:      etimsReceiptForm,
		ItemList:     etimsItems,
	}

	if invoice.IsCreditNote {
		form.RcptTyCd = "R" // S for Sale, R for Credit Note
	}

	// For debugging purposes only
	payloadBytes, err := json.Marshal(form)
	if err != nil {
		utils.Log.Infof("failed to marshal VFMS sales form payload, err=[%v]", err)
		return err
	}
	utils.PrettyPrintJSON(payloadBytes)

	// Send to etims vscu provider
	response, err := s.etimsVSCU.SaveSale(form)
	if err != nil {
		utils.Log.Infof("failed to post sale to etims, err=[%v]", err)
		return err
	}

	utils.Log.Infof("etims save sale response code=[%v]", response.ResultCd)
	// utils.PrintStructToConsole(response)

	if response.Status == 400 {
		utils.Log.Infof("failed to post sale to etims, err=[%v]", err)
		return err
	}

	if response.ResultCd == "000" {
		// Update signature and verification url only on successful KRA response
		invoice.EtimsInternalData = &response.Data.IntrlData
		invoice.EtimsReceiptSignature = &response.Data.RcptSign
		invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
		invoice.EtimsTotalReceiptNumber = &response.Data.RcptNo
		invoice.EtimsVSCUReceiptPublicationDate = &response.Data.VsdcRcptPbctDate

		// signature := fmt.Sprintf("%v/%v %v %v", response.Data.SdcID, response.Data.RcptNo, response.Data.RcptSign, response.Data.VsdcRcptPbctDate)
		invoice.Signature = response.Data.RcptSign
		invoice.SignedDate = utils.FormatTime(time.Now(), "20060102150405")

	} else if response.ResultCd == "924" && invoice.Signature == "" && invoice.VerificationURL == "" {
		// Update signature and verification url only on successful KRA response
		invoice.EtimsInternalData = &response.Data.IntrlData
		invoice.EtimsMrcNumber = &response.Data.MrcNo
		invoice.EtimsReceiptSignature = &response.Data.RcptSign
		invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
		invoice.EtimsTotalReceiptNumber = &response.Data.RcptNo
		invoice.EtimsVSCUReceiptPublicationDate = &response.Data.VsdcRcptPbctDate

	} else {
		return err
	}

	if response.Data != nil {
		invoice.EtimsInternalData = &response.Data.IntrlData
		invoice.EtimsMrcNumber = &response.Data.MrcNo
		invoice.EtimsReceiptNumber = &response.Data.RcptNo
		invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
		invoice.EtimsResultCode = &response.ResultCd
		invoice.EtimsResultMessage = &response.ResultMsg
		invoice.EtimsSDCID = &response.Data.SdcID
	}

	utils.Log.Infof("invoice.Signature=[%v]", invoice.Signature)

	if invoice.Signature != "" {
		// https://etims-sbx.kra.go.ke/common/link/etims/receipt/indexEtimsReceiptData?Data=P000000010E00EIW354USAF6HTG5Q
		// Data=P000000010E (PIN) 00 (Branch ID) EIW354USAF6HTG5Q (signature from vscu)

		// To check if client is live - if live use prod, otherwise use sandbox
		apiBaseURL := s.etimsSandboxAPIURL

		if store.EtimsEnvironment == "production" {
			apiBaseURL = s.etimsProductionAPIURL
		}

		signatureData := fmt.Sprintf("%v%v%v", store.PIN, store.EtimsBranch, invoice.Signature)
		verificationURL := fmt.Sprintf("%v/common/link/etims/receipt/indexEtimsReceiptData?Data=%v", apiBaseURL, signatureData)

		utils.Log.Infof("verificationURL=[%v]", verificationURL)

		invoice.VerificationURL = verificationURL

		// Generate Signature and QR Code
		// TODO: Pick QR Code URL Folder from settings
		// if invoice.EtimsResultDateTime != nil {
		// 	err = utils.GenerateETIMSVSCUQRCode(ctx, invoice)
		// 	if err != nil {
		// 		utils.Log.Infof("failed to generate QR Code, err=[%v]", err)
		// 		return err
		// 	}
		// }

		// Save response recieved by updating the current invoice
		err = s.invoiceRepository.Save(context.Background(), operations, invoice)
		if err != nil {
			utils.Log.Infof("failed to update signed invoice, err=[%v]", err)
			return err
		}
	}

	return nil
}
