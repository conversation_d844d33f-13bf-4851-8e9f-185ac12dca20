package analytics

import (
	"log"
	"net/http"
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/ctxhelper"

	"github.com/gin-gonic/gin"
)

func getDashboardAnalytics(
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
	storeRepository repos.StoreRepository,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {

			tokenInfo := ctxhelper.TokenInfo(ctx.Request.Context())
			defaultStore, err := storeRepository.GetUserDefaultStore(tokenInfo.UserID)
			if err != nil && utils.IsErrNoRows(err) {
				log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
				ctx.JSON(500, gin.H{
					"message": "unable to retrieve dashboard analytics",
				})
				return
			}

			storeId = defaultStore.ID
		}

		dashboardAnalytics, err := analyticsService.GetDashboardAnalytics(ctx.Request.Context(), transactionsDB, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to retrieve dashboard analytics",
			})
			return
		}

		ctx.JSON(http.StatusOK, dashboardAnalytics)
		return
	}
}
