package invoicetemplates

import (
	"bufio"
	"fmt"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/utils"
	"os"
	"strings"
	"time"
)

func ProcessTemplateOneInvoice(textFile string, invoice *entities.Invoice) error {

	invoice.DateCreated = time.Now()

	file, err := os.Open(textFile)
	utils.CheckError(err, "Error reading signed input file >>> "+textFile)
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Split(bufio.ScanLines)

	var lines []string
	lineNumber := 1
	customerTINNumberLine := 0
	grandTotalNumberLine := 0
	message := ""

	var foundInvoiceNumber bool
	var foundCustomerTIN bool

	for scanner.Scan() {

		invoiceLine := scanner.Text()
		invoiceLine = strings.ToUpper(invoiceLine)
		invoiceLine = strings.TrimSpace(invoiceLine)

		trimedInvoiceLine := strings.Replace(invoiceLine, " ", "", -1)
		lines = append(lines, invoiceLine)
		LogMessage(fmt.Sprintf("[TMPL_1] invoiceLine [%v] %v", utils.ConvertIntToString(lineNumber), invoiceLine))

		if lineNumber > 1 && strings.Contains(trimedInvoiceLine, "INVOICE") && strings.Contains(trimedInvoiceLine, "NUMBER") &&
			!foundInvoiceNumber {

			// ORIGINAL EXPORT INVOICE NUMBER: 5578624901
			lineArr := strings.Split(invoiceLine, " ")
			arrLen := len(lineArr)

			if arrLen >= 3 {
				invoiceNumber := lineArr[arrLen-1]
				invoice.InvoiceNumber = invoiceNumber

				foundInvoiceNumber = true
			}

		} else if lineNumber > 1 && strings.Contains(trimedInvoiceLine, "BILL-TO") && strings.Contains(trimedInvoiceLine, "PARTY") {

			// BILL-TO PARTY : SAID SALIM BAKHRESA & CO LTD NYERERE ROAD DAR ES SALAAM INVOICE DATE: DUE DATE: PAYMENT TERMS: 28.JUL.2023 28.JUL.2023 PAYABLE IMMEDIATELY

			customerName := ParseDataBetween(invoiceLine, invoiceLine, "PARTY", "INVOICE")
			customerName = strings.ReplaceAll(customerName, ":", "")
			customerName = strings.TrimSpace(customerName)
			invoice.CustomerName = customerName

			paymentDates := ParseDataBetween(invoiceLine, invoiceLine, "TERMS", "PAYABLE")
			paymentDates = strings.TrimSpace(paymentDates)

			if len(paymentDates) == 0 {
				paymentDates = ParseDataBetween(invoiceLine, invoiceLine, "TERMS", "DAYS")
			}

			paymentDates = strings.ReplaceAll(paymentDates, ":", "")
			paymentDates = strings.TrimSpace(paymentDates)

			datesArr := strings.Split(paymentDates, " ")
			invoiceDate := datesArr[0]
			// fmt.Printf("paymentDates=[%v], invoiceDate=[%v]\n", paymentDates, invoiceDate)

			invoice.InvoiceDate = invoiceDate

		} else if lineNumber > 1 && strings.Contains(trimedInvoiceLine, "CUSTOMER") && strings.Contains(trimedInvoiceLine, "NO") &&
			!foundCustomerTIN {

			// CUSTOMER NO : TZ043907 CUSTOMER TAX NO.: ***********
			lineArr := strings.Split(invoiceLine, " ")
			arrLen := len(lineArr)

			if arrLen >= 3 {
				customerTIN := lineArr[arrLen-1]
				customerTIN = strings.ReplaceAll(customerTIN, ":", "")
				invoice.CustomerTIN = customerTIN

				foundCustomerTIN = true
			}

			customerNumber := ParseDataBetween(invoiceLine, invoiceLine, ":", "CUSTOMER")
			invoice.CustomerNumber = customerNumber

		} else if strings.Contains(trimedInvoiceLine, "VERIFY.TRA.GO.TZ") {
			//  HTTPS://VERIFY.TRA.GO.TZ/D984BE14070_175949
			invoice.VerificationURL = invoiceLine

			// 211DDB6337E1F1FF608259DFF1638AE972482656/0611231239/03TZ343001664#26086361.23 00_0010_14059
			invoiceSignature := lines[lineNumber-2]

			if strings.Contains(invoiceSignature, "#") {
				invoice.Signature = invoiceSignature

				signArr := strings.Split(invoiceSignature, "#")

				signArr2 := strings.Split(signArr[1], " ")
				invoiceGrandTotalStr := signArr2[0]

				grandTotalFloat := utils.ConvertStringToFloat64(invoiceGrandTotalStr)
				if grandTotalFloat > 0 {
					invoice.GrandTotal = grandTotalFloat

					if invoice.NetAmount == 0 {
						invoice.NetAmount = grandTotalFloat
						invoice.TaxableAmount = grandTotalFloat
						invoice.GrossAmount = grandTotalFloat
					}
				}

			}

			if strings.Contains(invoiceSignature, "/") {
				signArr := strings.Split(invoiceSignature, "/")

				signatureDate, _ := utils.ConvertSignatureDateToString(signArr[1])

				invoice.SignedDate = signatureDate
			}

		} else if strings.Contains(trimedInvoiceLine, "@") {

			// Retrieve customer emails
			lineArr := strings.Split(invoiceLine, " ")
			arrLen := len(lineArr)

			if arrLen > 0 {

				for _, str := range lineArr {

					// validate email
					if utils.IsVaalidEmail(str) {
						invoice.CustomerEmail += strings.TrimSpace(str) + ","
					}

				}
			}

		} else if lineNumber == customerTINNumberLine {

			customerTIN, isCustomerTinValid := SanitizeCustomerTIN(invoiceLine)
			if isCustomerTinValid {
				invoice.CustomerTIN = customerTIN
			}

		} else if strings.Contains(trimedInvoiceLine, "TAX") && strings.Contains(trimedInvoiceLine, "BASE") &&
			strings.Contains(trimedInvoiceLine, "AMOUNT") {

			// TOTAL DUE: TAX BASE AMOUNT = TZS 98053.61 VAT = TZS 17636.28
			message = fmt.Sprintf("[TMPL_1] Setting net and VAT Amounts : [%v] line: [%v]", lineNumber, invoiceLine)
			LogMessage(message)

			lineArr := strings.Split(invoiceLine, " ")
			arrLen := len(lineArr)

			if arrLen >= 6 {
				vatAmtStr := lineArr[arrLen-1]

				vatAmtFloat := utils.ConvertStringToFloat64(vatAmtStr)
				if vatAmtFloat > 0 {
					invoice.Vat = vatAmtFloat
				}

				netAmttStr := lineArr[arrLen-5]

				netAmttFloat := utils.ConvertStringToFloat64(netAmttStr)
				if netAmttFloat > 5 {
					invoice.NetAmount = netAmttFloat
				}
			}

		} else if lineNumber == grandTotalNumberLine {

			message = fmt.Sprintf("[TMPL_1] GrandTotal : [%v] Processing total line[9]: %v", lineNumber, invoiceLine)
			LogMessage(message)

			lineArr := strings.Split(invoiceLine, " ")
			arrLen := len(lineArr)

			// 134,659,980.00 0.00 109,317.60 134,769,297.60

			if arrLen >= 3 {

				netAmount := lineArr[0]
				netAmtFloat := utils.ConvertStringToFloat64(netAmount)
				if netAmtFloat > 0 {
					invoice.NetAmount = netAmtFloat
				}

				vat := lineArr[arrLen-2]
				vatFloat := utils.ConvertStringToFloat64(vat)
				if vatFloat > 0 {
					invoice.Vat = vatFloat
				}

				// grandTotal := lineArr[arrLen-1]
				// grandTotalFloat := utils.ConvertStringToFloat64(grandTotal)
				// if grandTotalFloat > 0 {
				// 	invoice.GrandTotal = grandTotalFloat
				// }
			}
		}

		lineNumber++
	}

	// Remove trailing comma in customer email
	invoice.CustomerEmail = strings.TrimSuffix(invoice.CustomerEmail, ",")

	return nil
}
