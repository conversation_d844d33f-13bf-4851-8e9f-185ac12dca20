package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
)

const (
	countDashboardAnalyticsSQL = `SELECT COUNT(*) AS count FROM dashboard_analytics WHERE deleted_at IS NULL AND store_id=$1`

	createDashboardAnalyticSQL = `INSERT INTO dashboard_analytics (store_id, invoices_count, credit_notes_count, failed_processing_count, 
		stores_count, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`

	deleteDashboardAnalyticByIDSQL = `UPDATE dashboard_analytics SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectDashboardAnalyticByIDAndStoreIDSQL = selectDashboardAnalyticSQL + ` AND id=$1 AND store_id=$2`

	selectDashboardAnalyticByStoreSQL = selectDashboardAnalyticSQL + ` AND store_id=$1`

	filterDashboardAnalyticsSQL = selectDashboardAnalyticSQL + ` AND store_id=$1`

	selectDashboardAnalyticSQL = `SELECT id, store_id, invoices_count, credit_notes_count, failed_processing_count, stores_count, created_at, 
		updated_at FROM dashboard_analytics WHERE deleted_at IS NULL`

	updateDashboardAnalyticSQL = `UPDATE dashboard_analytics SET invoices_count=$1, credit_notes_count=$2, failed_processing_count=$3, 
		stores_count=$4, updated_at=$5 WHERE id=$6`
)

type (
	DashboardAnalyticRepository interface {
		CountDashboardAnalytics(ctx context.Context, storeId int64, filter *entities.PaginationFilter) (int, error)
		Delete(ctx context.Context, DashboardAnalytic *entities.DashboardAnalytic) error
		FilterDashboardAnalytics(ctx context.Context, db txns_db.TransactionsSQLOperations, storeId int64, filter *entities.PaginationFilter) ([]*entities.DashboardAnalytic, error)
		FindByID(ctx context.Context, DashboardAnalyticID int64, storeId int64) (*entities.DashboardAnalytic, error)
		FindByStoreID(ctx context.Context, storeId int64) (*entities.DashboardAnalytic, error)
		Save(ctx context.Context, DashboardAnalytic *entities.DashboardAnalytic) error
	}

	AppDashboardAnalyticRepository struct {
		db *sql.DB
	}
)

func NewDashboardAnalyticRepository(db *sql.DB) DashboardAnalyticRepository {
	return &AppDashboardAnalyticRepository{db: db}
}

func (r *AppDashboardAnalyticRepository) CountDashboardAnalytics(
	ctx context.Context,
	storeId int64,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := countDashboardAnalyticsSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppDashboardAnalyticRepository) Delete(
	ctx context.Context,
	dashboardAnalytic *entities.DashboardAnalytic,
) error {
	_, err := r.db.ExecContext(ctx, deleteDashboardAnalyticByIDSQL, dashboardAnalytic.ID)
	return err
}

func (r *AppDashboardAnalyticRepository) FilterDashboardAnalytics(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeId int64,
	filter *entities.PaginationFilter,
) ([]*entities.DashboardAnalytic, error) {

	dashboardAnalytics := make([]*entities.DashboardAnalytic, 0)

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := filterDashboardAnalyticsSQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return dashboardAnalytics, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoDashboardAnalytic(rows)
		if err != nil {
			return dashboardAnalytics, err
		}

		dashboardAnalytics = append(dashboardAnalytics, invoice)
	}

	return dashboardAnalytics, rows.Err()
}

func (r *AppDashboardAnalyticRepository) FindByID(
	ctx context.Context,
	dashboardAnalyticID int64,
	storeId int64,
) (*entities.DashboardAnalytic, error) {
	row := r.db.QueryRow(selectDashboardAnalyticByIDAndStoreIDSQL, dashboardAnalyticID, storeId)
	return r.scanRowIntoDashboardAnalytic(row)
}

func (r *AppDashboardAnalyticRepository) FindByStoreID(
	ctx context.Context,
	storeId int64,
) (*entities.DashboardAnalytic, error) {
	row := r.db.QueryRow(selectDashboardAnalyticByStoreSQL, storeId)
	return r.scanRowIntoDashboardAnalytic(row)
}

func (r *AppDashboardAnalyticRepository) Save(
	ctx context.Context,
	dashboardAnalytic *entities.DashboardAnalytic,
) error {

	dashboardAnalytic.Timestamps.Touch()
	var err error

	if dashboardAnalytic.IsNew() {

		err = r.db.QueryRow(
			createDashboardAnalyticSQL,
			dashboardAnalytic.StoreID,
			dashboardAnalytic.InvoicesCount,
			dashboardAnalytic.CreditNotesCount,
			dashboardAnalytic.FailedProcessingCount,
			dashboardAnalytic.StoresCount,
			dashboardAnalytic.CreatedAt,
			dashboardAnalytic.UpdatedAt,
		).Scan(&dashboardAnalytic.ID)

	} else {

		_, err = r.db.Exec(
			updateDashboardAnalyticSQL,
			dashboardAnalytic.InvoicesCount,
			dashboardAnalytic.CreditNotesCount,
			dashboardAnalytic.FailedProcessingCount,
			dashboardAnalytic.StoresCount,
			dashboardAnalytic.UpdatedAt,
			dashboardAnalytic.ID,
		)
	}

	if err != nil {
		log.Printf("error saving api_key, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppDashboardAnalyticRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.UserID > 0 {
		query += fmt.Sprintf(" AND user_id = $%d", currentIndex)
		args = append(args, filter.UserID)
		currentIndex += 1
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(api_key) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppDashboardAnalyticRepository) scanRowIntoDashboardAnalytic(
	rowScanner txns_db.RowScanner,
) (*entities.DashboardAnalytic, error) {

	var dashboardAnalytic entities.DashboardAnalytic

	err := rowScanner.Scan(
		&dashboardAnalytic.ID,
		&dashboardAnalytic.StoreID,
		&dashboardAnalytic.InvoicesCount,
		&dashboardAnalytic.CreditNotesCount,
		&dashboardAnalytic.FailedProcessingCount,
		&dashboardAnalytic.StoresCount,
		&dashboardAnalytic.CreatedAt,
		&dashboardAnalytic.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning dashboardAnalytic,  err=[%v]\n", err.Error())
		return &dashboardAnalytic, err
	}

	return &dashboardAnalytic, nil
}
