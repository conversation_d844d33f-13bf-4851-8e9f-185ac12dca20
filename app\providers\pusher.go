package providers

import (
	"context"
	"fmt"
	"os"
	"struts-pos-backend/app/logger"
	"sync"

	"struts-pos-backend/app/entities"

	firebase "firebase.google.com/go"
	"firebase.google.com/go/messaging"
	"google.golang.org/api/option"
)

const batchSize = 500

type (
	Pusher interface {
		SendMulticastNotifications(pushTokens []*entities.PushToken, pushNotification *entities.PushNotification, payloadData map[string]string) error
		SendNotifications(notificationPayload []*entities.PushNotificationPayload) error
	}

	AppPusher struct {
		client *messaging.Client
	}
)

func NewPusher() *AppPusher {

	ctx := context.Background()
	opt := option.WithCredentialsJSON([]byte(os.Getenv("GOOGLE_APPLICATION_CREDENTIALS")))
	app, err := firebase.NewApp(ctx, nil, opt)
	if err != nil {
		logger.Fatalf("Error initializing Firebase app: %v\n", err)
	}

	client, err := app.Messaging(ctx)
	if err != nil {
		logger.Fatalf("Error getting Messaging client: %v\n", err)
	}

	return &AppPusher{client: client}
}

func (s *AppPusher) SendMulticastNotifications(
	pushTokens []*entities.PushToken,
	pushNotification *entities.PushNotification,
	payloadData map[string]string,
) error {

	deviceTokens := make([]string, 0)
	for _, pushToken := range pushTokens {
		deviceTokens = append(deviceTokens, pushToken.DeviceToken)
	}

	broadcastNotification := &messaging.MulticastMessage{
		Data: payloadData,
		Notification: &messaging.Notification{
			Title: pushNotification.Title,
			Body:  pushNotification.Notification,
		},
		Android: &messaging.AndroidConfig{
			Priority: "high",
		},
		Webpush: &messaging.WebpushConfig{
			Notification: &messaging.WebpushNotification{
				Icon: pushNotification.Icon,
			},
			FcmOptions: &messaging.WebpushFcmOptions{
				Link: pushNotification.Link,
			},
		},
		Tokens: deviceTokens,
	}

	brNotification, err := s.client.SendMulticast(context.Background(), broadcastNotification)
	if err != nil {
		return err
	}

	if brNotification.FailureCount > 0 {
		var failedTokens []string
		for index, response := range brNotification.Responses {
			if !response.Success {
				failedTokens = append(failedTokens, deviceTokens[index])
			}
		}

		return fmt.Errorf("the following device tokens caused failures: [%v]", failedTokens)
	}

	return nil
}

func (s *AppPusher) SendNotifications(
	notificationPayload []*entities.PushNotificationPayload,
) error {

	notifications := make([]*messaging.Message, 0)

	for _, payload := range notificationPayload {

		notification := &messaging.Message{
			Data: payload.PayloadData,
			Notification: &messaging.Notification{
				Title: payload.PushNotification.Title,
				Body:  payload.PushNotification.Notification,
			},
			Android: &messaging.AndroidConfig{
				Priority: "high",
			},
			Webpush: &messaging.WebpushConfig{
				Notification: &messaging.WebpushNotification{
					Icon: payload.PushNotification.Icon,
				},
				FcmOptions: &messaging.WebpushFcmOptions{
					Link: payload.PushNotification.Link,
				},
			},
			Token: payload.PushToken.DeviceToken,
		}

		notifications = append(notifications, notification)
	}

	chunkedNotifications := s.chunkNotifications(notifications)

	var wg sync.WaitGroup

	errs := make([]error, 0)

	for _, chunk := range chunkedNotifications {
		wg.Add(1)

		go func(notificationsChunk []*messaging.Message) {
			defer wg.Done()

			responseMessage, err := s.client.SendAll(context.Background(), notificationsChunk)
			if err != nil {
				errs = append(errs, err)
			} else {

				if responseMessage.FailureCount > 0 {
					failedTokens := make([]error, 0)
					for index, response := range responseMessage.Responses {
						if !response.Success {
							err := fmt.Errorf(notifications[index].Token+" - ", response.Error)
							failedTokens = append(failedTokens, err)
						}
					}
					errs = append(errs, fmt.Errorf("device token(s) failure: [%v]", failedTokens))
				}
			}

		}(chunk)
	}
	wg.Wait()

	if len(errs) > 0 {
		return fmt.Errorf("the following errors occured: [%v]", errs)
	}

	return nil
}

func (s *AppPusher) chunkNotifications(
	notifications []*messaging.Message,
) [][]*messaging.Message {

	chunkedNotifications := make([][]*messaging.Message, 0)

	for i := 0; i < len(notifications); i += batchSize {
		end := i + batchSize

		if end > len(notifications) {
			end = len(notifications)
		}

		chunkedNotifications = append(chunkedNotifications, notifications[i:end])
	}

	return chunkedNotifications
}
