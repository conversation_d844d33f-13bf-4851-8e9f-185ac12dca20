package main

import (
	"context"
	"database/sql"
	"encoding/gob"
	"fmt"
	"os"
	esd_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/mock"
	"struts-pos-backend/app/providers"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/router"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/auth"

	"github.com/joho/godotenv"
)

func main() {

	// Logger
	utils.InitLogger(true)
	utils.Log.Info("App starting up")

	err := godotenv.Load(".env")
	if err != nil {
		panic("unable to load .env file!")
	}

	databaseURL := os.Getenv("DATABASE_URL")

	if os.Getenv("ENVIRONMENT") == "production" {
		databaseURL = os.Getenv("DATABASE_URL_PROD")
	}

	transactionsDB := esd_db.InitTransactionsDBWithURL(databaseURL)
	defer transactionsDB.Close()

	txnsDB, err := initiateDatabaseConnection(databaseURL)
	if err != nil {
		panic(fmt.Sprintf("Unable to connect to ESD database, err=[%v]", err))
	}

	// Providers
	var mpesa providers.MPesa
	var smtpSender providers.SmtpSender

	assetStore := providers.NewAssetStore(
		os.Getenv("AWS_BUCKET"),
		os.Getenv("AWS_REGION"),
		os.Getenv("AWS_ACCESS_KEY_ID"),
		os.Getenv("AWS_SECRET_ACCESS_KEY"),
		os.Getenv("ENVIRONMENT"),
	)

	uploadAssetStore := providers.NewAssetStore(
		os.Getenv("AWS_BUCKET"),
		os.Getenv("AWS_REGION"),
		os.Getenv("AWS_ASSET_UPLOADER_ACCESS_KEY_ID"),
		os.Getenv("AWS_ASSET_UPLOADER_SECRET_ACCESS_KEY"),
		os.Getenv("ENVIRONMENT"),
	)

	blurHash := providers.NewBlurHasher()
	imaging := providers.NewImaging()
	assetManager := providers.NewAssetManager(assetStore, uploadAssetStore, blurHash, imaging)

	utils.Log.Infof("assetManager=[%v]", assetManager)

	// Providers
	var etimsVSCUProvider providers.EtimsVSCU

	mpesaPhoneNumberHashDecoder := providers.NewMPesaPhoneNumberHashDecoder()

	var etimsAPIBaseURL string

	switch os.Getenv("ENVIRONMENT") {
	case "production":
		mpesa = providers.NewMPesa()
		smtpSender = providers.NewSmtpSender()
		etimsAPIBaseURL = os.Getenv("ETIMS_API_BASE_URL_PRODUCTION")
	case "staging":
		mpesa = mock.NewMPesa()
		smtpSender = providers.NewSmtpSender()
		etimsAPIBaseURL = os.Getenv("ETIMS_API_BASE_URL_SANDBOX")
	case "local", "test":
		mpesa = mock.NewMPesa()
		smtpSender = providers.NewSmtpSender()
		etimsAPIBaseURL = os.Getenv("ETIMS_API_BASE_URL_SANDBOX")
	default:
		mpesa = mock.NewMPesa()
		smtpSender = providers.NewSmtpSender()
		etimsAPIBaseURL = os.Getenv("ETIMS_API_BASE_URL_SANDBOX")
	}

	etimsVSCUProvider = providers.NewEtimsVSCU(etimsAPIBaseURL)
	// etimsVscuCoreWrapProvider := providers.NewTrnsSalesExecuteInitializer()

	// Repos
	activityRepository := repos.NewActivityRepository(txnsDB)
	apiKeyRepository := repos.NewAPIKeyRepository(txnsDB)
	applicationRepository := repos.NewApplicationRepository()
	categoryRepository := repos.NewCategoryRepository(txnsDB)
	companyRegistrationRepository := repos.NewCompanyRegistrationRepository(txnsDB)
	currencyRepository := repos.NewCurrencyRepository()
	customerIDTypeRepository := repos.NewCustomerIDTypeRepository(txnsDB)
	customerRepository := repos.NewCustomerRepository(txnsDB)
	dashboardAnalyticRepository := repos.NewDashboardAnalyticRepository(txnsDB)
	etimsItemRepository := repos.NewEtimsItemRepository(txnsDB)
	etimsStockItemRepository := repos.NewEtimsStockItemRepository(txnsDB)
	etimsStockRepository := repos.NewEtimsStockRepository(txnsDB)
	etimsNoticeRepository := repos.NewEtimsNoticeRepository(txnsDB)
	excelFileUploadRepository := repos.NewExcelFileUploadRepository(txnsDB)
	invoiceItemRepository := repos.NewInvoiceItemRepository(txnsDB)
	invoiceRepository := repos.NewInvoiceRepository(txnsDB)
	itemQuantityRepository := repos.NewItemQuantityRepository(txnsDB)
	itemRepository := repos.NewItemRepository(txnsDB)
	mpesaDepositRepository := repos.NewMPesaDepositRepository(txnsDB)
	organizationRepository := repos.NewOrganizationRepository(txnsDB)
	phoneNumberRepository := repos.NewPhoneNumberRepository()
	receivingRepository := repos.NewReceivingRepository(txnsDB)
	saleItemRepository := repos.NewSaleItemRepository(txnsDB)
	saleRepository := repos.NewSaleRepository(txnsDB)
	settingRepository := repos.NewSettingRepository(txnsDB)
	sessionRepository := repos.NewSessionRepository()
	storeRepository := repos.NewStoreRepository(txnsDB)
	supplierRepository := repos.NewSupplierRepository(txnsDB)
	userApplicationRepository := repos.NewUserApplicationRepository()
	userRepository := repos.NewUserRepository(txnsDB)
	zReportInvoiceRepository := repos.NewZReportInvoiceRepository(txnsDB)
	zReportRepository := repos.NewZReportRepository(txnsDB)

	// Services
	analyticsService := services.NewAnalyticsService(
		categoryRepository,
		customerRepository,
		dashboardAnalyticRepository,
		excelFileUploadRepository,
		itemRepository,
		invoiceRepository,
		organizationRepository,
		saleRepository,
		storeRepository,
		userRepository,
	)

	apiKeyService := services.NewAPIKeyService(
		apiKeyRepository,
		storeRepository,
		userRepository,
	)

	categoryService := services.NewCategoryService(
		categoryRepository,
		userRepository,
	)

	currencyService := services.NewCurrencyService(currencyRepository)

	customerService := services.NewCustomerService(customerRepository, userRepository)

	dashboardAnalyticsService := services.NewDashboardAnalyticsService(dashboardAnalyticRepository)

	etimsItemService := services.NewEtimsItemService(
		etimsVSCUProvider,
		etimsItemRepository,
		storeRepository,
	)

	etimsStockService := services.NewEtimsStockService(
		etimsVSCUProvider,
		etimsStockRepository,
		etimsStockItemRepository,
		storeRepository,
	)

	etimsVSCUService := services.NewEtimsVSCUService(
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_PRODUCTION"),
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_SANDBOX"),
		etimsVSCUProvider,
		// *etimsVscuCoreWrapProvider,
		dashboardAnalyticsService,
		etimsItemRepository,
		etimsNoticeRepository,
		invoiceItemRepository,
		invoiceRepository,
		os.Getenv("QRCODE_FOLDER"),
		storeRepository,
	)

	excelFileUploadService := services.NewExcelFileUploadService(
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_PRODUCTION"),
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_SANDBOX"),
		etimsVSCUProvider,
		excelFileUploadRepository,
		invoiceRepository,
		storeRepository,
		userRepository,
	)

	traVfdService := services.NewTRAVFDService(
		companyRegistrationRepository,
		customerIDTypeRepository,
		invoiceRepository,
		zReportRepository,
		zReportInvoiceRepository,
	)

	creditNoteService := services.NewCreditNoteService(
		etimsItemRepository,
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_PRODUCTION"),
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_SANDBOX"),
		etimsVSCUProvider,
		dashboardAnalyticsService,
		invoiceItemRepository,
		invoiceRepository,
		storeRepository,
		traVfdService,
		userRepository,
	)

	invoiceService := services.NewInvoiceService(
		invoiceItemRepository,
		invoiceRepository,
		smtpSender,
		storeRepository,
		traVfdService,
		userRepository,
	)

	itemService := services.NewItemService(
		etimsVSCUProvider,
		etimsItemRepository,
		itemQuantityRepository,
		itemRepository,
		organizationRepository,
		storeRepository,
		userRepository,
	)

	paymentService := services.NewPaymentService(
		mpesaDepositRepository,
		mpesaPhoneNumberHashDecoder,
		organizationRepository,
		phoneNumberRepository,
		smtpSender,
	)

	paymentTransactionsService := services.NewPaymentTransactionsService(mpesa)

	receivingService := services.NewReceivingService(
		itemQuantityRepository,
		itemRepository,
		organizationRepository,
		receivingRepository,
		userRepository,
	)

	saleService := services.NewSaleService(
		currencyRepository,
		itemRepository,
		organizationRepository,
		saleItemRepository,
		saleRepository,
		storeRepository,
		userRepository,
	)

	settingService := services.NewSettingService(settingRepository)

	sessionService := services.NewSessionService(
		applicationRepository,
		sessionRepository,
		userApplicationRepository,
		userRepository,
	)

	storeService := services.NewStoreService(
		organizationRepository,
		storeRepository,
		userRepository,
	)

	supplierService := services.NewSupplierService(supplierRepository, userRepository)

	userService := services.NewUserService(
		activityRepository,
		applicationRepository,
		organizationRepository,
		phoneNumberRepository,
		sessionRepository,
		storeRepository,
		userApplicationRepository,
		userRepository,
	)

	sessionAuthenticator := auth.NewSessionAuthenticator(sessionRepository, userRepository)

	// Router
	ginRouter := router.SetupRouter(
		transactionsDB,
		sessionAuthenticator,
		activityRepository,
		apiKeyRepository,
		apiKeyService,
		creditNoteService,
		etimsItemService,
		etimsStockService,
		etimsVSCUService,
		excelFileUploadService,
		userRepository,
		analyticsService,
		categoryService,
		currencyService,
		customerService,
		invoiceService,
		itemService,
		paymentService,
		paymentTransactionsService,
		receivingService,
		saleService,
		settingService,
		sessionService,
		storeRepository,
		storeService,
		supplierService,
		userService,
	)

	ctx := context.Background()
	userService.SetupAdmin(ctx, transactionsDB)

	// for gin context access later
	gob.Register(entities.User{})

	port := os.Getenv("PORT")

	// Start server
	utils.Log.Infof("Starting application on port=[%v]", port)
	ginRouter.Run(":" + port)
	utils.Log.Infof("Started application on port=[%v]", port)
}

func initiateDatabaseConnection(databaseURL string) (*sql.DB, error) {

	txnsDB, err := sql.Open("postgres", databaseURL)
	if err != nil {
		panic(fmt.Sprintf("Unable to connect to ESD database, err=[%v]", err))
	}

	// postgrse DB Connection - *sql.DB
	postgresESDDB, err := setupDatabase(databaseURL)
	if err != nil {
		panic(fmt.Sprintf("Unable to connect to postgres database, err=[%v]", err))
	}

	// run db migrations
	storage := esd_db.NewStorage(postgresESDDB)
	err = storage.RunMigrations(databaseURL)
	if err != nil {
		panic(fmt.Sprintf("Unable to run postgres db migrations, err=[%v]", err))
	}

	return txnsDB, nil
}

func setupDatabase(connString string) (*sql.DB, error) {
	// change "postgres" for whatever supported database you want to use
	db, err := sql.Open("postgres", connString)
	if err != nil {
		return nil, err
	}

	// ping the DB to ensure that it is connected
	err = db.Ping()
	if err != nil {
		return nil, err
	}

	return db, nil
}

func conductEtimsTests() {
	// Demo local dev device
	etimsVscuCore := providers.NewEtimsVSCUCore(
		"https://etims-api-sbx.kra.go.ke/etims-api",
		"00",
		"SN5433Q09",
		"P051922564N",
	)

	// etimsVscuCore = providers.NewEtimsVSCUCore(
	// 	"https://etims-api.kra.go.ke/etims-api",
	// 	"02",
	// 	"STAN-CVU-Y12-8879",
	// 	"P052012153M",
	// )
	// etimsVscuCore.SelectServerTime()
	etimsVscuCore.SelectNotices()
	etimsVscuCore.EchoTest("Testing 123..")
	// etimsVscuCore.SelectInfo()

	// Demo local dev device
	// etimsVscuCore = providers.NewEtimsVSCUCore(
	// 	"https://etims-api.kra.go.ke/etims-api",
	// 	"00",
	// 	"SOK241PRMA",
	// 	"P052133751T",
	// )
	// etimsVscuCore.SelectInfo()
}
