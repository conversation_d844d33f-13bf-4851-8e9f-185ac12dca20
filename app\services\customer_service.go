package services

import (
	"context"
	"errors"
	"fmt"
	"struts-pos-backend/app/apperr"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/ctxhelper"

	txns_db "struts-pos-backend/app/database"
)

type CustomerService interface {
	CreateCustomer(ctx context.Context, operations txns_db.TransactionsSQLOperations, customer *forms.CreateCustomerForm, storeId int64) (*entities.Customer, error)
	DownloadCustomers(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string, storeId int64) (*entities.CustomerList, error)
	FilterCustomers(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, storeId int64) (*entities.CustomerList, error)
	FindCustomerByID(ctx context.Context, customerID int64, storeId int64) (*entities.Customer, error)
	GetCustomerByName(ctx context.Context, customerNumber string) (*entities.Customer, error)
	SaveCustomer(ctx context.Context, operations txns_db.TransactionsSQLOperations, customer *entities.Customer) error
	UpdateCustomer(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateCustomerForm, int64) (*entities.Customer, error)
}

type AppCustomerService struct {
	customerRepository repos.CustomerRepository
	userRepository     repos.UserRepository
}

func NewCustomerService(
	customerRepo repos.CustomerRepository,
	userRepository repos.UserRepository,
) CustomerService {
	return &AppCustomerService{
		customerRepository: customerRepo,
		userRepository:     userRepository,
	}
}

func (s *AppCustomerService) CreateCustomer(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateCustomerForm,
	storeId int64,
) (*entities.Customer, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Customer{}, err
	}

	if form.StoreID != storeId {
		return &entities.Customer{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"store id in form=[%v] and request header=[%v] do not match",
			form.StoreID,
			storeId,
		)
	}

	customer, err := s.customerRepository.FindByEmailAndStore(ctx, form.Email, form.StoreID)
	if err != nil && !utils.IsErrNoRows(err) {
		return customer, errors.New("customer already exists")
	}

	customer.FirstName = form.FirstName
	customer.LastName = form.LastName
	customer.Email = form.Email
	customer.PhoneNumber = form.PhoneNumber
	customer.OrganizationID = user.OrganizationID
	customer.StoreID = storeId

	err = s.customerRepository.Save(ctx, customer)
	if err != nil {
		return customer, err
	}

	return customer, nil
}

func (s *AppCustomerService) DownloadCustomers(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
	storeId int64,
) (*entities.CustomerList, error) {

	customerList := &entities.CustomerList{}

	count, err := s.customerRepository.CountCustomers(ctx, filter, storeId)
	if err != nil {
		return customerList, err
	}

	categories, err := s.customerRepository.FilterCustomers(ctx, operations, filter, storeId)
	if err != nil {
		return customerList, err
	}

	customerList.Customers = categories

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	customerList.Pagination = pagination

	// utils.CreateAndAppendCustomersDataToExcelFile(filePath, categories)

	return customerList, nil
}

func (s *AppCustomerService) FilterCustomers(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	storeId int64,
) (*entities.CustomerList, error) {

	customerList := &entities.CustomerList{}

	count, err := s.customerRepository.CountCustomers(ctx, filter, storeId)
	if err != nil {
		fmt.Printf("Unable to count customers, err=[%v]\n", err.Error())
		return customerList, err
	}

	customers, err := s.customerRepository.FilterCustomers(ctx, operations, filter, storeId)
	if err != nil {
		fmt.Printf("Unable to filter customers, err=[%v]\n", err.Error())
		return customerList, err
	}

	customerList.Customers = customers

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	customerList.Pagination = pagination

	return customerList, nil
}

func (s *AppCustomerService) FindCustomerByID(
	ctx context.Context,
	customerID int64,
	storeId int64,
) (*entities.Customer, error) {

	customer, err := s.customerRepository.FindByID(ctx, customerID, storeId)
	if err != nil {
		return customer, err
	}

	return customer, nil
}

func (s *AppCustomerService) GetCustomerByName(
	ctx context.Context,
	customerNumber string,
) (*entities.Customer, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Customer{}, err
	}

	customer, err := s.customerRepository.FindByEmailAndStore(ctx, customerNumber, user.OrganizationID)
	if err != nil {
		return customer, err
	}

	return customer, nil
}

func (s *AppCustomerService) SaveCustomer(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	customer *entities.Customer,
) error {

	err := s.customerRepository.Save(ctx, customer)
	if err != nil {
		return err
	}

	return nil
}

func (s *AppCustomerService) UpdateCustomer(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	customerID int64,
	form *forms.UpdateCustomerForm,
	storeId int64,
) (*entities.Customer, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	customer, err := s.customerRepository.FindByID(ctx, customerID, storeId)
	if err != nil {
		return customer, errors.New("unable to find customer")
	}

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Customer{}, err
	}

	if customer.OrganizationID != user.OrganizationID {
		return &entities.Customer{}, errors.New("user does not have permission to update organization customer")
	}

	if form.FirstName != "" {
		customer.FirstName = form.FirstName
	}

	if form.LastName != "" {
		customer.LastName = form.LastName
	}

	if form.Email != "" {
		customer.Email = form.Email
	}

	if form.PhoneNumber != "" {
		customer.PhoneNumber = form.PhoneNumber
	}

	err = s.customerRepository.Save(ctx, customer)
	if err != nil {
		return customer, err
	}

	return customer, nil
}
