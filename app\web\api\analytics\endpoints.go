package analytics

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/web/auth"

	"struts-pos-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
	storeRepository repos.StoreRepository,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.GET("/analytics", getDashboardAnalytics(transactionsDB, analyticsService, storeRepository))
		protectedAPI.GET("/analytics/dashboard", getDashboardAnalytics(transactionsDB, analyticsService, storeRepository))
	}
}
