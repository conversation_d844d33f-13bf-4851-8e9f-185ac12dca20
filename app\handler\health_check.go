package handler

import (
	"fmt"
	"log"
	"net/http"
	"strings"
	"struts-pos-backend/app/models"
	"struts-pos-backend/app/utils"
	"time"

	"github.com/jinzhu/gorm"
)

var key = "EuXqfey1AeDwo9S3Vivo23ZO2yfz97sQHOqb5Da8jKwasdfasdf821O6zke4zBN5"

func HealthCheck(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	signature := GenerateLicense()
	ValidateLicense(signature)

	response := models.JSONResponse{}
	response.Status = "00"
	response.Description = "OK. All services up."
	respondJSON(w, http.StatusOK, response)
}

func GenerateLicense() string {
	signature := "2024-02-14 : Struts POS 2024 License by Struts Technology."
	encrypted, err1 := utils.EncryptString(signature, key)
	utils.CheckError(err1, "Error encrypting string.")
	fmt.Printf("license=[%v]\n", encrypted)
	return encrypted
}

func ValidateLicense(encrypted string) bool {

	decrypted, err2 := utils.DecryptString(encrypted, key)
	utils.CheckError(err2, "Error decrypting string.")

	// 29-03-2019 : HSC 2019 License by Struts Technology.
	signatureArray := strings.Split(decrypted, ":")

	date := signatureArray[0]
	date = strings.Replace(date, " ", "", -1)

	expiryDate, err3 := time.Parse("2006-01-02", date)
	utils.CheckError(err3, "Error parsing expiry date!")

	// Check if within validity period
	if expiryDate.After(time.Now()) {
		// log.Println("License is Valid.")
		return true
	} else {
		log.Println("License is Expired!")
		return false
	}

}
