package services

import (
	"context"

	db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
)

type (
	CurrencyService interface {
		FilterCurrencies(context.Context, db.TransactionsSQLOperations, *entities.PaginationFilter) (*entities.CurrencyList, error)
		FindCurrencyByID(context.Context, db.TransactionsSQLOperations, int64) (*entities.Currency, error)
	}

	AppCurrencyService struct {
		currencyRepository repos.CurrencyRepository
	}
)

func NewCurrencyService(
	currencyRepository repos.CurrencyRepository,
) CurrencyService {
	return &AppCurrencyService{
		currencyRepository: currencyRepository,
	}
}

func (s *AppCurrencyService) FilterCurrencies(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.CurrencyList, error) {

	currencyList := &entities.CurrencyList{}

	currencies, err := s.currencyRepository.FilterCurrencies(ctx, operations, filter)
	if err != nil {
		return currencyList, utils.NewDatabaseError(
			err,
			"Failed to filter Currencys with filter=[%v]",
			filter,
		)
	}

	currenciesCount, err := s.currencyRepository.CountCurrencies(ctx, operations, filter)
	if err != nil {
		return currencyList, utils.NewDatabaseError(
			err,
			"Failed to count Currencys with filter=[%v]",
			filter,
		)
	}

	currencyList.Currencies = currencies
	currencyList.Pagination.Count = currenciesCount
	currencyList.Pagination.Page = filter.Page
	currencyList.Pagination.Per = filter.Per

	return currencyList, nil
}

func (s *AppCurrencyService) FindCurrencyByID(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	currencyID int64,
) (*entities.Currency, error) {

	currency, err := s.currencyRepository.FindByID(ctx, operations, currencyID)
	if err != nil {
		return currency, utils.NewDatabaseError(
			err,
			"Failed to find Currency by id=[%v]",
			currencyID,
		)
	}

	return currency, err
}
