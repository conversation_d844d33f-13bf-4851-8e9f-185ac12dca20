package entities

type (

	// TrnsSalesSaveWrReq represents the request for saving a transaction
	TrnsSalesSaveWrReq struct {
		Tin         string       `json:"tin"`
		BhfId       string       `json:"bhfId"`
		CustTin     string       `json:"custTin"`
		SalesTyCd   string       `json:"salesTyCd"`
		RcptTyCd    string       `json:"rcptTyCd"`
		InvcNo      int64        `json:"invcNo"`
		TaxblAmtA   float64      `json:"taxblAmtA"`
		TaxblAmtB   float64      `json:"taxblAmtB"`
		TaxAmtA     float64      `json:"taxAmtA"`
		TaxAmtB     float64      `json:"taxAmtB"`
		Receipt     *ReceiptData `json:"receipt"`
		SalesSttsCd string       `json:"salesSttsCd"`
	}

	// ReceiptData represents receipt information
	ReceiptData struct {
		CurRcptNo  int64  `json:"curRcptNo"`
		TotRcptNo  int64  `json:"totRcptNo"`
		RcptPbctDt string `json:"rcptPbctDt"`
		RptNo      int64  `json:"rptNo"`
		IntrlData  string `json:"intrlData"`
		RcptSign   string `json:"rcptSign"`
	}

	// TrnsSalesSaveWrRes represents the response for saving a transaction
	TrnsSalesSaveWrRes struct {
		ResultCd  string                  `json:"resultCd"`
		ResultMsg string                  `json:"resultMsg"`
		Data      *TrnsSalesSaveWrResData `json:"data,omitempty"`
	}

	// TrnsSalesSaveWrResData represents the data in the response
	TrnsSalesSaveWrResData struct {
		SdcId            string `json:"sdcId"`
		MrcNo            string `json:"mrcNo"`
		RcptNo           int64  `json:"rcptNo"`
		TotRcptNo        int64  `json:"totRcptNo"`
		VsdcRcptPbctDate string `json:"vsdcRcptPbctDate"`
		IntrlData        string `json:"intrlData"`
		RcptSign         string `json:"rcptSign"`
	}
)
