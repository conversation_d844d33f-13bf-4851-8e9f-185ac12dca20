package instagram

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/instagram/callback_url", processInstagramCallback(transactionsDB, apiKeyService))
	}
}
