package ctxhelper

import (
	"context"

	"struts-pos-backend/app/entities"
)

func ClientIdentifier(ctx context.Context) entities.ClientIdentifier {
	existing := ctx.Value(entities.ContextKeyClientIdentifier)
	if existing == nil {
		return entities.ClientIdentifier("")
	}

	return entities.ClientIdentifier(existing.(string))
}

func WithClientIdentifier(ctx context.Context, clientIdentifier string) context.Context {
	return context.WithValue(ctx, entities.ContextKeyClientIdentifier, clientIdentifier)
}
