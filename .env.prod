API_BASE_URL=https://optimusapi.strutstechnology.com/api
DATABASE_URL=postgres://gidemn:Rjt2LbYz2<PERSON>m<PERSON><EMAIL>:5432/struts_pos?sslmode=disable
DATABASE_URL_PROD=postgres://gidemn:<EMAIL>:5432/struts_pos?sslmode=disable
ENVIRONMENT=production
ETIMS_API_BASE_URL=http://**************:8088
ETIMS_API_BASE_URL_DOCKER_1=http://host.docker.internal:8088
ETIMS_API_BASE_URL_DOCKER=http://**************:8088
ETIMS_BRANCH_ID=00
ETIMS_DEVICE_SERIAL_NUMBER=SN5433Q09
ETIMS_PIN_NUMBER_PROD=P051629857B
ETIMS_PIN_NUMBER=P051922564N
ETIMS_VERIFICATION_BASE_URL=https://etims-sbx.kra.go.ke
ETIMS_VERIFICATION_BASE_URL_PROD=https://etims.kra.go.ke
ETIMS_VERIFICATION_BASE_URL_SANDBOX=https://etims-sbx.kra.go.ke
ETIMS_VERIFICATION_BASE_URL_PRODUCTION=https://etims.kra.go.ke
EXCHANGE_RATE_API_KEY=XXXX
MPESA_API_URL=https://api.safaricom.co.ke
MPESA_SHORTCODE=4044085
MPESA_PASSKEY=XXXXX
MPESA_HASH_API_KEY=a2a23a9445bdcefcffce
MPESA_HASH_API_URL=https://hashesapi-1-t9408137.deta.app
QRCODE_FOLDER=/tmp/etims_qr_codes
SERVER_IP=localhost
SMTP_EMAIL=<EMAIL> 
SMTP_PASSWORD=EmailPasss
SMTP_PORT=465
SMTP_HOST=mail.domain.com
CC_EMAILS=<EMAIL>
PORT=9015
