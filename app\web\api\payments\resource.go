package payments

import (
	"context"
	"io"
	"log"
	"net/http"

	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/webutils"

	"github.com/gin-gonic/gin"
)

func decodeMpesaPhonenumberHash(
	txnsDB txns_db.TransactionsDB,
	paymentService services.PaymentService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		mpesaPhoneNumberHash := ctx.Params.ByName("hash")

		decodeResponse, _, err := paymentService.DecodePhoneNumberHash(ctx, txnsDB, mpesaPhoneNumberHash)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to deode phone number hash",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, decodeResponse)
	}
}

func processSafaricomPaybillAccBalance(
	txnsDB txns_db.TransactionsDB,
) func(c *gin.Context) {

	return func(c *gin.Context) {

		body, _ := io.ReadAll(c.Request.Body)
		requestBody := string(body)
		// TODO: Save the latest balance to db under mpesa details
		log.Printf("Processing M-Pesa Acc balance, requestBody=[%v]\n", requestBody)

		// Validate all payments
		c.JSON(http.StatusOK, "")
	}
}

func processSafaricomPaymentConfirmation(
	txnsDB txns_db.TransactionsDB,
	paymentService services.PaymentService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var form forms.MPesaDepositForm
		err := ctx.Bind(&form)
		if err != nil {
			log.Printf("Failed to bind mpesa topup form, err=[%v]", err)
		}

		log.Printf("Processing M-Pesa payment confirmation, form;\n")
		utils.PrintStructToConsole(form)
		log.Printf("---------------------------------\n")

		err = paymentService.ProcessMPesaDeposit(ctx, txnsDB, &form)
		if err != nil {
			log.Printf("Failed to process mpesa topup, err=[%v]", err)
		}

		// Validate all payments
		ctx.JSON(http.StatusOK, gin.H{
			"status": "Ok",
		})
	}
}

func processSafaricomPaymentValidation(
	txnsDB txns_db.TransactionsDB,
	paymentService services.PaymentService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		body, _ := io.ReadAll(ctx.Request.Body)
		requestBody := string(body)
		// TODO: Return success to accept all payments.
		log.Printf("Processing M-Pesa payment validation, requestBody=[%v]\n", requestBody)

		// Validate all payments
		ctx.JSON(http.StatusOK, gin.H{
			"status": "Ok",
		})
	}
}

func processSafaricomSTKCallback(
	txnsDB txns_db.TransactionsDB,
) func(c *gin.Context) {

	return func(c *gin.Context) {

		body, _ := io.ReadAll(c.Request.Body)
		requestBody := string(body)
		// TODO: Route STK Callback respectively by calling application
		log.Printf("Processing M-Pesa STK callback, requestBody=[%v]\n", requestBody)

		// Validate all payments
		c.JSON(http.StatusOK, "")
	}
}

func processSafaricomSTKResult(
	txnsDB txns_db.TransactionsDB,
) func(c *gin.Context) {

	return func(c *gin.Context) {

		body, _ := io.ReadAll(c.Request.Body)
		requestBody := string(body)
		// TODO: Route STK Callback respectively by calling application
		log.Printf("Processing M-Pesa STK result, requestBody=[%v]\n", requestBody)

		// Validate all payments
		c.JSON(http.StatusOK, "")
	}
}

func processSafaricomTimeout(
	txnsDB txns_db.TransactionsDB,
) func(c *gin.Context) {

	return func(c *gin.Context) {

		body, _ := io.ReadAll(c.Request.Body)
		requestBody := string(body)
		// TODO: Clear up any items held by order
		log.Printf("Processing M-Pesa timeout, requestBody=[%v]\n", requestBody)

		// Validate all payments
		c.JSON(http.StatusOK, "")
	}
}

func requestSafaricomPaybillAccBalance(
	txnsDB txns_db.TransactionsDB,
	paymentTransactionsService services.PaymentTransactionsService,
) func(c *gin.Context) {

	return func(c *gin.Context) {

		res, err := paymentTransactionsService.RequestMPesaPaybillBalance(context.Background(), txnsDB)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to request for Safaricom M-Pesa paybill balance",
			)

			webutils.HandleError(c, appError)
			return
		}

		c.JSON(http.StatusOK, res)
	}
}

func registerMPesaCallbackURL(
	txnsDB txns_db.TransactionsDB,
	paymentTransactionsService services.PaymentTransactionsService,
) func(c *gin.Context) {

	return func(c *gin.Context) {

		var form forms.RegisterMPesaURLsForm

		err := c.BindJSON(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidForm,
				"Failed to bind M-Pesa register URL form",
			)

			webutils.HandleError(c, appError)
			return
		}

		err = paymentTransactionsService.RegisterMPesaCallbackURL(context.Background(), &form)
		if err != nil {
			appError := utils.NewErrorWithErrorCodeAndMessage(
				err,
				utils.ErrorCodeRequestFailed,
				"Failed to register URL, err="+err.Error(),
				err.Error(),
			)

			webutils.HandleError(c, appError)
			return
		}

		c.JSON(http.StatusOK, "M-Pesa URLs registered successfully.")
	}
}

func registerClientsMPesaCallbackURLs(
	txnsDB txns_db.TransactionsDB,
	paymentTransactionsService services.PaymentTransactionsService,
) func(c *gin.Context) {

	return func(c *gin.Context) {

		var form forms.RegisterClientMPesaURLsForm

		err := c.BindJSON(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidForm,
				"Failed to bind client M-Pesa register URL form",
			)

			webutils.HandleError(c, appError)
			return
		}

		err = paymentTransactionsService.RegisterClientMPesaCallbackURL(context.Background(), &form)
		if err != nil {
			appError := utils.NewErrorWithErrorCodeAndMessage(
				err,
				utils.ErrorCodeRequestFailed,
				"Failed to register URL for client, err="+err.Error(),
				err.Error(),
			)

			webutils.HandleError(c, appError)
			return
		}

		c.JSON(http.StatusOK, "M-Pesa URLs registered successfully for client.")
	}
}
