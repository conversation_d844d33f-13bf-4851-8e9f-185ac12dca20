package mock

import (
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/logger"
	"struts-pos-backend/app/providers"
)

type (
	MockMPesaPhoneNumberHashDecoder struct {
	}
)

func NewMPesaPhoneNumberHashDecoder() providers.MPesaPhoneNumberHashDecoder {
	return &MockMPesaPhoneNumberHashDecoder{}
}

func (s *MockMPesaPhoneNumberHashDecoder) DecodePhoneNumberHash(phoneNumberHash string) (*entities.MPesaPhoneNumberHashDecoderResponse, error) {
	logger.Infof("decoding phone number hash=[%v]", phoneNumberHash)
	return &entities.MPesaPhoneNumberHashDecoderResponse{Hash: phoneNumberHash, PhoneNumber: "************"}, nil
}

func (s *MockMPesaPhoneNumberHashDecoder) TopUpHashAccount(form *forms.PhoneNumberHashTopupForm) (*entities.PhoneNumberHashTopupResponse, error) {
	logger.Infof("topping up account=[%v] with amount=[%v]", form.AccountNumber, form.Amount)
	return &entities.PhoneNumberHashTopupResponse{PhoneNumber: "************", CreditBalance: 1000}, nil
}
