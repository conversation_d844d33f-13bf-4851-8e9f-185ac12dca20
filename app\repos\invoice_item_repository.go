package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
	"struts-pos-backend/app/entities"
	"time"

	txns_db "struts-pos-backend/app/database"
)

const (
	countInvoiceItemsSQL = `SELECT COUNT(*) AS count FROM invoice_items WHERE 1=1`

	insertInvoiceItemSQL = `INSERT INTO invoice_items (invoice_id, name, quantity, serial_number, rate, vat, total, uuid, created_at, updated_at) VALUES`

	createInvoiceItemSQL = insertInvoiceItemSQL + ` ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING id`

	findInvoiceItemsByInvoiceIDsSQL = selectInvoiceItemSQL + " AND invoice_id IN "

	selectInvoiceItemByInvoiceItemNumberSQL = selectInvoiceItemSQL + ` AND invoice_id=$1`

	selectInvoiceItemByIDSQL = selectInvoiceItemSQL + ` AND id=$1`

	selectInvoiceItemSQL = `SELECT id, invoice_id, name, quantity, serial_number, rate, vat, total, uuid, created_at, updated_at FROM invoice_items WHERE 1=1`

	updateInvoiceItemSQL = `UPDATE invoice_items SET name=$1, updated_at=$2 WHERE id=$3`

	updateMultipleInvoiceItemsSQL = `UPDATE invoice_items AS c SET (name, updated_at) = (u.name, u.updated_at)`
)

type (
	InvoiceItemRepository interface {
		CountInvoiceItems(ctx context.Context, filter *entities.PaginationFilter) (int, error)
		FilterInvoiceItems(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) ([]*entities.InvoiceItem, error)
		GetInvoiceItemByInvoiceItemNumber(ctx context.Context, email string) (*entities.InvoiceItem, error)
		GetInvoiceItemsForInvoiceIDs(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoiceIDs []int64) ([]*entities.InvoiceItem, error)
		GetInvoiceItemByID(ctx context.Context, invoiceID int64) (*entities.InvoiceItem, error)
		GetZReportTotals(ctx context.Context, operations txns_db.TransactionsSQLOperations, isZeportedCheck bool) (entities.ZReportTotals, error)
		Save(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoiceItem *entities.InvoiceItem) error
		SaveMultiple(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoiceItems []*entities.InvoiceItem) error
		UpdateMultipleInvoiceItems(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoiceItems []*entities.InvoiceItem) error
	}

	AppInvoiceItemRepository struct {
		db *sql.DB
	}
)

func NewInvoiceItemRepository(db *sql.DB) InvoiceItemRepository {
	return &AppInvoiceItemRepository{db: db}
}

func (r *AppInvoiceItemRepository) CountInvoiceItems(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countInvoiceItemsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count, nil
}

func (r *AppInvoiceItemRepository) FilterInvoiceItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.InvoiceItem, error) {

	invoiceItems := make([]*entities.InvoiceItem, 0)

	args := make([]interface{}, 0)
	query := selectInvoiceItemSQL
	currentIndex := 1

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return invoiceItems, err
	}

	defer rows.Close()

	for rows.Next() {
		invoiceItem, err := r.scanRowIntoInvoiceItem(rows)
		if err != nil {
			return invoiceItems, err
		}

		invoiceItems = append(invoiceItems, invoiceItem)
	}

	return invoiceItems, rows.Err()
}

func (r *AppInvoiceItemRepository) GetInvoiceItemByID(
	ctx context.Context,
	invoiceID int64,
) (*entities.InvoiceItem, error) {
	row := r.db.QueryRow(selectInvoiceItemByIDSQL, invoiceID)
	return r.scanRowIntoInvoiceItem(row)
}

func (r *AppInvoiceItemRepository) GetInvoiceItemsForInvoiceIDs(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	invoiceIDs []int64,
) ([]*entities.InvoiceItem, error) {

	invoiceItems := make([]*entities.InvoiceItem, 0)

	if len(invoiceIDs) == 0 {
		return invoiceItems, nil
	}

	var args []interface{}
	query := findInvoiceItemsByInvoiceIDsSQL

	values := make([]string, len(invoiceIDs))
	for index, invoiceID := range invoiceIDs {
		values[index] = fmt.Sprintf("$%d", index+1)
		args = append(args, invoiceID)
	}

	invoiceIDsString := " (" + strings.Join(values, ",") + ")"
	query = query + invoiceIDsString

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return invoiceItems, err
	}

	defer rows.Close()
	for rows.Next() {
		invoiceItem, err := r.scanRowIntoInvoiceItem(rows)
		if err != nil {
			return invoiceItems, err
		}

		invoiceItems = append(invoiceItems, invoiceItem)
	}

	return invoiceItems, rows.Err()
}

func (r *AppInvoiceItemRepository) GetZReportTotals(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	isZeportedCheck bool,
) (entities.ZReportTotals, error) {

	var zReportTotals entities.ZReportTotals

	args := []interface{}{}
	query := selectZReportSummarySQL

	if isZeportedCheck {
		query += " AND is_z_reported=false"
	}

	err := operations.QueryRowContext(ctx, query, args...).Scan(
		&zReportTotals.Count,
		&zReportTotals.NetAmount,
		&zReportTotals.VatAmount,
		&zReportTotals.TotalAmount,
	)
	if err != nil {
		return zReportTotals, err
	}
	return zReportTotals, nil
}

func (r *AppInvoiceItemRepository) GetInvoiceItemByInvoiceItemNumber(
	ctx context.Context,
	email string,
) (*entities.InvoiceItem, error) {
	row := r.db.QueryRow(selectInvoiceItemByInvoiceItemNumberSQL, email)
	return r.scanRowIntoInvoiceItem(row)
}

func (r *AppInvoiceItemRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	invoiceItem *entities.InvoiceItem,
) error {

	invoiceItem.Timestamps.Touch()
	var err error

	if invoiceItem.IsNew() {
		res := operations.QueryRowContext(
			ctx,
			createInvoiceItemSQL,
			invoiceItem.InvoiceID,
			invoiceItem.Name,
			invoiceItem.Quantity,
			invoiceItem.SerialNumber,
			invoiceItem.Rate,
			invoiceItem.Vat,
			invoiceItem.Total,
			invoiceItem.UUID,
			invoiceItem.CreatedAt,
			invoiceItem.UpdatedAt,
		)

		err = res.Scan(&invoiceItem.ID)

	} else {

		_, err = operations.ExecContext(
			ctx,
			updateInvoiceItemSQL,
			invoiceItem.InvoiceID,
			invoiceItem.UpdatedAt,
			invoiceItem.ID,
		)
	}

	if err != nil {
		log.Printf("error saving invoiceItem, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppInvoiceItemRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	invoice_items []*entities.InvoiceItem,
) error {

	query := insertInvoiceItemSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(invoice_items))

	for index, invoiceItem := range invoice_items {
		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4, currentIndex+5,
			currentIndex+6, currentIndex+7, currentIndex+8, currentIndex+9)
		currentIndex += 10
		args = append(
			args,
			invoiceItem.InvoiceID,
			invoiceItem.Name,
			invoiceItem.Quantity,
			invoiceItem.SerialNumber,
			invoiceItem.Rate,
			invoiceItem.Vat,
			invoiceItem.Total,
			invoiceItem.UUID,
			time.Now(),
			time.Now(),
		)
	}

	query += strings.Join(values, ",")
	query += " ON CONFLICT(invoice_id, name, uuid) DO UPDATE SET quantity = EXCLUDED.quantity, total = EXCLUDED.total, updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&invoice_items[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppInvoiceItemRepository) SelectZReportSummary(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.ZReportTotals, error) {

	zReportTotals := &entities.ZReportTotals{}

	return zReportTotals, nil
}

func (r *AppInvoiceItemRepository) UpdateMultipleInvoiceItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	invoice_items []*entities.InvoiceItem,
) error {

	query := updateMultipleInvoiceItemsSQL + " FROM (VALUES "
	values := make([]string, len(invoice_items))
	args := make([]interface{}, 0)
	currentIndex := 1

	for index, invoiceItem := range invoice_items {
		values[index] = fmt.Sprintf("($%d::bigint, $%d::bool, $%d::timestamptz)", currentIndex, currentIndex+1, currentIndex+2)
		currentIndex += 3
		args = append(args, invoiceItem.ID, invoiceItem.InvoiceID, time.Now())
	}

	query += strings.Join(values, ",")
	query += ") AS u(id, is_z_reported, updated_at) where u.id = c.id"
	_, err := operations.ExecContext(ctx, query, args...)

	return err
}

func (r *AppInvoiceItemRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	// if filter.IsZReported.Valid {
	// 	query += fmt.Sprintf(" AND is_z_reported = $%d ", currentIndex)
	// 	args = append(args, filter.IsZReported.Bool)
	// 	currentIndex++
	// }

	// if len(filter.SearchCriteria) > 0 {

	// 	switch filter.SearchCriteria {
	// 	case "invoice_items_failed_to_sign":
	// 		query += " AND LENGTH(signature) < 5 AND created_at < DATEADD(day, -1, convert(date, GETDATE()))"

	// 	case "processing_invoice_items":
	// 		query += " AND LENGTH(signature) < 5"

	// 	case "signed_invoice_items":
	// 		query += " AND LENGTH(signature) > 5"
	// 	}
	// }

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND LOWER(invoice_number) LIKE '%%' || $%d || '%%' ", currentIndex)
		args = append(args, strings.ToLower(filter.Search))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppInvoiceItemRepository) scanRowIntoInvoiceItem(
	rowScanner txns_db.RowScanner,
) (*entities.InvoiceItem, error) {

	var invoiceItem entities.InvoiceItem

	err := rowScanner.Scan(
		&invoiceItem.ID,
		&invoiceItem.InvoiceID,
		&invoiceItem.Name,
		&invoiceItem.Quantity,
		&invoiceItem.SerialNumber,
		&invoiceItem.Rate,
		&invoiceItem.Vat,
		&invoiceItem.Total,
		&invoiceItem.UUID,
		&invoiceItem.CreatedAt,
		&invoiceItem.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning invoiceItem,  err=[%v]\n", err.Error())
		return &invoiceItem, err
	}

	return &invoiceItem, nil
}
