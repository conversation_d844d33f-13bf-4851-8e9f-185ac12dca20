package suppliers

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	supplierService services.SupplierService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/suppliers", createSupplier(transactionsDB, supplierService))
		protectedAPI.GET("/suppliers", filterSuppliers(transactionsDB, supplierService))
		protectedAPI.GET("/suppliers/:id", getSupplier(transactionsDB, supplierService))
		protectedAPI.PUT("/suppliers/:id", updateSupplier(transactionsDB, supplierService))
		protectedAPI.GET("/suppliers/download", downloadSuppliersExcel(transactionsDB, supplierService))
	}
}
