package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"struts-pos-backend/app/apperr"
	"struts-pos-backend/app/database"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/logger"
	"struts-pos-backend/app/providers"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/ctxhelper"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
)

type ExcelFileUploadService interface {
	CreateExcelFileUpload(ctx context.Context, operations database.TransactionsSQLOperations, form *forms.CreateExcelFileUploadForm) (*entities.ExcelFileUpload, error)
	DeleteExcelFileUpload(ctx context.Context, id int64) (*entities.ExcelFileUpload, error)
	FilterExcelFileUploads(ctx context.Context, operations database.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.ExcelFileUploadList, error)
	FindExcelFileUploadByID(ctx context.Context, excelFileUploadID int64) (*entities.ExcelFileUpload, error)
	GetExcelFileUploadByUUID(ctx context.Context, excelFileUpload string) (*entities.ExcelFileUpload, error)
	ProcessFileUploads(ctx context.Context, operations database.TransactionsSQLOperations)
	SaveExcelFileUpload(ctx context.Context, operations database.TransactionsSQLOperations, excelFileUpload *entities.ExcelFileUpload) error
	UpdateExcelFileUpload(ctx context.Context, transactionsDB *database.AppTransactionsDB, excelFileUploadID int64, form *forms.UpdateExcelFileUploadForm) (*entities.ExcelFileUpload, error)
}

type AppExcelFileUploadService struct {
	etimsProductionAPIURL     string
	etimsSandboxAPIURL        string
	etimsVSCU                 providers.EtimsVSCU
	excelFileUploadRepository repos.ExcelFileUploadRepository
	invoiceRepository         repos.InvoiceRepository
	storeRepository           repos.StoreRepository
	userRepository            repos.UserRepository
}

func NewExcelFileUploadService(
	etimsProductionAPIURL string,
	etimsSandboxAPIURL string,
	etimsVSCU providers.EtimsVSCU,
	excelFileUploadRepo repos.ExcelFileUploadRepository,
	invoiceRepository repos.InvoiceRepository,
	storeRepository repos.StoreRepository,
	userRepository repos.UserRepository,
) ExcelFileUploadService {
	return &AppExcelFileUploadService{
		etimsProductionAPIURL:     etimsProductionAPIURL,
		etimsSandboxAPIURL:        etimsSandboxAPIURL,
		etimsVSCU:                 etimsVSCU,
		excelFileUploadRepository: excelFileUploadRepo,
		invoiceRepository:         invoiceRepository,
		storeRepository:           storeRepository,
		userRepository:            userRepository,
	}
}

func (s *AppExcelFileUploadService) CreateExcelFileUpload(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	form *forms.CreateExcelFileUploadForm,
) (*entities.ExcelFileUpload, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.ExcelFileUpload{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find user by id=[%v]",
			tokenInfo.UserID,
		)
	}

	store, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	if err != nil && utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return &entities.ExcelFileUpload{}, err
	}

	form.StoreID = &store.ID

	excelFileUpload := &entities.ExcelFileUpload{
		CreatedBy:      user.ID,
		FileName:       form.FileName,
		OrganizationID: user.OrganizationID,
		Status:         entities.FileProcessingStatusPending,
		Store:          store,
		StoreID:        store.ID,
		UUID:           utils.GenerateAssetUUID(),
	}

	err = s.excelFileUploadRepository.Save(ctx, excelFileUpload)
	if err != nil {
		return excelFileUpload, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to create excel file upload with description=[%v]",
			form.FileName,
		)
	}

	return excelFileUpload, nil
}

func (s *AppExcelFileUploadService) DeleteExcelFileUpload(
	ctx context.Context,
	keyID int64,
) (*entities.ExcelFileUpload, error) {

	excelFileUpload := &entities.ExcelFileUpload{}
	var err error

	excelFileUpload, err = s.excelFileUploadRepository.FindByID(ctx, keyID)
	if err != nil {

		if utils.IsErrNoRows(err) {
			return excelFileUpload, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("excelFileUpload not found"),
				utils.ErrorCodeResourceNotFound,
				"excelFileUpload not found",
				"excelFileUpload=[%v] not found",
				keyID,
			)
		}

		return excelFileUpload, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find excelFileUpload by id=[%v]",
			keyID,
		)
	}

	err = s.excelFileUploadRepository.Delete(ctx, excelFileUpload)
	if err != nil {
		return excelFileUpload, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to delete excelFileUpload by id=[%v]",
			keyID,
		)
	}

	return excelFileUpload, nil
}

func (s *AppExcelFileUploadService) FilterExcelFileUploads(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.ExcelFileUploadList, error) {

	excelFileUploadList := &entities.ExcelFileUploadList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)
	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return excelFileUploadList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find user by id=[%v]",
			tokenInfo.UserID,
		)
	}

	filter.UserID = user.ID

	store, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	if err != nil && utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return excelFileUploadList, err
	}

	filter.StoreID = store.ID

	count, err := s.excelFileUploadRepository.CountExcelFileUploads(ctx, filter)
	if err != nil {
		fmt.Printf("Unable to count excelFileUploads, err=[%v]\n", err.Error())
		return excelFileUploadList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to count excel file uploads for user=[%v]",
			tokenInfo.UserID,
		)
	}

	excelFileUploads, err := s.excelFileUploadRepository.FilterExcelFileUploads(ctx, operations, filter)
	if err != nil {
		fmt.Printf("Unable to filter excelFileUploads, err=[%v]\n", err.Error())
		return excelFileUploadList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to filter excel file uploads for user=[%v]",
			tokenInfo.UserID,
		)
	}

	storeIDs := make([]int64, len(excelFileUploads))
	for index, excelFileUpload := range excelFileUploads {
		storeIDs[index] = excelFileUpload.StoreID
	}

	stores, err := s.storeRepository.FindByIDs(ctx, operations, storeIDs)
	if err != nil {
		return excelFileUploadList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find stores for user=[%v]",
			tokenInfo.UserID,
		)
	}

	storeMap := make(map[int64]*entities.Store)
	for _, store := range stores {
		storeMap[store.ID] = store
	}

	for _, excelFileUpload := range excelFileUploads {
		if _, exists := storeMap[excelFileUpload.StoreID]; exists {
			excelFileUpload.Store = storeMap[excelFileUpload.StoreID]
		}
	}

	excelFileUploadList.ExcelFileUploads = excelFileUploads

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	excelFileUploadList.Pagination = pagination

	return excelFileUploadList, nil
}

func (s *AppExcelFileUploadService) FindExcelFileUploadByID(
	ctx context.Context,
	keyID int64,
) (*entities.ExcelFileUpload, error) {

	excelFileUpload := &entities.ExcelFileUpload{}
	var err error

	// tokenInfo := ctxhelper.TokenInfo(ctx)

	// user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	// if err != nil {
	// 	return &entities.ExcelFileUpload{}, apperr.Wrap(
	// 		err,
	// 	).AddLogMessagef(
	// 		"Failed to find user by id=[%v]",
	// 		tokenInfo.UserID,
	// 	)
	// }

	excelFileUpload, err = s.excelFileUploadRepository.FindByID(ctx, keyID)
	if err != nil {

		if utils.IsErrNoRows(err) {
			return excelFileUpload, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("excelFileUpload not found"),
				utils.ErrorCodeResourceNotFound,
				"excelFileUpload not found",
				"excelFileUpload=[%v] not found",
				keyID,
			)
		}

		return excelFileUpload, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find excelFileUpload by id=[%v]",
			keyID,
		)
	}

	store, err := s.storeRepository.FindByID(ctx, excelFileUpload.StoreID)
	if err != nil {
		return excelFileUpload, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find store by id=[%v]",
			excelFileUpload.StoreID,
		)
	}

	excelFileUpload.Store = store

	return excelFileUpload, nil
}

func (s *AppExcelFileUploadService) GetExcelFileUploadByUUID(
	ctx context.Context,
	key string,
) (*entities.ExcelFileUpload, error) {

	// tokenInfo := ctxhelper.TokenInfo(ctx)

	// user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	// if err != nil {
	// 	return &entities.ExcelFileUpload{}, apperr.Wrap(
	// 		err,
	// 	).AddLogMessagef(
	// 		"Failed to find user by id=[%v]",
	// 		tokenInfo.UserID,
	// 	)
	// }

	excelFileUpload, err := s.excelFileUploadRepository.FindByKey(ctx, key)
	if err != nil {
		return excelFileUpload, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find excelFileUpload by key=[%v]",
			key,
		)
	}
	store, err := s.storeRepository.FindByID(ctx, excelFileUpload.StoreID)
	if err != nil {
		return excelFileUpload, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find store by id=[%v]",
			excelFileUpload.StoreID,
		)
	}

	excelFileUpload.Store = store

	return excelFileUpload, nil
}

func (s *AppExcelFileUploadService) ProcessFileUploads(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
) {

	filter := &entities.PaginationFilter{
		Limit:  5,
		Offset: 0,
		Status: entities.FileProcessingStatusPending.String(),
	}

	excelFileUploads, err := s.excelFileUploadRepository.FilterExcelFileUploads(ctx, operations, filter)
	if err != nil && !utils.IsErrNoRows(err) {
		fmt.Printf("Unable to filter excelFileUploads, err=[%v]\n", err.Error())
	}

	if len(excelFileUploads) == 0 {
		return
	}

	processingStatus := entities.FileProcessingStatusProcessed

	for _, excelFileUpload := range excelFileUploads {
		fmt.Printf("processing excel file=[%v]\n", excelFileUpload.FileName)

		// Read all data , sign and save it to db - invoices table.
		err := s.processExcelFile(ctx, operations, excelFileUpload)
		if err != nil {
			fmt.Printf("error processing excel file=[%v], err=[%v]\n", excelFileUpload.FileName, err)
			processingStatus = entities.FileProcessingStatusPending
		}

		// Update excel file that it has being processed
		excelFileUpload.Status = processingStatus
		err = s.excelFileUploadRepository.Save(ctx, excelFileUpload)
		if err != nil {
			fmt.Printf("error saving excel file=[%v], err=[%v]\n", excelFileUpload.FileName, err)
		}
	}
}

func (s *AppExcelFileUploadService) SaveExcelFileUpload(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	excelFileUpload *entities.ExcelFileUpload,
) error {

	err := s.excelFileUploadRepository.Save(ctx, excelFileUpload)
	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to save excelFileUpload=[%v]",
			excelFileUpload.FileName,
		)
	}

	return nil
}

func (s *AppExcelFileUploadService) UpdateExcelFileUpload(
	ctx context.Context,
	transactionsDB *database.AppTransactionsDB,
	excelFileUploadID int64,
	form *forms.UpdateExcelFileUploadForm,
) (*entities.ExcelFileUpload, error) {

	excelFileUpload, err := s.excelFileUploadRepository.FindByID(ctx, excelFileUploadID)
	if err != nil {
		return excelFileUpload, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find excelFileUpload by id=[%v]",
			excelFileUploadID,
		)
	}

	excelFileUpload.Status = entities.FileProcessingStatus(form.Status)

	err = s.excelFileUploadRepository.Save(ctx, excelFileUpload)
	if err != nil {
		return excelFileUpload, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to update excelFileUpload by id=[%v]",
			excelFileUploadID,
		)
	}

	return excelFileUpload, nil
}

func (s *AppExcelFileUploadService) processExcelFile(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	excelFileUpload *entities.ExcelFileUpload,
) error {

	var recordsCount int64

	// Open the Excel file
	fileName := fmt.Sprintf("./uploads/%v", excelFileUpload.FileName)
	f, err := excelize.OpenFile(fileName)
	if err != nil {
		log.Fatalf("Failed to open Excel file: %v", err)
		return err
	}

	// Get all the sheet names
	sheetList := f.GetSheetMap()
	fmt.Printf("Sheets: %v\n", sheetList)

	// Read the content of the first sheet (you can specify any sheet name)
	// sheetName := sheetList[0] // Read the first sheet
	rows := f.GetRows("VAT_Format(Input)")

	store, err := s.storeRepository.FindByID(ctx, excelFileUpload.StoreID)
	if err != nil && utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve store by id=[%v], err=[%v]\n", excelFileUpload.StoreID, err)
		return err
	}

	// Iterate over the rows and columns
	// Create invoices and invoice items, and sign them
	// Skip the first row containing headers
	for i, row := range rows {

		invoice := &entities.Invoice{
			DateCreated: time.Now(),
			StoreID:     &store.ID,
		}

		etimsItem := &entities.EtimsItem{
			ItemTyCd:    entities.EtimsProductTypeService.String(),
			BhfID:       "00",
			IsrcAplcbYn: "N",
			ModrID:      "Admin",
			ModrNm:      "Admin",
			OrgnNatCd:   "KE",
			PkgUnitCd:   "BG",
			QtyUnitCd:   "U",
			RegrID:      "Admin",
			RegrNm:      "Admin",
			SftyQty:     1,
			TaxTyCd:     entities.EtimsTaxTypeB.String(),
			Tin:         "P052223456Z",
			UseYn:       "Y",
		}
		etimsItem.Timestamps.Touch()

		item := &entities.InvoiceItem{}
		item.Timestamps.Touch()

		for j, colCell := range row {
			row := i + 1
			column := j + 1
			fmt.Printf("Row %d, Column %d: %s\n", row, column, colCell)

			if i > 0 { // Ingore the first row containing columnn headers
				switch column {
				case 1: // Customer name
					invoice.CustomerName = colCell
				case 2: // Customer pin
					invoice.CustomerTIN = colCell
					invoice.CustomerTIN = ""
				case 3: // Voucher ID
					invoice.InvoiceNumber = colCell
				case 4: // Customer Serial Number
					invoice.CustomerNumber = colCell
				case 5: // Invoice Type
					documentTypeCode := strings.ToUpper(*utils.String(colCell))
					if documentTypeCode == "R" {
						invoice.IsCreditNote = true
					}
					invoice.DocumentTypeCode = &documentTypeCode
				case 6: // Quantity
					etimsItem.SftyQty = utils.ConvertStringToFloat64(colCell)
				case 7: // Item Name
					itemName := strings.TrimSpace(colCell)
					etimsItem.AddInfo = utils.GenerateItemCode(itemName, 7)
					etimsItem.ItemCd = utils.GenerateItemCode(itemName, 20)
					etimsItem.ItemClsCd = utils.GenerateItemCode(itemName, 5)
					etimsItem.ItemNm = itemName
					etimsItem.ItemStdNm = itemName
				case 8: // Amount
					unitPrice := utils.ConvertStringToFloat64(colCell)
					etimsItem.DftPrc = unitPrice
					invoice.GrandTotal = unitPrice
				case 9: // Tax
					taxCode := colCell
					vatAmtB := 0.00

					if strings.ToUpper(taxCode) == "B" { // B-16.00%
						vatAmt := invoice.GrandTotal * 0.138
						vatAmtB = vatAmt
					}
					invoice.Vat = vatAmtB

					invoice.EtimsItems = append(invoice.EtimsItems, etimsItem)

					item.Amount = etimsItem.DftPrc
					item.ItemID = int(recordsCount)
					item.ItemName = etimsItem.ItemNm
					item.Name = etimsItem.ItemNm
					item.Quantity = etimsItem.SftyQty
					item.SerialNumber = fmt.Sprintf("%v", recordsCount)
					item.Total = invoice.GrandTotal

					invoice.InvoiceItems = append(invoice.InvoiceItems, item)

					err = s.signInvoiceViaKRAETIMS(ctx, operations, store, invoice)
					if err != nil {
						fmt.Printf("error signing invoice, err=[%v]\n", err)
					}
				}
			}
		}

		if i > 1 {
			recordsCount++
		}
	}

	excelFileUpload.RecordsCount = recordsCount

	return nil
}

func (s *AppExcelFileUploadService) signInvoiceViaKRAETIMS(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	store *entities.Store,
	invoice *entities.Invoice,
) error {

	fmt.Printf("Signing invoice=[%v] via etims...\n", invoice.InvoiceNumber)

	timeNow := time.Now()
	commonDate := utils.FormatTime(timeNow, "20060102030405") // yyyyMMddhhmmss

	itemsCount := len(invoice.InvoiceItems)

	customerName := invoice.CustomerName
	if len(customerName) >= 20 {
		customerName = customerName[:19]
	}

	storeName := store.Name
	if len(storeName) >= 20 {
		storeName = storeName[:19]
	}

	trimmedTraderName := strings.ReplaceAll(store.Name, " ", "")
	if len(trimmedTraderName) >= 20 {
		trimmedTraderName = trimmedTraderName[:19]
	}

	etimsReceiptForm := &forms.EtimsReceiptForm{
		Adrs:         "Nairobi,Kenya",
		BtmMsg:       store.Name,
		CustMblNo:    invoice.CustomerPhone,
		CustTin:      invoice.CustomerTIN,
		PrchrAcptcYn: "Y",
		RptNo:        invoice.ID,
		TopMsg:       storeName,
		TrdeNm:       customerName,
	}

	var etimsItems []*forms.EtimsItemForm
	itemSeqCounter := 1

	for _, etimsItem := range invoice.EtimsItems {

		vatFloat64 := 0.00

		if strings.ToUpper(etimsItem.TaxTyCd) == "B" {
			vat := etimsItem.DftPrc * 0.1379
			vatStr := fmt.Sprintf("%.2f", vat)
			vatFloat64 = utils.ConvertStringToFloat64(vatStr)
		}

		taxableAmount := etimsItem.DftPrc * etimsItem.SftyQty
		taxableAmountStr := fmt.Sprintf("%.2f", taxableAmount)
		taxableAmountFloat64 := utils.ConvertStringToFloat64(taxableAmountStr)

		etimsItem := &forms.EtimsItemForm{
			DcAmt:     etimsItem.DcAmt,
			DcRt:      etimsItem.DcRt,
			ItemSeq:   itemSeqCounter,
			ItemCd:    etimsItem.ItemCd,
			ItemClsCd: etimsItem.ItemClsCd,
			ItemNm:    etimsItem.ItemNm,
			Bcd:       etimsItem.Bcd,
			PkgUnitCd: etimsItem.PkgUnitCd,
			Pkg:       etimsItem.SftyQty,
			QtyUnitCd: etimsItem.QtyUnitCd,
			Qty:       etimsItem.SftyQty,
			Prc:       etimsItem.DftPrc,
			SplyAmt:   utils.RoundFloat64(taxableAmount, 2),
			IsrccCd:   "N",
			TaxTyCd:   etimsItem.TaxTyCd,
			TaxblAmt:  etimsItem.DftPrc,
			TaxAmt:    vatFloat64,
			TotAmt:    taxableAmountFloat64,
		}

		etimsItems = append(etimsItems, etimsItem)
		itemSeqCounter++
	}

	// invoiceNumber := invoice.InvoiceNumber
	invoiceNumber := utils.ConvertStringToInt64(invoice.InvoiceNumber)

	// totalTaxableAmount := invoice.GrandTotal - invoice.Vat
	// totalTaxableAmountStr := fmt.Sprintf("%.2f", totalTaxableAmount)
	totalTaxableAmountFloat64 := invoice.GrandTotal

	totalTaxableAmountFloat64 = utils.RoundFloat64(totalTaxableAmountFloat64, 2)
	totalVat := utils.RoundFloat64(invoice.Vat, 2)
	taxAmountB := utils.RoundFloat64(invoice.GrandTotal, 2)

	// Construct etims sale form
	form := &forms.CreateEtimsSalesForm{
		Tin:          store.PIN,
		BhfID:        store.EtimsBranch,
		TrdInvcNo:    invoiceNumber,
		InvcNo:       invoiceNumber,
		OrgInvcNo:    invoiceNumber,
		CustTin:      invoice.CustomerTIN,
		CustNm:       invoice.CustomerName,
		SalesTyCd:    "N",
		RcptTyCd:     "S", // S for Sale, R for Credit Note
		PmtTyCd:      entities.ETIMSPaymentMethodCash.String(),
		SalesSttsCd:  entities.TransactionProgressApproved.String(),
		CfmDt:        commonDate,
		SalesDt:      utils.FormatTime(timeNow, "20060102"), // yyyyMMdd
		StockRlsDt:   commonDate,
		TotItemCnt:   itemsCount,
		TaxblAmtA:    0.00,
		TaxblAmtB:    taxAmountB,
		TaxblAmtC:    0.00,
		TaxblAmtD:    0.00,
		TaxblAmtE:    0.00,
		TaxRtA:       0.00,
		TaxRtB:       16.00,
		TaxRtC:       0.00,
		TaxRtD:       0.00,
		TaxRtE:       8.00,
		TaxAmtA:      invoice.VatA,
		TaxAmtB:      totalVat,
		TaxAmtC:      invoice.VatC,
		TaxAmtD:      0.00,
		TaxAmtE:      invoice.VatE,
		TotTaxAmt:    totalVat,
		TotTaxblAmt:  totalTaxableAmountFloat64,
		TotAmt:       invoice.GrandTotal,
		TrdeNm:       trimmedTraderName,
		PrchrAcptcYn: "N",
		Remark:       fmt.Sprintf("Sale invoice number %v", invoice.InvoiceNumber),
		RegrID:       "11999",
		RegrNm:       trimmedTraderName,
		ModrID:       "45678",
		ModrNm:       trimmedTraderName,
		Receipt:      etimsReceiptForm,
		ItemList:     etimsItems,
	}

	if invoice.IsCreditNote {
		form.RcptTyCd = "R" // S for Sale, R for Credit Note
	}

	// For debugging purposes only
	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal VFMS sales form payload, err=[%v]\n", err)
		return err
	}
	utils.PrettyPrintJSON(payloadBytes)

	etimsVscu := providers.NewEtimsVSCUWithParams(
		os.Getenv("ETIMS_API_BASE_URL"),
		store.EtimsBranch,
		store.EtimsDeviceSerial,
		store.PIN,
	)

	if store.EtimsEnvironment == "production" {
		productionETIMSVSCUurl := os.Getenv("VSCU_API_URL_PRODUCTION") // "http://**************:8098"
		etimsVscu = providers.NewEtimsVSCUWithParams(
			productionETIMSVSCUurl,
			store.EtimsBranch,
			store.EtimsDeviceSerial,
			store.PIN,
		)
	}

	// Send to etims vscu provider
	response, err := etimsVscu.SaveSale(form)
	if err != nil {
		logger.Errorf("failed to post sale to etims, err=[%v]\n", err)
		return err
	}

	fmt.Printf("etims save sale response code=[%v]\n", response.ResultCd)
	// utils.PrintStructToConsole(response)

	if response.Status == 400 {
		logger.Errorf("failed to post sale to etims, err=[%v]\n", err)
		return err
	}

	if response.ResultCd == "000" {
		// Update signature and verification url only on successful KRA response
		invoice.EtimsInternalData = &response.Data.IntrlData
		invoice.EtimsReceiptSignature = &response.Data.RcptSign
		invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
		invoice.EtimsTotalReceiptNumber = &response.Data.RcptNo
		invoice.EtimsVSCUReceiptPublicationDate = &response.Data.VsdcRcptPbctDate

		// signature := fmt.Sprintf("%v/%v %v %v", response.Data.SdcID, response.Data.RcptNo, response.Data.RcptSign, response.Data.VsdcRcptPbctDate)
		invoice.Signature = response.Data.RcptSign
		invoice.SignedDate = utils.FormatTime(time.Now(), "20060102150405")

	} else if response.ResultCd == "924" && invoice.Signature == "" && invoice.VerificationURL == "" {
		// Update signature and verification url only on successful KRA response
		invoice.EtimsInternalData = &response.Data.IntrlData
		invoice.EtimsMrcNumber = &response.Data.MrcNo
		invoice.EtimsReceiptSignature = &response.Data.RcptSign
		invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
		invoice.EtimsTotalReceiptNumber = &response.Data.RcptNo
		invoice.EtimsVSCUReceiptPublicationDate = &response.Data.VsdcRcptPbctDate

	} else {
		return err
	}

	if response.Data != nil {
		invoice.EtimsInternalData = &response.Data.IntrlData
		invoice.EtimsMrcNumber = &response.Data.MrcNo
		invoice.EtimsReceiptNumber = &response.Data.RcptNo
		invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
		invoice.EtimsResultCode = &response.ResultCd
		invoice.EtimsResultMessage = &response.ResultMsg
		invoice.EtimsSDCID = &response.Data.SdcID
	}

	fmt.Printf("invoice.Signature=[%v]\n", invoice.Signature)

	if invoice.Signature != "" {
		// https://etims-sbx.kra.go.ke/common/link/etims/receipt/indexEtimsReceiptData?Data=P000000010E00EIW354USAF6HTG5Q
		// Data=P000000010E (PIN) 00 (Branch ID) EIW354USAF6HTG5Q (signature from vscu)

		// To check if client is live - if live use prod, otherwise use sandbox
		apiBaseURL := s.etimsSandboxAPIURL

		if store.EtimsEnvironment == "production" {
			apiBaseURL = s.etimsProductionAPIURL
		}

		signatureData := fmt.Sprintf("%v%v%v", store.PIN, store.EtimsBranch, invoice.Signature)
		verificationURL := fmt.Sprintf("%v/common/link/etims/receipt/indexEtimsReceiptData?Data=%v", apiBaseURL, signatureData)
		invoice.VerificationURL = verificationURL

		// Generate Signature and QR Code
		if invoice.EtimsResultDateTime != nil {
			err = utils.GenerateETIMSVSCUQRCode(ctx, invoice)
			if err != nil {
				logger.Errorf("failed to generate QR Code, err=[%v]\n", err)
				return err
			}
		}

		// Save response recieved by updating the current invoice
		err = s.invoiceRepository.Save(ctx, operations, invoice)
		if err != nil {
			logger.Errorf("failed to update signed invoice, err=[%v]\n", err)
			return err
		}
	}

	return nil
}
