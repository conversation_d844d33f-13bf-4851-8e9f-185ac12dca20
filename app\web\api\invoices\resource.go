package invoices

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/webutils"
)

func downloadInvoicesExcel(
	transactionsDB *database.AppTransactionsDB,
	invoiceService services.InvoiceService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering activities.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		t := time.Now()
		dateString := t.Format("Invoices-2006-01-02-150405")
		outputFolder := "./downloads" // os.Getenv("OUTPUT_FOLDER")
		if _, err := os.Stat(outputFolder); os.IsNotExist(err) {
			err := os.Mkdir(outputFolder, os.ModePerm)
			if err != nil {
				ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Unable to access downloads directory"})
				return
			}
		}

		excelFileName := fmt.Sprintf("%v.xlsx", dateString)
		excelFilePath := fmt.Sprintf("%v//%v", outputFolder, excelFileName)

		_, err = invoiceService.DownloadInvoices(ctx.Request.Context(), transactionsDB, filter, excelFilePath)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to download invoices",
			})
			return
		}

		// ctx.JSON(http.StatusOK, invoiceList)
		file, err := os.Open(excelFilePath)
		if err != nil {
			ctx.String(http.StatusInternalServerError, fmt.Sprintf("Error opening file: %s", err))
			return
		}
		defer file.Close()

		// Set the headers for the response
		ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		ctx.Header("Content-Disposition", "attachment; filename="+excelFileName)

		// Copy the file content to the response writer
		_, err = io.Copy(ctx.Writer, file)
		if err != nil {
			ctx.String(500, fmt.Sprintf("Error copying file to response: %s", err))
			return
		}
	}
}

func filterInvoices(
	transactionsDB *database.AppTransactionsDB,
	invoiceService services.InvoiceService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering activities.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		invoiceList, err := invoiceService.FilterInvoices(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"error": err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, invoiceList)
	}
}

func getInvoiceByID(
	transactionsDB *database.AppTransactionsDB,
	invoiceService services.InvoiceService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		invoiceIDStr := ctx.Param("id")
		invoiceID, err := strconv.ParseInt(invoiceIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse invoiceID=[%v], err=[%v]\n", invoiceIDStr, err)
		}

		invoice, err := invoiceService.FindInvoiceByID(ctx.Request.Context(), transactionsDB, invoiceID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter invoices",
			})
			return
		}

		ctx.JSON(http.StatusOK, invoice)
	}
}

func getInvoiceByInvoiceNumber(
	transactionsDB *database.AppTransactionsDB,
	invoiceService services.InvoiceService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		invoiceNumberStr := ctx.Param("invoiceNumber")
		invoiceNumber, err := strconv.ParseInt(invoiceNumberStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse invoiceNumber=[%v], err=[%v]\n", invoiceNumberStr, err)
		}

		invoice, err := invoiceService.GetInvoiceByInvoiceNumber(ctx.Request.Context(), transactionsDB, invoiceNumber)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter invoices",
			})
			return
		}

		ctx.JSON(http.StatusOK, invoice)
	}
}

func getInvoicePDFFile(
	transactionsDB *database.AppTransactionsDB,
	invoiceService services.InvoiceService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		invoiceIDStr := ctx.Param("id")
		invoiceID, err := strconv.ParseInt(invoiceIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse invoiceID=[%v], err=[%v]\n", invoiceIDStr, err)
		}

		invoice, err := invoiceService.FindInvoiceByID(context.Background(), transactionsDB, invoiceID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter invoices",
			})
			return
		}

		ctx.Writer.Header().Set("Content-type", "application/pdf")
		ctx.File(invoice.FileName)
	}
}

func signInvoice(
	transactionsDB *database.AppTransactionsDB,
	invoiceService services.InvoiceService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var invoiceForm forms.InvoiceForm
		err := ctx.Bind(&invoiceForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind invoice form while signing invoice",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		invoice, err := invoiceService.SignInvoice(context.Background(), transactionsDB, &invoiceForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to sign invoice. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, invoice)
	}
}

func signInvoicesInBulk(
	transactionsDB *database.AppTransactionsDB,
	invoiceService services.InvoiceService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var bulkInvoiceForm forms.BulkInvoiceForm
		err := ctx.Bind(&bulkInvoiceForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind bulk invoice form while signing invoices",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		for _, invoiceForm := range bulkInvoiceForm.Invoices {
			// wrap in go routine
			go func() {
				_, err := invoiceService.SignInvoice(context.Background(), transactionsDB, invoiceForm)
				if err != nil {
					ctx.JSON(500, gin.H{
						"message": "unable to sign invoice. Err=" + err.Error(),
					})
					return
				}
			}()
		}

		ctx.JSON(http.StatusOK, gin.H{
			"message": "invoices signed successfully",
		})
	}
}
