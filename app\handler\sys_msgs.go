package handler

import (
	"log"
	"struts-pos-backend/app/models"
	"struts-pos-backend/app/utils"
	"net/http"

	"github.com/jinzhu/gorm"
)

// GetAllSysMsgs - GetAllSysMsgs
func GetAllSysMsgs(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	sysMsgs := []models.SysMsg{}
	db.Order("Id DESC").Find(&sysMsgs)
	respondJSON(w, http.StatusOK, sysMsgs)
}

// GetSysMsg gets a user instance if exists, or respond the 404 error otherwise
func GetSysMsg(db *gorm.DB, sysMsgID int) *models.SysMsg {
	sysMsg := models.SysMsg{}
	if err := db.First(&sysMsg, models.SysMsg{ID: sysMsgID}).Error; err != nil {
		return &sysMsg
	}
	return &sysMsg
}

// CheckForSysMsgsToSend - CheckForSysMsgsToSend
func CheckForSysMsgsToSend(db *gorm.DB) {

	for i := 0; ; i++ {

		// log.Println("Checking for unsent messages to send...")

		// Get all pending sys msgs - check status
		unsentMessages := []models.SysMsg{}
		db.Order("Id DESC").Where("status = 0 ").Limit(100).Find(&unsentMessages)

		for _, sysMsg := range unsentMessages {
			// Updated accordingly whether successful or failed
			response := models.SysMsg{}

			if sysMsg.MsgType == "EMAIL" {
				log.Println("About to Send Email ID >>> ", sysMsg.ID)
				// response = utils.SendCustomEmail(sysMsg)
				log.Println("Send Email Response    >>> ", response.Status)
			} else {
				log.Println("About to Send SMS ID >>> ", sysMsg.ID)
				response = utils.SendSMSNexmo(sysMsg)
				log.Println("Send SMS Response    >>> ", response.Status)
			}

			if response.Status == 2 {
				// db.model(&sysMsg).UpdateColumns(models.SysMsg{Status: 2, DateSent: time.Now(), TrxID: response.TrxID})
			} else if response.Status == 3 {
				// db.model(&sysMsg).UpdateColumns(models.SysMsg{Status: 3, DateSent: time.Now(), TrxID: response.TrxID})
			}

		}

		utils.DelaySeconds(2) // Check every 5 seconds
	}

}
