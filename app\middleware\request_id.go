package middleware

import (
	"struts-pos-backend/app/web/ctxhelper"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const requestIdHeaderKey = "x-request-id"

func SetRequestId() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestId := uuid.New().String()
		ctx := ctxhelper.WithRequestId(c.Request.Context(), requestId)
		c.Request = c.Request.WithContext(ctx)
		c.Header(requestIdHeaderKey, requestId)
		c.Next()
	}
}
