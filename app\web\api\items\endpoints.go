package items

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/web/auth"

	"struts-pos-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	itemService services.ItemService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/items", createItem(transactionsDB, itemService))
		protectedAPI.GET("/items", filterItems(transactionsDB, itemService))
		protectedAPI.GET("/items/:id", getItem(transactionsDB, itemService))
		protectedAPI.PUT("/items/:id", updateItem(transactionsDB, itemService))
		protectedAPI.GET("/items/download", downloadItemsExcel(transactionsDB, itemService))
	}
}
