package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
	"time"
)

const (
	countStockItemsSQL = `SELECT COUNT(*) AS count FROM etims_stock_items WHERE store_id=$1`

	insertEtimsStockItemSQL = `INSERT INTO etims_stock_items (etims_stock_id, itemSeq, itemCd, itemClsCd, itemNm, bcd, pkgUnitCd, pkg, qtyUnitCd, 
		qty, itemExprDt, prc, splyAmt, totDcAmt, taxblAmt, taxTyCd, taxAmt, totAmt, uuid, created_at, updated_at) VALUES`

	createEtimsStockItemSQL = insertEtimsStockItemSQL + ` ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, 
		$20, $21) RETURNING id`

	selectEtimsStockItemByIDSQL = selectEtimsStockItemSQL + ` WHERE id=$1`

	selectEtimsStockItemByUUIDSQL = selectEtimsStockItemSQL + ` WHERE uuid=$1`

	selectEtimsStockItemSQL = `SELECT id, etims_stock_id, itemSeq, itemCd, itemClsCd, itemNm, bcd, pkgUnitCd, pkg, qtyUnitCd, qty, itemExprDt, prc,
		splyAmt, totDcAmt, taxblAmt, taxTyCd, taxAmt, totAmt, uuid, created_at, updated_at FROM etims_stock_items`
)

type (
	EtimsStockItemRepository interface {
		Count(context.Context, *entities.PaginationFilter) (int, error)
		FilterEtimsStockItems(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.EtimsStockItem, error)
		FindByID(context.Context, int64) (*entities.EtimsStockItem, error)
		FindByStockItemCode(context.Context, string) (*entities.EtimsStockItem, error)
		Save(context.Context, *entities.EtimsStockItem) error
		SaveMultiple(context.Context, txns_db.TransactionsSQLOperations, []*entities.EtimsStockItem) error
	}

	AppEtimsStockItemRepository struct {
		db *sql.DB
	}
)

func NewEtimsStockItemRepository(db *sql.DB) EtimsStockItemRepository {
	return &AppEtimsStockItemRepository{db: db}
}

func (r *AppEtimsStockItemRepository) Count(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countStockItemsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count, nil
}

func (r *AppEtimsStockItemRepository) FilterEtimsStockItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.EtimsStockItem, error) {

	etimsStockItems := make([]*entities.EtimsStockItem, 0)

	args := make([]interface{}, 0)
	query := selectEtimsStockItemSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return etimsStockItems, err
	}

	defer rows.Close()

	for rows.Next() {
		etimsStockItem, err := r.scanRowIntoEtimsStockItem(rows)
		if err != nil {
			return etimsStockItems, err
		}

		etimsStockItems = append(etimsStockItems, etimsStockItem)
	}

	return etimsStockItems, rows.Err()
}

func (r *AppEtimsStockItemRepository) FindByID(
	ctx context.Context,
	id int64,
) (*entities.EtimsStockItem, error) {
	row := r.db.QueryRow(selectEtimsStockItemByIDSQL, id)
	return r.scanRowIntoEtimsStockItem(row)
}

func (r *AppEtimsStockItemRepository) FindByStockItemCode(
	ctx context.Context,
	uuid string,
) (*entities.EtimsStockItem, error) {
	row := r.db.QueryRow(selectEtimsStockItemByUUIDSQL, uuid)
	return r.scanRowIntoEtimsStockItem(row)
}

func (r *AppEtimsStockItemRepository) Save(
	ctx context.Context,
	etimsStockItem *entities.EtimsStockItem,
) error {

	etimsStockItem.Timestamps.Touch()

	err := r.db.QueryRow(
		createEtimsStockItemSQL,
		etimsStockItem.ID,
		etimsStockItem.EtimsStockId,
		etimsStockItem.ItemSeq,
		etimsStockItem.ItemCd,
		etimsStockItem.ItemNm,
		etimsStockItem.Bcd,
		etimsStockItem.PkgUnitCd,
		etimsStockItem.Pkg,
		etimsStockItem.QtyUnitCd,
		etimsStockItem.Qty,
		etimsStockItem.ItemExprDt,
		etimsStockItem.Prc,
		etimsStockItem.SplyAmt,
		etimsStockItem.TotDcAmt,
		etimsStockItem.TaxblAmt,
		etimsStockItem.TaxTyCd,
		etimsStockItem.TaxAmt,
		etimsStockItem.TotAmt,
		etimsStockItem.UUID,
		etimsStockItem.CreatedAt,
		etimsStockItem.UpdatedAt,
	).Scan(&etimsStockItem.ID)

	if err != nil {
		log.Printf("error saving etimsStockItem, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppEtimsStockItemRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	etimsStockItems []*entities.EtimsStockItem,
) error {

	query := insertEtimsStockItemSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(etimsStockItems))

	for index, etimsStockItem := range etimsStockItems {

		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4, currentIndex+5, currentIndex+6, currentIndex+7, currentIndex+8, currentIndex+9, currentIndex+10,
			currentIndex+11, currentIndex+12, currentIndex+13, currentIndex+14, currentIndex+15, currentIndex+16, currentIndex+17, currentIndex+18, currentIndex+19, currentIndex+20)

		currentIndex += 21

		args = append(
			args,
			etimsStockItem.EtimsStockId,
			etimsStockItem.ItemSeq,
			etimsStockItem.ItemCd,
			etimsStockItem.ItemClsCd,
			etimsStockItem.ItemNm,
			etimsStockItem.Bcd,
			etimsStockItem.PkgUnitCd,
			etimsStockItem.Pkg,
			etimsStockItem.QtyUnitCd,
			etimsStockItem.Qty,
			etimsStockItem.ItemExprDt,
			etimsStockItem.Prc,
			etimsStockItem.SplyAmt,
			etimsStockItem.TotDcAmt,
			etimsStockItem.TaxblAmt,
			etimsStockItem.TaxTyCd,
			etimsStockItem.TaxAmt,
			etimsStockItem.TotAmt,
			etimsStockItem.UUID,
			time.Now(),
			time.Now(),
		)
	}

	query += strings.Join(values, ",")
	// query += " ON CONFLICT(itemCd) DO UPDATE SET updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&etimsStockItems[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppEtimsStockItemRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND LOWER(itemNm) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(itemCd) LIKE '%%' || $%d || '%%' ", currentIndex)
		args = append(args, strings.ToLower(filter.Search))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppEtimsStockItemRepository) scanRowIntoEtimsStockItem(
	rowScanner txns_db.RowScanner,
) (*entities.EtimsStockItem, error) {

	var etimsStockItem entities.EtimsStockItem

	err := rowScanner.Scan(
		&etimsStockItem.ID,
		&etimsStockItem.EtimsStockId,
		&etimsStockItem.ItemSeq,
		&etimsStockItem.ItemCd,
		&etimsStockItem.ItemNm,
		&etimsStockItem.Bcd,
		&etimsStockItem.PkgUnitCd,
		&etimsStockItem.Pkg,
		&etimsStockItem.QtyUnitCd,
		&etimsStockItem.Qty,
		&etimsStockItem.ItemExprDt,
		&etimsStockItem.Prc,
		&etimsStockItem.SplyAmt,
		&etimsStockItem.TotDcAmt,
		&etimsStockItem.TaxblAmt,
		&etimsStockItem.TaxTyCd,
		&etimsStockItem.TaxAmt,
		&etimsStockItem.TotAmt,
		&etimsStockItem.UUID,
		&etimsStockItem.CreatedAt,
		&etimsStockItem.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning etimsStockItem,  err=[%v]\n", err.Error())
		return &etimsStockItem, err
	}

	return &etimsStockItem, nil
}
