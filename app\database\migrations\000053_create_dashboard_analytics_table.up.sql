CREATE TABLE IF NOT EXISTS dashboard_analytics (
    id                       BIGSERIAL                   PRIMARY KEY, 
    store_id                 BIGINT                      NOT NULL REFERENCES stores(id),         
    invoices_count           BIGINT                      NOT NULL,
    credit_notes_count       BIGINT                      NOT NULL,
    failed_processing_count  BIGINT                      NOT NULL,            
    stores_count             BIGINT                      NOT NULL,
    deleted_at               TIMESTAMP,
    created_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at               TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS dashboard_analytics_store_id_uidx ON dashboard_analytics(store_id);


-- Migration to populate the dashboard_analytics table with data from the existing tables
-- Iterate stores and insert a row for each store

-- Insert rows into dashboard_analytics for each store, avoiding duplicates
INSERT INTO dashboard_analytics (
    store_id,
    invoices_count,
    credit_notes_count,
    failed_processing_count,
    stores_count,
    created_at
)
SELECT
    s.id,
    0 AS invoices_count,
    0 AS credit_notes_count,
    0 AS failed_processing_count,
    0 AS stores_count,
    now() AS created_at
FROM stores s
WHERE NOT EXISTS (
    SELECT 1
    FROM dashboard_analytics da
    WHERE da.store_id = s.id
);


-- Migration to update invoices and credit notes count from invoices table using store id
-- Update dashboard_analytics with counts from invoices table
WITH invoice_data AS (
    SELECT 
        store_id,
        COUNT(*) FILTER (WHERE is_credit_note = false) AS invoices_count,
        COUNT(*) FILTER (WHERE is_credit_note = true) AS credit_notes_count
    FROM invoices
    WHERE store_id IS NOT NULL
    GROUP BY store_id
)

UPDATE dashboard_analytics da
SET 
    invoices_count = idata.invoices_count,
    credit_notes_count = idata.credit_notes_count,
    failed_processing_count = 0,
    updated_at = now()
FROM invoice_data idata
WHERE da.store_id = idata.store_id;
