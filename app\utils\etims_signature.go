package utils

import (
	"crypto/hmac"
	"crypto/sha1"
	"fmt"
	"strings"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
)

func GenerateEtimsSignature(
	invoice *entities.Invoice,
	form *forms.CreateEtimsSalesForm,
	tinBhfPath string,
) (string, error) {

	mrcNo, _ := GetKey("mrcNo", tinBhfPath)
	sdcId, _ := GetKey("sdcId", tinBhfPath)
	signKey, _ := GetKey("signKey", tinBhfPath)

	// PrintStructToConsole(form)
	rcptPbctDt := form.Receipt.RcptPbctDt
	// fmt.Printf("rcptPbctDt=[%v]\n", rcptPbctDt)

	var sb strings.Builder
	sb.WriteString(rcptPbctDt)
	sb.WriteString(form.Tin)
	sb.WriteString(form.CustTin)
	sb.WriteString(mrcNo)
	sb.WriteString(Lpad(invoice.InvoiceNumber, 10))
	sb.WriteString(" 0,00")
	sb.WriteString(LpadAmount(fmt.Sprintf("%.2f", form.TaxblAmtA), 15))
	sb.WriteString(LpadAmount(fmt.Sprintf("%.2f", form.TaxAmtA), 15))
	sb.WriteString("18,00")
	sb.WriteString(LpadAmount(fmt.Sprintf("%.2f", form.TaxblAmtB), 15))
	sb.WriteString(LpadAmount(fmt.Sprintf("%.2f", form.TaxAmtB), 15))
	sb.WriteString(" 0,00")
	sb.WriteString(LpadAmount("0,00", 15))
	sb.WriteString(LpadAmount("0,00", 15))
	sb.WriteString(" 0,00")
	sb.WriteString(LpadAmount("0,00", 15))
	sb.WriteString(LpadAmount("0,00", 15))
	sb.WriteString(form.SalesTyCd)
	sb.WriteString(form.RcptTyCd)
	sb.WriteString(sdcId)
	sb.WriteString(rcptPbctDt)
	sb.WriteString(Lpad(fmt.Sprintf("%v", invoice.InvoiceNumber), 10))
	sb.WriteString(Lpad(fmt.Sprintf("%v", invoice.InvoiceNumber), 10))

	sbString := sb.String()
	// fmt.Printf("sbString=[%v]\n", sbString)

	return hmacSha1(sbString, signKey)
}

// hmacSha1 computes HMAC-SHA1 of the given message using the provided key.
func hmacSha1(message, key string) (string, error) {

	keyBytes := EtimsDecode(key)

	mac := hmac.New(sha1.New, keyBytes)
	mac.Write([]byte(message))
	rawHmac := mac.Sum(nil)

	// Truncate to 10 bytes
	encVal := make([]byte, 10)
	copy(encVal, rawHmac[:10])

	// Encode to base32
	k := EtimsEncode(encVal)

	return k, nil
}
