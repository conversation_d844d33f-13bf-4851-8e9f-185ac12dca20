package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
)

const (
	countItemQuantitiesSQL = `SELECT COUNT(*) AS count FROM item_quantities WHERE 1=1`

	createItemQuantitySQL = `INSERT INTO item_quantities (item_id, quantity, created_at, updated_at) VALUES ($1, $2, $3, $4) RETURNING id`

	selectItemQuantityByIDSQL = selectItemQuantitySQL + ` WHERE id=$1`

	selectItemQuantitySQL = `SELECT id, item_id, quantity, created_at, updated_at FROM item_quantities`

	updateItemQuantitySQL = `UPDATE item_quantities SET quantity=$1, updated_at=$2 WHERE id=$3`
)

type (
	ItemQuantityRepository interface {
		CountItemQuantities(context.Context, *entities.PaginationFilter) int
		FilterItemQuantities(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.ItemQuantity, error)
		FindByID(context.Context, int64) (*entities.ItemQuantity, error)
		Save(context.Context, *entities.ItemQuantity) error
	}

	AppItemQuantityRepository struct {
		db *sql.DB
	}
)

func NewItemQuantityRepository(db *sql.DB) ItemQuantityRepository {
	return &AppItemQuantityRepository{db: db}
}

func (r *AppItemQuantityRepository) CountItemQuantities(
	ctx context.Context,
	filter *entities.PaginationFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countItemQuantitiesSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppItemQuantityRepository) FilterItemQuantities(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.ItemQuantity, error) {

	categories := make([]*entities.ItemQuantity, 0)

	args := make([]interface{}, 0)
	query := selectItemQuantitySQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return categories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoItemQuantity(rows)
		if err != nil {
			return categories, err
		}

		categories = append(categories, invoice)
	}

	return categories, rows.Err()
}

func (r *AppItemQuantityRepository) FindByID(
	ctx context.Context,
	itemQuantityID int64,
) (*entities.ItemQuantity, error) {
	row := r.db.QueryRow(selectItemQuantityByIDSQL, itemQuantityID)
	return r.scanRowIntoItemQuantity(row)
}

func (r *AppItemQuantityRepository) Save(
	ctx context.Context,
	itemQuantity *entities.ItemQuantity,
) error {

	itemQuantity.Timestamps.Touch()
	var err error

	if itemQuantity.IsNew() {
		err = r.db.QueryRow(
			createItemQuantitySQL,
			itemQuantity.ItemID,
			itemQuantity.Quantity,
			itemQuantity.CreatedAt,
			itemQuantity.UpdatedAt,
		).Scan(&itemQuantity.ID)

	} else {

		_, err = r.db.Exec(
			updateItemQuantitySQL,
			itemQuantity.Quantity,
			itemQuantity.UpdatedAt,
			itemQuantity.ID,
		)
	}

	if err != nil {
		log.Printf("error saving itemQuantity, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppItemQuantityRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppItemQuantityRepository) scanRowIntoItemQuantity(
	rowScanner txns_db.RowScanner,
) (*entities.ItemQuantity, error) {

	var itemQuantity entities.ItemQuantity

	err := rowScanner.Scan(
		&itemQuantity.ID,
		&itemQuantity.ItemID,
		&itemQuantity.Quantity,
		&itemQuantity.CreatedAt,
		&itemQuantity.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning itemQuantity,  err=[%v]\n", err.Error())
		return &itemQuantity, err
	}

	return &itemQuantity, nil
}
