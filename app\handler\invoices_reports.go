package handler

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"log"
	"struts-pos-backend/app/models"
	"struts-pos-backend/app/utils"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"github.com/jinzhu/gorm"
)

// RetrieveInvoiceByID - RetrieveInvoiceByID
// swagger:route GET /invoices/id/{id} Invoices invoiceRequestByID
// Gets a single invoice details for the respective invoice id - as saved in the database.
// JSON Response is provided with respective success or fail details for invoice.
// responses:
//  200: singleInvoiceResponse
//  404: jsonResponse
func RetrieveInvoiceByID(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	response := models.JSONResponse{}

	// Retrieve parsed invoice number
	vars := mux.Vars(r)
	invoiceIDString := vars["id"]
	invoiceID := utils.ConvertStringToInt(invoiceIDString)
	invoice := GetInvoiceDetails(db, invoiceID)

	if invoice.ID > 0 {

		// swaggerResponse := ConstructInvoiceReponse(db, invoice)
		respondJSON(w, http.StatusOK, nil)

	} else {

		response.Status = "01"
		response.Description = "Invoice not found!"
		respondJSON(w, 404, response)
	}

}

func DownloadInvoice(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	// response := models.JSONResponse{}
	vars := mux.Vars(r)
	invoiceIDString := vars["id"]
	invoiceID := utils.ConvertStringToInt(invoiceIDString)
	invoice := GetInvoiceDetails(db, invoiceID)

	// grab the generated receipt.pdf file and stream it to browser
	streamPDFbytes, err := ioutil.ReadFile(invoice.FileName)

	if err != nil {
		fmt.Println("Error retrieving PDF invoice file >>> ", err)
	}

	b := bytes.NewBuffer(streamPDFbytes)

	// stream straight to client(browser)
	w.Header().Set("Content-type", "application/pdf")

	if _, err := b.WriteTo(w); err != nil { // <----- here!
		fmt.Fprintf(w, "%s", err)
	}

	w.Write([]byte("PDF Generated"))
}

func CountInvoices(db *gorm.DB, reportsFilter models.ReportsFilter) int64 {

	var count int64

	if reportsFilter.StartDate.IsZero() && reportsFilter.EndDate.IsZero() {
		db.Table("invoices").Count(&count)
	} else {
		db.Table("invoices").Where("date_created BETWEEN ? and ? ",
			reportsFilter.StartDate, reportsFilter.EndDate).Count(&count)
	}

	if reportsFilter.Status == "failed" {
		db.Table("invoices").Where("signature = ? ", reportsFilter.Status).Count(&count)
	}

	if reportsFilter.Query != "query" && len(strings.TrimSpace(reportsFilter.Query)) > 0 {
		db.Table("invoices").Where("invoice_number LIKE ? ", "%"+reportsFilter.Query+"%").Count(&count)
	}

	return count
}

func AllInvoicesVatSum(db *gorm.DB, reportsFilter models.ReportsFilter) float64 {
	var result models.Result
	db.Raw("select sum(vat) as total from invoices").Scan(&result)
	return result.Total
}

func AllInvoicesTotalSum(db *gorm.DB, reportsFilter models.ReportsFilter) float64 {
	var result models.Result
	db.Raw("select sum(grand_total) as total from invoices").Scan(&result)
	return result.Total
}

func FilterInvoices(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	apiResponse := models.InvoicesRepsApiReport{}

	vars := mux.Vars(r)
	startDate := utils.FormatReportDateStringTimestamp(vars["startdate"])
	endDate := utils.FormatReportDateStringTimestamp(vars["enddate"]) // .AddDate(0, 0, 1)

	reportsFilter := models.ReportsFilter{}
	reportsFilter.StartDate = startDate
	reportsFilter.EndDate = endDate

	log.Println("Filtering reports for startDate : ", startDate, ", and endDate : ", endDate)

	invoices := []models.Invoice{}
	db.Order("Id DESC").Where("date_created >= ? AND date_created <= ? ", startDate, endDate).Find(&invoices)
	apiResponse.InvoicesCount = CountInvoices(db, reportsFilter)
	apiResponse.Invoices = invoices
	respondJSON(w, http.StatusOK, apiResponse)
}
