package invoicetemplates

import (
	"bufio"
	"errors"
	"fmt"
	"log"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/utils"
	"os"
	"strings"
)

func GetInvoiceType(txtFile string, invoice *entities.Invoice) error {
	file, err := os.Open(txtFile)
	utils.CheckError(err, "Error reading signed input file >>> "+txtFile)
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Split(bufio.ScanLines)

	var lineNumber = 1
	isValidInvoice := false

	for scanner.Scan() {

		invoiceLine := scanner.Text()
		trimedInvoiceLine := strings.Replace(invoiceLine, " ", "", -1)
		trimedInvoiceLine = strings.ToUpper(trimedInvoiceLine)

		// fmt.Printf("%v\n", invoiceLine)

		if strings.Contains(trimedInvoiceLine, "NYOTA") && strings.Contains(trimedInvoiceLine, "TANZANIA") {
			isValidInvoice = true
		}

		lineNumber++
	}

	if isValidInvoice {
		log.Println("Processing Invoice Template 1 [ONE]  >>> ", txtFile)
		err = ProcessTemplateOneInvoice(txtFile, invoice)
		if err != nil {
			fmt.Printf("unable to process template one invoice, err=[%v]\n", err)
		}
	}

	return nil
}

func ParseDataBetween(message, rawMessage, startKeyword, stopKeyword string) string {
	start, stop, err := GetStartAndEndIndices(message, startKeyword, stopKeyword)
	if err != nil {
		return ""
	}

	data := strings.TrimSpace(rawMessage[start:stop])
	if strings.HasSuffix(strings.ToLower(data), "agg") {
		data = data[0 : len(data)-3]
	}

	data = strings.TrimSpace(data)

	for strings.HasSuffix(data, ".") {
		data = strings.TrimSuffix(data, ".")
	}

	return data
}

func GetStartAndEndIndices(message, startKeyword, stopKeyword string) (int, int, error) {
	start := strings.Index(message, startKeyword)
	if start == -1 {
		return -1, -1, errors.New("cannot find start keyword")
	} else {
		start += len(startKeyword)
	}

	stop := strings.Index(message[start:], stopKeyword)
	if stop == -1 {
		return -1, -1, errors.New("cannot find stop keyword")
	} else {
		stop += start
	}

	return start, stop, nil
}

func ValidateTotals(invoice *entities.Invoice) *entities.Invoice {

	if invoice.Vat == 0 {
		return invoice
	}

	grossAmtStr := invoice.GrossAmount
	vatAmtStr := invoice.Vat

	grossAmt := invoice.GrossAmount
	vat := invoice.Vat
	// grandTotal := utils.ConvertStringToFloat64(invoice.GrandTotal)

	// fmt.Printf("grossAmt=[%v]\n", grossAmt)
	// fmt.Printf("vat=[%v]\n", vat)
	// fmt.Printf("grandTotal=[%v]\n", grandTotal)

	if vat > grossAmt {
		invoice.Vat = grossAmtStr
		invoice.GrossAmount = vatAmtStr
	}

	if invoice.GrossAmount == invoice.GrandTotal {
		invoice.Vat = 0.00
	}

	return invoice
}

func GetValueAt(invoiceLine, identifier, lookingFor string, position int) string {
	data := ""
	dataIndex := -1

	trimedInvoiceLine := strings.Replace(invoiceLine, " ", "", -1)

	// fmt.Printf("invoiceLine=[%v] \n", invoiceLine)
	// PAGE 2 OF 2 31. JANUARY 2021 PSI00003304 C01206 101-684-288 10-015917-Q
	lineArr := strings.Split(strings.TrimSpace(invoiceLine), " ")

	for i, arrItem := range lineArr {

		// fmt.Printf("arItem=[%v], identifier=[%v], i=[%v] dataindex=[%v], position=[%v] \n", arrItem, identifier, i, dataIndex, position)

		// I N V O I C E INVOICE NO: 1-1-81266 P O NO:
		if strings.TrimSpace(arrItem) == identifier {
			// fmt.Printf("Found arrItem=[%v], identifier=[%v], i=[%v], dataIndex=[%v] \n", arrItem, identifier, i, dataIndex)
			dataIndex = i + position
		}

		if i == dataIndex {

			data = cleanData(arrItem)
			// fmt.Printf("Found data index data=[%v], i=[%v], dataIndex=[%v], ", data, i, dataIndex)

			if data == "" {
				// fmt.Println("Incrementing i...")
				dataIndex = i + 1
			} else {
				break
			}
		}
	}

	if data == "" && strings.Contains(trimedInvoiceLine, "PO") {
		// I N V OI C E I NVOI CE N O: 1- 1- 73896 PO N O:
		invoiceNumber := ParseDataBetween(trimedInvoiceLine, trimedInvoiceLine, ":", "PO")
		data = invoiceNumber
	}

	data = cleanData(data)

	if len(data) < 5 && lookingFor == "invoice_no" {
		data = ""
	}

	// fmt.Printf("GetValueAt invoiceNo data=[%v] \n", data)
	return data
}

func cleanData(data string) string {
	data = strings.Replace(data, " ", "", -1)
	data = strings.Replace(data, ":", "", -1)
	data = strings.Replace(data, ".", "", -1)
	return data
}

func CleanAmount(amount string) string {
	amount = strings.Replace(amount, "(TSH)", "", -1)
	amount = strings.Replace(amount, "TOTAL", "", -1)
	amount = strings.Replace(amount, "TSH", "", -1)
	amount = strings.Replace(amount, ",", "", -1)
	amount = strings.Replace(amount, " ", "", -1)
	return amount
}

func ProcessCustomerName(invoiceLine, trimedInvoiceLine string) string {

	invoiceLine = strings.Replace(invoiceLine, "SOLD TO", "", -1)

	breakPoint := "BOX"
	if strings.Contains(trimedInvoiceLine, "LIMITED") {
		breakPoint = "LIMITED"
	} else if strings.Contains(trimedInvoiceLine, "LTD") {
		breakPoint = "LTD"
	} else if strings.Contains(trimedInvoiceLine, "BOX") {
		breakPoint = "BOX"
	} else if strings.Contains(trimedInvoiceLine, "PO") {
		breakPoint = "PO"
	}

	lineArr := strings.Split(strings.TrimSpace(invoiceLine), " ")
	customerName := ""

	// LogMessage(fmt.Sprintf("custome name breakPoint=[%v]\n", breakPoint))
	for _, str := range lineArr {
		if str == breakPoint {
			break
		}

		customerName += str + " "
	}

	if breakPoint == "LIMITED" || breakPoint == "LTD" {
		customerName += breakPoint
	}

	LogMessage(fmt.Sprintf("customerName=[%v]\n", customerName))
	return customerName
}

func ProcessTotals(invoice *entities.Invoice, invoiceLine string) (bool, error) {

	LogMessage(fmt.Sprintf("Processing totals line=[%v]", invoiceLine))
	foundGrandTotal := false
	lineArr := strings.Split(strings.TrimSpace(invoiceLine), " ")
	arrLen := len(lineArr)
	var grossAmt, vat, grandTotal string

	var vatAmtFloat, grossAmtFloat, grandTotalFloat float64

	if arrLen == 2 {

		grandTotal = lineArr[arrLen-1]
		grandTotal = strings.Replace(grandTotal, ",", "", -1)

		grandTotalFloat = utils.ConvertStringToFloat64(grandTotal)
		if grandTotalFloat > 0 {

			invoice.Vat = 0.00
			invoice.GrandTotal = grandTotalFloat
			foundGrandTotal = true
		}

	} else if arrLen > 2 {

		grossAmt, vat, grandTotal = lineArr[arrLen-3], lineArr[arrLen-2], lineArr[arrLen-1]

		grossAmt = strings.Replace(grossAmt, ",", "", -1)
		vat = strings.Replace(vat, ",", "", -1)
		grandTotal = strings.Replace(grandTotal, ",", "", -1)

		grossAmtFloat = utils.ConvertStringToFloat64(grossAmt)
		if grossAmtFloat > 0 {
			invoice.GrossAmount = grossAmtFloat
		}

		vatAmtFloat = utils.ConvertStringToFloat64(vat)
		if vatAmtFloat > 0 {
			invoice.Vat = vatAmtFloat
		}

		grandTotalFloat = utils.ConvertStringToFloat64(grandTotal)
		if grandTotalFloat > 0 {
			invoice.GrandTotal = grandTotalFloat
			foundGrandTotal = true
		}
	}

	// grossAmtFloat = utils.ConvertStringToFloat64(grossAmt)
	// vatAmtFloat = utils.ConvertStringToFloat64(vat)
	// grossPlusVat := grossAmtFloat + vatAmtFloat

	// fmt.Printf("grossAmt: [%v]\n", grossAmt)
	// fmt.Printf("vat: [%v]\n", vat)
	// fmt.Printf("grandTotal: [%v]\n\n", grandTotal)

	// fmt.Printf("grandTotalFloat: [%v]\n", grandTotalFloat)
	// fmt.Printf("grossPlusVat: [%v]\n", grossPlusVat)

	// if grandTotalFloat > grossPlusVat {
	// 	fmt.Printf("grandTotalFloat > grossPlusVat   setting vat to 0.00\n")
	// 	invoice.GrossAmount = grandTotalFloat
	// 	invoice.Vat = 0.00
	// }

	return foundGrandTotal, nil
}

func TrimAt(line, identifier string) string {
	newStr := ""
	line = strings.Replace(line, ":", " ", -1)
	line = strings.TrimSpace(line)
	lineArr := strings.Split(line, " ")
	for _, arrItem := range lineArr {
		newStr += arrItem + " "
		if arrItem == identifier {
			break
		}
	}
	return newStr
}

func SanitizeCustomerName(customerName string) string {
	if strings.Contains(customerName, "LIMITED") {
		customerName = TrimAt(customerName, "LIMITED")

	} else if strings.Contains(customerName, "SERVICES") {
		customerName = TrimAt(customerName, "SERVICES")

	} else if strings.Contains(customerName, "COMMISSION") {
		customerName = TrimAt(customerName, "COMMISSION")

	} else if strings.Contains(customerName, "COMMISSION") {
		customerName = TrimAt(customerName, "COMMISSION")

	} else if strings.Contains(customerName, "UNIVERSITY") {
		customerName = TrimAt(customerName, "UNIVERSITY")

	} else if strings.Contains(customerName, "ELEMENTARY") {
		customerName = TrimAt(customerName, "ELEMENTARY")

	} else if strings.Contains(customerName, "LTD") {
		customerName = TrimAt(customerName, "LTD")
	}

	customerName = strings.Replace(customerName, "(", "", -1)
	customerName = strings.Replace(customerName, ")", "", -1)

	return customerName
}

func SanitizeCustomerTIN(invoiceLine string) (string, bool) {
	isCustomerTinValid := false
	customerTIN := strings.Replace(invoiceLine, " ", "", -1)
	customerTIN = strings.Replace(customerTIN, "VAT", "", -1)
	customerTIN = strings.Replace(customerTIN, "IDENTIFIER", "", -1)
	customerTIN = strings.Replace(customerTIN, "-", "", -1)

	if len(customerTIN) == 9 && utils.ConvertStringToInt64(customerTIN) > 0 {
		isCustomerTinValid = true
	}

	return customerTIN, isCustomerTinValid
}

func LogMessage(message string) {

	var LOG_MESSAGE bool

	if os.Getenv("ENVIRONMENT") == "dev" {
		LOG_MESSAGE = true
	}

	if LOG_MESSAGE {
		fmt.Printf("%v\n", message)
	}
}
