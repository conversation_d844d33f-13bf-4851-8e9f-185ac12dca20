package cronjobs

import (
	"fmt"
	"os"
	"runtime"
	"strings"
	"struts-pos-backend/app/providers"
	"struts-pos-backend/app/utils"
	"time"
)

func RestartETIMSVSCU(smtpSender providers.SmtpSender) error {
	hostOS := runtime.GOOS

	// list etims vscu pid's
	osCmds := []string{"ps", "-a", "|", "grep", "etims"} // ps -au | grep etims

	switch hostOS {
	case "linux":
		osCmds = []string{"ps", "-aux", "|", "grep", "etims"}

	case "darwin":
		osCmds = []string{"ps", "-a", "|", "grep", "etims"}

	case "windows":
		osCmds = []string{"ps", "-a", "|", "grep", "etims"}
	}

	cmdOutput, err := utils.RunOSLevelCommand(strings.Join(osCmds, " "))
	if err != nil {
		fmt.Printf("error listing running etims applications, err=[%v] \n", err)
		return err
	}

	// Read through the response and pick the pid'S
	pidDataArr := strings.Split(cmdOutput, "\n")
	pidsToKill := []int64{}

	for _, pidData := range pidDataArr {
		applicationData := strings.TrimSpace(pidData)
		if len(applicationData) > 0 && strings.Contains(applicationData, "etims-vscu-") {

			fmt.Printf("app pid data=[%v]\n", applicationData)
			lineArr := strings.Split(applicationData, " ")

			if utils.ConvertStringToInt64(lineArr[0]) > 0 {
				pidsToKill = append(pidsToKill, utils.ConvertStringToInt64(lineArr[0]))
			} else if utils.ConvertStringToInt64(lineArr[0]) > 0 {
				pidsToKill = append(pidsToKill, utils.ConvertStringToInt64(lineArr[1]))
			}
		}
	}

	fmt.Printf("pidsToKill=[%+v]\n", pidsToKill)
	// Kill the pid's
	for _, pid := range pidsToKill {
		pidStr := fmt.Sprintf("%v", pid)
		osCmds := []string{"kill", "-9", pidStr} // kill -9 pid
		cmdOutput, err = utils.RunOSLevelCommand(strings.Join(osCmds, " "))
		if err != nil {
			fmt.Printf("error killing running etims processes, err=[%v] \n", err)
			return err
		}

		fmt.Printf("kill pid=[%v] cmd res=[%v]\n", pidStr, cmdOutput)
	}

	err = startEtimsApplications()
	if err != nil {
		fmt.Printf("error starting etims applications, err=[%v] \n", err)
		return err
	}

	// sendEmailOnSuccessfulETIMSApplicationsRestarts(smtpSender)

	return nil
}

func startEtimsApplications() error {
	// start etims vscu - sandbox
	sandboxJarFilePath := os.Getenv("ETIMS_VSCU_SANDBOX_JAR_FILE")
	fmt.Printf("starting etims vscu sandbox jar file=[%v]\n", sandboxJarFilePath)
	osCmds := []string{"java", "-jar", sandboxJarFilePath, "&"}
	if runtime.GOOS == "darwin" || runtime.GOOS == "linux" {
		osCmds = []string{"nohup", "java", "-jar", sandboxJarFilePath, "&"}
	}
	cmdOutput, err := utils.RunOSLevelCommand(strings.Join(osCmds, " "))
	if err != nil {
		fmt.Printf("error starting etims sandbox jar file, err=[%v] \n", err)
		return err
	}
	fmt.Printf("start etims sandbox vscu response=[%v]\n", cmdOutput)

	// start etims vscu - prod
	productionJarFilePath := os.Getenv("ETIMS_VSCU_PRODUCTION_JAR_FILE")
	fmt.Printf("starting etims vscu production jar file=[%v]\n", productionJarFilePath)
	if runtime.GOOS == "darwin" || runtime.GOOS == "linux" {
		osCmds = []string{"nohup", "java", "-jar", productionJarFilePath, "&"}
	}

	cmdOutput, err = utils.RunOSLevelCommand(strings.Join(osCmds, " "))
	if err != nil {
		fmt.Printf("error starting etims production jar file, err=[%v] \n", err)
		return err
	}
	fmt.Printf("start etims production vscu response=[%v]\n", cmdOutput)

	return nil
}

func sendEmailOnSuccessfulETIMSApplicationsRestarts(smtpSender providers.SmtpSender) {
	subject := "ETIMS VSCU Applications Restart"
	to := "<EMAIL>"

	formattedDateTime := utils.FormatTime(time.Now(), "2006-01-02 03:04:05") // yyyy-MM-dd hh:mm:ss
	items := map[string]string{
		"Time": formattedDateTime,
	}

	// Send Email to admin with payment details.
	go smtpSender.SendEmailWithTemplate("optimus", "templates/etims-vscu-restart.html", to, subject, items, nil)
	fmt.Printf("Restart email sent at=[%v]\n", formattedDateTime)
}
