package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/utils"
	"time"

	txns_db "struts-pos-backend/app/database"
)

const (
	countInvoicesSQL = `SELECT COUNT(*) AS count FROM invoices WHERE store_id=$1`

	insertInvoiceSQL = `INSERT INTO invoices (invoice_number, invoice_date, signed_date, organization_id, customer_email, customer_name, customer_tin, customer_vrn, document_type_code, receipt_code,  
		gross_amount, net_amount, store_id, taxable_amount, vat, grand_total, file_name, original_file_name, signature, verification_url, etims_result_code, etims_result_message, etims_result_date_time, 
		etims_receipt_number, etims_receipt_signature, etims_total_receipt_number, etims_vscu_receipt_publication_date, etims_sdcid, etims_mrc_number, etims_internal_data, is_credit_note, created_at, updated_at) VALUES`

	createInvoiceSQL = insertInvoiceSQL + ` ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33) 
		ON CONFLICT(store_id, invoice_number, document_type_code) DO UPDATE SET gross_amount = EXCLUDED.gross_amount, net_amount = EXCLUDED.net_amount, vat = EXCLUDED.vat,  grand_total = EXCLUDED.grand_total, 
		signature = EXCLUDED.signature, verification_url = EXCLUDED.verification_url, updated_at=Now() RETURNING id`

	selectInvoiceByIDSQL = selectInvoiceSQL + ` AND id=$2`

	selectInvoiceByInvoiceNumberSQL = selectInvoiceSQL + ` AND invoice_number=$2`

	selectCreditNoteInvoiceNumberSQL = selectInvoiceSQL + ` AND is_credit_note = true AND invoice_number=$2`

	selectInvoiceSQL = `SELECT id, invoice_number, invoice_date, customer_email, customer_name, customer_tin, customer_vrn, document_type_code, etims_internal_data, etims_mrc_number, etims_receipt_number, 
		etims_receipt_signature, etims_result_code, etims_result_date_time, etims_result_message, etims_sdcid, etims_total_receipt_number, etims_vscu_receipt_publication_date, receipt_code, gross_amount, 
		net_amount, store_id, taxable_amount, vat, grand_total, file_name, original_file_name, signature, signed_date, verification_url, is_credit_note, created_at, updated_at FROM invoices WHERE store_id=$1`

	selectZReportSummarySQL = `SELECT count(*) as count, COALESCE(SUM(gross_amount), 0) as net_amount, COALESCE(SUM(vat), 0) as vat_amount, 
		COALESCE(SUM(grand_total), 0) as total_amount FROM invoices WHERE 1=1`

	updateInvoiceSQL = `UPDATE invoices SET customer_name=$1, customer_tin=$2, customer_vrn=$3, net_amount=$4, store_id=$5, vat=$6, signature=$7, signed_date=$8, verification_url=$9, 
		etims_result_code=$10, etims_result_message=$11, etims_result_date_time=$12, etims_receipt_number=$13, etims_receipt_signature=$14, etims_total_receipt_number=$15,  
		etims_vscu_receipt_publication_date=$16, etims_sdcid=$17, etims_mrc_number=$18, document_type_code=$19, etims_internal_data=$20, updated_at=$21 WHERE id=$22`

	updateMultipleInvoicesSQL = `UPDATE invoices AS c SET (is_z_reported, updated_at) = (u.is_z_reported, u.updated_at)`
)

type (
	InvoiceRepository interface {
		CountInvoices(ctx context.Context, filter *entities.PaginationFilter) (int, error)
		FilterInvoices(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) ([]*entities.Invoice, error)
		GetCreditNoteByCreditNoteNumber(ctx context.Context, creditNoteNumber string, storeID int64) (*entities.Invoice, error)
		GetInvoiceByInvoiceNumber(ctx context.Context, invoiceNumber int64, storeID int64) (*entities.Invoice, error)
		GetInvoiceByID(ctx context.Context, invoiceID int64, storeID int64) (*entities.Invoice, error)
		GetZReportTotals(ctx context.Context, operations txns_db.TransactionsSQLOperations, isZeportedCheck bool) (entities.ZReportTotals, error)
		Save(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoice *entities.Invoice) error
		SaveMultiple(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoices []*entities.Invoice) error
		UpdateMultipleInvoices(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoices []*entities.Invoice) error
	}

	AppInvoiceRepository struct {
		db *sql.DB
	}
)

func NewInvoiceRepository(db *sql.DB) InvoiceRepository {
	return &AppInvoiceRepository{db: db}
}

func (r *AppInvoiceRepository) CountInvoices(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, filter.StoreID)
	query := countInvoicesSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)

	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppInvoiceRepository) FilterInvoices(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.Invoice, error) {

	invoices := make([]*entities.Invoice, 0)

	args := make([]interface{}, 0)
	args = append(args, filter.StoreID)
	query := selectInvoiceSQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return invoices, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoInvoice(rows)
		if err != nil {
			return invoices, err
		}

		invoices = append(invoices, invoice)
	}

	return invoices, rows.Err()
}

func (r *AppInvoiceRepository) GetInvoiceByID(
	ctx context.Context,
	invoiceID int64,
	storeID int64,
) (*entities.Invoice, error) {
	row := r.db.QueryRow(selectInvoiceByIDSQL, storeID, invoiceID)
	return r.scanRowIntoInvoice(row)
}

func (r *AppInvoiceRepository) GetZReportTotals(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	isZeportedCheck bool,
) (entities.ZReportTotals, error) {

	var zReportTotals entities.ZReportTotals

	args := []interface{}{}
	query := selectZReportSummarySQL

	if isZeportedCheck {
		query += " AND is_z_reported=false"
	}

	err := operations.QueryRowContext(ctx, query, args...).Scan(
		&zReportTotals.Count,
		&zReportTotals.NetAmount,
		&zReportTotals.VatAmount,
		&zReportTotals.TotalAmount,
	)
	if err != nil {
		return zReportTotals, err
	}
	return zReportTotals, nil
}

func (r *AppInvoiceRepository) GetCreditNoteByCreditNoteNumber(
	ctx context.Context,
	creditNoteNumber string,
	storeID int64,
) (*entities.Invoice, error) {
	row := r.db.QueryRow(selectCreditNoteInvoiceNumberSQL, storeID, creditNoteNumber)
	return r.scanRowIntoInvoice(row)
}

func (r *AppInvoiceRepository) GetInvoiceByInvoiceNumber(
	ctx context.Context,
	invoiceNumber int64,
	storeID int64,
) (*entities.Invoice, error) {
	row := r.db.QueryRow(selectInvoiceByInvoiceNumberSQL, storeID, invoiceNumber)
	return r.scanRowIntoInvoice(row)
}

func (r *AppInvoiceRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	invoice *entities.Invoice,
) error {

	invoice.Timestamps.Touch()
	var err error

	if invoice.IsNew() {
		res := operations.QueryRowContext(
			ctx,
			createInvoiceSQL,
			invoice.InvoiceNumber,
			invoice.InvoiceDate,
			invoice.SignedDate,
			invoice.OrganizationID,
			invoice.CustomerEmail,
			invoice.CustomerName,
			invoice.CustomerTIN,
			invoice.CustomerVRN,
			invoice.DocumentTypeCode,
			invoice.ReceiptCode,
			invoice.GrossAmount,
			invoice.NetAmount,
			invoice.StoreID,
			invoice.TaxableAmount,
			invoice.Vat,
			invoice.GrandTotal,
			invoice.FileName,
			invoice.OriginalFileName,
			invoice.Signature,
			invoice.VerificationURL,
			invoice.EtimsResultCode,
			invoice.EtimsResultMessage,
			invoice.EtimsResultDateTime,
			invoice.EtimsReceiptNumber,
			invoice.EtimsReceiptSignature,
			invoice.EtimsTotalReceiptNumber,
			invoice.EtimsVSCUReceiptPublicationDate,
			invoice.EtimsSDCID,
			invoice.EtimsMrcNumber,
			invoice.EtimsInternalData,
			invoice.IsCreditNote,
			invoice.CreatedAt,
			invoice.UpdatedAt,
		)

		err = res.Scan(&invoice.ID)

	} else {

		_, err = operations.ExecContext(
			ctx,
			updateInvoiceSQL,
			invoice.CustomerName,
			invoice.CustomerTIN,
			invoice.CustomerVRN,
			invoice.NetAmount,
			invoice.StoreID,
			invoice.Vat,
			invoice.Signature,
			invoice.SignedDate,
			invoice.VerificationURL,
			invoice.EtimsResultCode,
			invoice.EtimsResultMessage,
			invoice.EtimsResultDateTime,
			invoice.EtimsReceiptNumber,
			invoice.EtimsReceiptSignature,
			invoice.EtimsTotalReceiptNumber,
			invoice.EtimsVSCUReceiptPublicationDate,
			invoice.EtimsSDCID,
			invoice.EtimsMrcNumber,
			invoice.DocumentTypeCode,
			invoice.EtimsInternalData,
			invoice.UpdatedAt,
			invoice.ID,
		)
	}

	if err != nil {
		log.Printf("error saving invoice, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppInvoiceRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	invoices []*entities.Invoice,
) error {

	query := insertInvoiceSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(invoices))

	for index, invoice := range invoices {
		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4, currentIndex+5, currentIndex+6, currentIndex+7, currentIndex+8, currentIndex+9,
			currentIndex+10, currentIndex+11, currentIndex+12, currentIndex+13, currentIndex+14)
		currentIndex += 15
		args = append(args, invoice.InvoiceNumber, invoice.OrganizationID, invoice.CustomerName, invoice.CustomerTIN, invoice.CustomerVRN, invoice.ReceiptCode, invoice.GrossAmount, invoice.Vat,
			invoice.GrandTotal, invoice.FileName, invoice.OriginalFileName, invoice.Signature, invoice.VerificationURL, time.Now(), time.Now())
	}

	query += strings.Join(values, ",")
	query += " ON CONFLICT(store_id, invoice_number, document_type_code) DO UPDATE SET grand_total = EXCLUDED.grand_total, updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&invoices[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppInvoiceRepository) SelectZReportSummary(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.ZReportTotals, error) {

	zReportTotals := &entities.ZReportTotals{}

	return zReportTotals, nil
}

func (r *AppInvoiceRepository) UpdateMultipleInvoices(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	invoices []*entities.Invoice,
) error {

	query := updateMultipleInvoicesSQL + " FROM (VALUES "
	values := make([]string, len(invoices))
	args := make([]interface{}, 0)
	currentIndex := 1

	for index, invoice := range invoices {
		values[index] = fmt.Sprintf("($%d::bigint, $%d::bool, $%d::timestamptz)", currentIndex, currentIndex+1, currentIndex+2)
		currentIndex += 3
		args = append(args, invoice.ID, invoice.IsZReported, time.Now())
	}

	query += strings.Join(values, ",")
	query += ") AS u(id, is_z_reported, updated_at) where u.id = c.id"
	_, err := operations.ExecContext(ctx, query, args...)

	return err
}

func (r *AppInvoiceRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.IsEmailSent.Valid {
		query += fmt.Sprintf(" AND is_email_sent = $%d ", currentIndex)
		args = append(args, filter.IsEmailSent.Bool)
		currentIndex++
	}

	if filter.IsZReported.Valid {
		query += fmt.Sprintf(" AND is_z_reported = $%d ", currentIndex)
		args = append(args, filter.IsZReported.Bool)
		currentIndex++
	}

	if len(filter.SearchCriteria) > 0 {

		switch filter.SearchCriteria {
		case "invoices_failed_to_sign_001":
			// query += " AND LENGTH(signature) < 5 AND created_at < DATEADD(day, -1, convert(date, GETDATE()))"
			query += " AND LENGTH(signature) < 5"

		case "processing_invoices":
			query += " AND LENGTH(signature) < 5"

		case "signed_invoices":
			query += " AND LENGTH(signature) > 5"
		}
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(invoice_number) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(customer_name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(signature) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(etims_internal_data) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(customer_tin) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(filter.Search))
		currentIndex++
	}

	documentType := strings.TrimSpace(strings.ToUpper(filter.DocumentType))
	if len(documentType) > 0 {
		switch documentType {
		case "CREDIT_NOTE":
			query += " AND document_type_code = 'R'"
		case "INVOICE":
			query += " AND document_type_code = 'S'"
		}
	}

	return query, args, currentIndex
}

func (r *AppInvoiceRepository) scanRowIntoInvoice(
	rowScanner txns_db.RowScanner,
) (*entities.Invoice, error) {

	var invoice entities.Invoice

	err := rowScanner.Scan(
		&invoice.ID,
		&invoice.InvoiceNumber,
		&invoice.InvoiceDate,
		&invoice.CustomerEmail,
		&invoice.CustomerName,
		&invoice.CustomerTIN,
		&invoice.CustomerVRN,
		&invoice.DocumentTypeCode,
		&invoice.EtimsInternalData,
		&invoice.EtimsMrcNumber,
		&invoice.EtimsReceiptNumber,
		&invoice.EtimsReceiptSignature,
		&invoice.EtimsResultCode,
		&invoice.EtimsResultDateTime,
		&invoice.EtimsResultMessage,
		&invoice.EtimsSDCID,
		&invoice.EtimsTotalReceiptNumber,
		&invoice.EtimsVSCUReceiptPublicationDate,
		&invoice.ReceiptCode,
		&invoice.GrossAmount,
		&invoice.NetAmount,
		&invoice.StoreID,
		&invoice.TaxableAmount,
		&invoice.Vat,
		&invoice.GrandTotal,
		&invoice.FileName,
		&invoice.OriginalFileName,
		&invoice.Signature,
		&invoice.SignedDate,
		&invoice.VerificationURL,
		&invoice.IsCreditNote,
		&invoice.CreatedAt,
		&invoice.UpdatedAt,
	)

	if err != nil && !utils.IsErrNoRows(err) {
		log.Printf("error scanning invoice,  err=[%v]\n", err.Error())
		return &invoice, err
	}

	return &invoice, nil
}
