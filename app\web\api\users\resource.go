package users

import (
	"context"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/webutils"
)

func filterUsers(
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var loginForm forms.UserLoginForm
		ctx.Bind(&loginForm)

		ctx.JSON(200, gin.H{
			"message": "All services up",
		})
	}
}

func getUser(
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		userIDStr := ctx.Param("id")
		userID, err := strconv.ParseInt(userIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse userID=[%v], err=[%v]\n", userIDStr, err)
		}

		user, err := userService.FindByID(context.Background(), userID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to find user",
			})
			return
		}

		ctx.JSON(http.StatusOK, user)
	}
}

func registerUser(
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var form forms.UserRegistrationForm
		err := ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind user registration form",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		user, err := userService.RegisterUser(ctx.Request.Context(), transactionsDB, &form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to register user. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, user)
	}
}
