package excel_file_uploads

import (
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"

	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/logger"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/webutils"
)

func createExcelFileUpload(
	transactionsDB *database.AppTransactionsDB,
	excelFileUploadService services.ExcelFileUploadService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		// Get the uploaded file from the request
		file, err := ctx.FormFile("file")
		if err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "No file is received"})
			return
		}

		// Create a directory to save the file if it doesn't exist
		saveDir := "./uploads"
		if _, err := os.Stat(saveDir); os.IsNotExist(err) {
			err := os.Mkdir(saveDir, os.ModePerm)
			if err != nil {
				ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Unable to create directory"})
				return
			}
		}

		// Define the path to save the file
		filePath := filepath.Join(saveDir, file.Filename)

		// Save the uploaded file to the specified directory
		if err := ctx.SaveUploadedFile(file, filePath); err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Unable to save the file"})
			return
		}

		// ctx.JSON(http.StatusOK, gin.H{
		// 	"message": "File uploaded successfully",
		// 	"file":    file.Filename,
		// })

		form := &forms.CreateExcelFileUploadForm{
			FileName: file.Filename,
		}

		excelFileUpload, err := excelFileUploadService.CreateExcelFileUpload(ctx.Request.Context(), transactionsDB, form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save excelFileUpload. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, excelFileUpload)
	}
}

func filterExcelFileUploads(
	transactionsDB *database.AppTransactionsDB,
	excelFileUploadService services.ExcelFileUploadService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering excelFileUploads",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		excelFileUploadList, err := excelFileUploadService.FilterExcelFileUploads(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter excelFileUploads",
			})
			return
		}

		ctx.JSON(http.StatusOK, excelFileUploadList)
	}
}

func getExcelFileUpload(
	transactionsDB *database.AppTransactionsDB,
	excelFileUploadService services.ExcelFileUploadService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		excelFileUploadIDStr := ctx.Param("id")
		excelFileUploadID, err := strconv.ParseInt(excelFileUploadIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse excelFileUploadID=[%v], err=[%v]\n", excelFileUploadIDStr, err)
		}

		excelFileUpload, err := excelFileUploadService.FindExcelFileUploadByID(ctx.Request.Context(), excelFileUploadID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find excelFileUpload by id=[%v]",
				excelFileUploadIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, excelFileUpload)
	}
}

func deleteExcelFileUpload(
	transactionsDB *database.AppTransactionsDB,
	excelFileUploadService services.ExcelFileUploadService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		excelFileUploadIDStr := ctx.Param("id")
		excelFileUploadID, err := strconv.ParseInt(excelFileUploadIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse excelFileUploadID=[%v], err=[%v]\n", excelFileUploadIDStr, err)
		}

		excelFileUpload, err := excelFileUploadService.DeleteExcelFileUpload(ctx.Request.Context(), excelFileUploadID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to delete excelFileUpload by id=[%v]",
				excelFileUploadIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, excelFileUpload)
	}
}

func testExcelFileUpload(
	transactionsDB *database.AppTransactionsDB,
	excelFileUploadService services.ExcelFileUploadService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var form interface{}
		err := ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind excelFileUpload form while testing excelFileUpload",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		logger.Infof("Received form=[%+v]\n", form)

		ctx.JSON(http.StatusOK, "")
	}
}

func updateExcelFileUpload(
	transactionsDB *database.AppTransactionsDB,
	excelFileUploadService services.ExcelFileUploadService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		excelFileUploadIDStr := ctx.Param("id")
		excelFileUploadID, err := strconv.ParseInt(excelFileUploadIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse excelFileUploadID=[%v], err=[%v]\n", excelFileUploadIDStr, err)
		}

		var form forms.UpdateExcelFileUploadForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind excelFileUpload form while updating excelFileUpload",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		excelFileUpload, err := excelFileUploadService.UpdateExcelFileUpload(ctx.Request.Context(), transactionsDB, excelFileUploadID, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to update excelFileUpload by id=[%v]",
				excelFileUploadIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, excelFileUpload)
	}
}
