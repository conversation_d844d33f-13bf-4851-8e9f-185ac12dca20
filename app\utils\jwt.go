package utils

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/models"

	// "time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gorilla/context"
	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

// User struct
type User struct {
	Username  string `json:"username"`
	Password  string `json:"password"`
	OrgID     int    `json:"org_id"`
	ExpiresAt string `json:"expires_at"`
	Issuer    string `json:"issuer"`
}

// Exception struct
type Exception struct {
	Message string `json:"message"`
}

// CreateTokenEndpoint - CreateTokenEndpoint
func CreateTokenEndpoint_001(w http.ResponseWriter, req *http.Request, org_id string) {
	var user User
	_ = json.NewDecoder(req.Body).Decode(&user)
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"username":  user.Username,
		"ExpiresAt": 15000,
		"Issuer":    "pgw",
		"org_id":    org_id})

	tokenString, error := token.SignedString([]byte(os.Getenv("API_SECRET")))
	if error != nil {
		log.Println(error)
	}
	json.NewEncoder(w).Encode(entities.JwtToken{Token: tokenString})
}

func CreateTokenEndpointCustom(
	session *entities.Session,
	user *entities.User,
) *entities.JwtToken {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"application_type": session.ApplicationType.String(),
		"Email":            user.Email,
		"ExpiresAt":        15000,
		"Issuer":           "esdwsgo",
		"session_id":       session.ID,
		"user_id":          user.ID,
		"org_id":           user.OrganizationID,
	})
	tokenString, error := token.SignedString([]byte(os.Getenv("API_SECRET")))
	if error != nil {
		log.Println(error)
	}
	randTokenString, _ := EncryptString(GenerateRandomString(10), GenerateRandomString(10))
	user.Token = randTokenString

	jwtToken := &entities.JwtToken{Status: "00", Token: tokenString}
	return jwtToken
}

// CreateTokenEndpointCustom - CreateTokenEndpointCustom
func CreateTokenEndpointCustom_INITIAL(db *gorm.DB, w http.ResponseWriter, user *models.User, saveV2Token string) string {

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"username":  user.Email,
		"password":  user.Password,
		"ExpiresAt": 15000,
		"Issuer":    "nbc",
		"id":        user.ID})

	tokenString, error := token.SignedString([]byte(os.Getenv("API_SECRET")))
	if error != nil {
		log.Println(error)
	}

	return tokenString
}

func ProtectedEndpoint(w http.ResponseWriter, req *http.Request) {
	params := req.URL.Query()
	token, _ := jwt.Parse(params["token"][0], func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("There was an error")
		}
		return []byte("secret"), nil
	})
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		var user User
		mapstructure.Decode(claims, &user)
		json.NewEncoder(w).Encode(user)
	} else {
		w.WriteHeader(http.StatusUnauthorized)
	}
}

func ValidateMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
		authorizationHeader := req.Header.Get("authorization")
		if authorizationHeader != "" {
			bearerToken := strings.Split(authorizationHeader, " ")
			if len(bearerToken) == 2 {
				token, error := jwt.Parse(bearerToken[1], func(token *jwt.Token) (interface{}, error) {
					if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
						return nil, fmt.Errorf("There was an error")
					}
					return []byte(os.Getenv("API_SECRET")), nil
				})

				if error != nil {
					json.NewEncoder(w).Encode(Exception{Message: error.Error()})
					return
				}

				if token.Valid {
					context.Set(req, "decoded", token.Claims)
					next(w, req)
				} else {
					w.WriteHeader(http.StatusUnauthorized)
				}
			}
		} else {
			w.WriteHeader(http.StatusUnauthorized)
		}
	})
}

func TestEndpoint(w http.ResponseWriter, req *http.Request) {
	decoded := context.Get(req, "decoded")
	var user User
	mapstructure.Decode(decoded.(jwt.MapClaims), &user)
	json.NewEncoder(w).Encode(user)
}

// Get Authenticated User Org id to save in transactions
func GetUserOrgId(req *http.Request) User {
	decoded := context.Get(req, "decoded")
	var user User
	mapstructure.Decode(decoded.(jwt.MapClaims), &user)
	return user
}

// https://www.thepolyglotdeveloper.com/2017/03/authenticate-a-golang-api-with-json-web-tokens/
