package services

import (
	"context"
	"fmt"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/providers"
	"struts-pos-backend/app/repos"
)

type (
	VFMSService interface {
		FindVFMSItemByID(context.Context, int64) (*entities.VFMSItem, error)
		FindVFMSItemsByNames(context.Context, txns_db.TransactionsSQLOperations, []string) ([]*entities.VFMSItem, error)
		GetVFMSNonTaxItems(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) (*entities.VFMSItemList, error)
		SyncVFMSNonTaxItems(context.Context, txns_db.TransactionsSQLOperations) (*entities.VFMSItemList, error)
	}

	AppVFMSService struct {
		vfms               providers.VFMS
		vfmsItemRepository repos.VFMSItemRepository
	}
)

func NewVFMSService(
	vfms providers.VFMS,
	vfmsItemRepository repos.VFMSItemRepository,
) VFMSService {
	return &AppVFMSService{
		vfms:               vfms,
		vfmsItemRepository: vfmsItemRepository,
	}
}

func (s *AppVFMSService) FindVFMSItemByID(
	ctx context.Context,
	itemID int64,
) (*entities.VFMSItem, error) {

	vfmsItem, err := s.vfmsItemRepository.GetVFMSItemByID(ctx, itemID)
	if err != nil {
		return vfmsItem, err
	}

	return vfmsItem, nil
}

func (s *AppVFMSService) FindVFMSItemsByNames(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	itemNames []string,
) ([]*entities.VFMSItem, error) {

	return s.vfmsItemRepository.FindVFMSItemsByNames(ctx, operations, itemNames)
}

func (s *AppVFMSService) GetVFMSNonTaxItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.VFMSItemList, error) {

	vfmsItemList := &entities.VFMSItemList{}

	count, err := s.vfmsItemRepository.CountVFMSItems(ctx, filter)
	if err != nil {
		return vfmsItemList, err
	}

	filtereVFMSItems, err := s.vfmsItemRepository.FilterVFMSItems(ctx, operations, filter)
	if err != nil {
		return vfmsItemList, err
	}

	vfmsItemList.VFMSItems = filtereVFMSItems

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	vfmsItemList.Pagination = pagination

	return vfmsItemList, nil
}

func (s *AppVFMSService) SyncVFMSNonTaxItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
) (*entities.VFMSItemList, error) {

	fmt.Printf("Syncing VFMS items...\n")
	vfmsItemList := &entities.VFMSItemList{}

	// Fetch items from ZRA API
	nonTaxItems, err := s.vfms.GetNonTaxItems()
	if err != nil {
		return vfmsItemList, err
	}

	nonTaxItems = s.removeItemDuplicateItems(nonTaxItems)

	// Save and update items locally, index is item name
	err = s.vfmsItemRepository.SaveMultiple(ctx, operations, nonTaxItems)
	if err != nil {
		fmt.Printf("err saving multiple vfms items, err=[%+v]", err.Error())
		return vfmsItemList, err
	}

	fmt.Printf("Done Syncing VFMS items.\n")

	return vfmsItemList, nil
}

func (s *AppVFMSService) removeItemDuplicateItems(vfmsItems []*entities.VFMSItem) []*entities.VFMSItem {
	encountered := make(map[string]*entities.VFMSItem)
	uniqueItems := []*entities.VFMSItem{}

	for _, item := range vfmsItems {
		if existingItem, ok := encountered[item.Name]; !ok {
			encountered[item.Name] = existingItem
			uniqueItems = append(uniqueItems, item)
		}
	}

	return uniqueItems
}
