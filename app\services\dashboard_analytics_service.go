package services

import (
	"context"
	"log"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
	"time"
)

type DashboardAnalyticsService interface {
	IncrementInvoiceCount(ctx context.Context, storeID int64) error
	IncrementCreditNoteCount(ctx context.Context, storeID int64) error
	IncrementStoreCount(ctx context.Context, storeID int64) error
	IncrementFailedProcessingCount(ctx context.Context, storeID int64) error
}

type AppDashboardAnalyticsService struct {
	dashboardAnalyticRepository repos.DashboardAnalyticRepository
}

func NewDashboardAnalyticsService(
	dashboardAnalyticRepository repos.DashboardAnalyticRepository,
) DashboardAnalyticsService {
	return &AppDashboardAnalyticsService{
		dashboardAnalyticRepository: dashboardAnalyticRepository,
	}
}

func (s *AppDashboardAnalyticsService) IncrementInvoiceCount(ctx context.Context, storeID int64) error {
	return s.incrementCount(ctx, storeID, "invoice")
}

func (s *AppDashboardAnalyticsService) IncrementCreditNoteCount(ctx context.Context, storeID int64) error {
	return s.incrementCount(ctx, storeID, "credit_note")
}

func (s *AppDashboardAnalyticsService) IncrementStoreCount(ctx context.Context, storeID int64) error {
	return s.incrementCount(ctx, storeID, "store")
}

func (s *AppDashboardAnalyticsService) IncrementFailedProcessingCount(ctx context.Context, storeID int64) error {
	return s.incrementCount(ctx, storeID, "failed_processing")
}

func (s *AppDashboardAnalyticsService) incrementCount(ctx context.Context, storeID int64, countType string) error {
	// Find existing analytics record for the store
	dashboardAnalytic, err := s.dashboardAnalyticRepository.FindByStoreID(ctx, storeID)
	if err != nil && !utils.IsErrNoRows(err) {
		log.Printf("error finding dashboard analytics for store_id=%v: %v", storeID, err)
		return err
	}

	// If no record exists, create a new one
	if dashboardAnalytic == nil || utils.IsErrNoRows(err) {
		dashboardAnalytic = &entities.DashboardAnalytic{
			StoreID:               storeID,
			InvoicesCount:         0,
			CreditNotesCount:      0,
			FailedProcessingCount: 0,
			StoresCount:           1,
		}
	}

	// Increment the appropriate counter
	switch countType {
	case "invoice":
		dashboardAnalytic.InvoicesCount++
	case "credit_note":
		dashboardAnalytic.CreditNotesCount++
	case "store":
		dashboardAnalytic.StoresCount++
	case "failed_processing":
		dashboardAnalytic.FailedProcessingCount++
	}

	dashboardAnalytic.StoresCount = 1
	dashboardAnalytic.UpdatedAt = time.Now()

	// Save the updated record
	err = s.dashboardAnalyticRepository.Save(ctx, dashboardAnalytic)
	if err != nil {
		log.Printf("error saving dashboard analytics for store_id=%v: %v", storeID, err)
		return err
	}

	return nil
}
