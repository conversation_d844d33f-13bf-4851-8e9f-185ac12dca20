package services

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"struts-pos-backend/app/apperr"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/ctxhelper"

	txns_db "struts-pos-backend/app/database"
)

type StoreService interface {
	CreateStore(ctx context.Context, operations txns_db.TransactionsSQLOperations, store *forms.CreateStoreForm) (*entities.Store, error)
	DownloadStores(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.StoreList, error)
	FilterStores(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.StoreList, error)
	FindStoreByID(ctx context.Context, storeID int64) (*entities.Store, error)
	GetStoreByName(ctx context.Context, storeNumber string) (*entities.Store, error)
	SaveStore(ctx context.Context, operations txns_db.TransactionsSQLOperations, store *entities.Store) error
	UpdateStore(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateStoreForm) (*entities.Store, error)
	UpdateStoreLicense(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateStoreLicenseForm) (*entities.Store, error)
}

type AppStoreService struct {
	storeRepository        repos.StoreRepository
	organizationRepository repos.OrganizationRepository
	userRepository         repos.UserRepository
}

func NewStoreService(
	organizationRepository repos.OrganizationRepository,
	storeRepo repos.StoreRepository,
	userRepository repos.UserRepository,
) StoreService {
	return &AppStoreService{
		organizationRepository: organizationRepository,
		storeRepository:        storeRepo,
		userRepository:         userRepository,
	}
}

func (s *AppStoreService) CreateStore(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateStoreForm,
) (*entities.Store, error) {

	store := &entities.Store{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Store{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Unable to find authenticated user details",
		)
	}

	organization, err := s.organizationRepository.FindByID(ctx, form.OrganizationID)
	if err != nil {
		return store, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find organization by id=[%v]",
			form.OrganizationID,
		)
	}

	store, err = s.storeRepository.FindByNameAndOrganization(ctx, form.Name, organization.ID)
	if err != nil && err != sql.ErrNoRows {
		return store, errors.New("store already exists")
	}

	store.AvatarURL = form.AvatarURL
	store.CountryCode = form.CountryCode
	store.Email = form.Email
	store.EtimsBranch = form.EtimsBranch
	store.EtimsDeviceSerial = form.EtimsDeviceSerial
	store.EtimsEnvironment = form.EtimsEnvironment
	store.Location = form.Location
	store.Name = form.Name
	store.OrganizationID = organization.ID
	store.PhoneNumber = form.PhoneNumber
	store.PIN = form.PIN
	store.UserID = user.ID
	store.VAT = form.VAT
	store.Website = form.Website

	err = s.storeRepository.Save(ctx, store)
	if err != nil {
		return store, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to create store with name=[%v]",
			form.Name,
		)
	}

	return store, nil
}

func (s *AppStoreService) DownloadStores(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.StoreList, error) {

	storeList := &entities.StoreList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.storeRepository.CountStoresForUser(ctx, operations, tokenInfo.UserID, filter)
	if err != nil {
		return storeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to count stores for user=[%v]",
			tokenInfo.UserID,
		)
	}

	stores, err := s.storeRepository.FilterStoresForUser(ctx, operations, tokenInfo.UserID, filter)
	if err != nil {
		return storeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to filter stores for user=[%v]",
			tokenInfo.UserID,
		)
	}

	storeList.Stores = stores

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	storeList.Pagination = pagination

	// utils.CreateAndAppendStoresDataToExcelFile(filePath, stores)

	return storeList, nil
}

func (s *AppStoreService) FilterStores(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.StoreList, error) {

	storeList := &entities.StoreList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)
	userID := tokenInfo.UserID

	defaultStore, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	if err != nil && !utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)

		return nil, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("failed to retrieve default store for user"),
			utils.ErrorCodeUnauthorized,
			"failed to retrieve default store for user",
			"cannot filter invoices for user with no store",
		)
	}

	filter.StoreID = defaultStore.ID

	count, err := s.storeRepository.CountStoresForUser(ctx, operations, userID, filter)
	if err != nil {
		return storeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to count stores for user=[%v]",
			tokenInfo.UserID,
		)
	}

	stores, err := s.storeRepository.FilterStoresForUser(ctx, operations, userID, filter)
	if err != nil {
		return storeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to filter stores for user=[%v]",
			tokenInfo.UserID,
		)
	}

	storeList.Stores = stores

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	storeList.Pagination = pagination

	return storeList, nil
}

func (s *AppStoreService) FindStoreByID(
	ctx context.Context,
	storeID int64,
) (*entities.Store, error) {

	store, err := s.storeRepository.FindByID(ctx, storeID)
	if err != nil {
		return store, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find store by id=[%v]",
			storeID,
		)
	}

	return store, nil
}

func (s *AppStoreService) GetStoreByName(
	ctx context.Context,
	storeName string,
) (*entities.Store, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Store{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find store by name=[%v]",
			storeName,
		)
	}

	organization, err := s.organizationRepository.FindByID(ctx, user.OrganizationID)
	if err != nil {
		return &entities.Store{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find organization by id=[%v]",
			user.OrganizationID,
		)
	}

	store, err := s.storeRepository.FindByNameAndOrganization(ctx, storeName, organization.ID)
	if err != nil {
		return store, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find store by org id=[%v]",
			organization.ID,
		)
	}

	return store, nil
}

func (s *AppStoreService) SaveStore(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	store *entities.Store,
) error {

	err := s.storeRepository.Save(ctx, store)
	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to save store by name=[%v]",
			store.Name,
		)
	}

	return nil
}

func (s *AppStoreService) UpdateStore(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeID int64,
	form *forms.UpdateStoreForm,
) (*entities.Store, error) {

	store, err := s.storeRepository.FindByID(ctx, storeID)
	if err != nil {
		return store, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to update store by id=[%v]",
			storeID,
		)
	}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Store{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find user by id=[%v]",
			tokenInfo.UserID,
		)
	}

	if store.OrganizationID != user.OrganizationID {
		return &entities.Store{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"User does not have permission to update store id=[%v]",
			storeID,
		)
	}

	if form.AvatarURL != "" {
		store.AvatarURL = form.AvatarURL
	}

	if form.CountryCode != "" {
		store.CountryCode = form.CountryCode
	}

	if form.Email > "" {
		store.Email = form.Email
	}

	if form.EtimsBranch > "" {
		store.EtimsBranch = form.EtimsBranch
	}

	if form.EtimsDeviceSerial > "" {
		store.EtimsDeviceSerial = form.EtimsDeviceSerial
	}

	if form.EtimsEnvironment > "" {
		store.EtimsEnvironment = form.EtimsEnvironment
	}

	if form.Location > "" {
		store.Location = form.Location
	}

	if form.Name > "" {
		store.Name = form.Name
	}

	if form.PIN > "" {
		store.PIN = form.PIN
	}

	if form.VAT > "" {
		store.VAT = form.VAT
	}

	err = s.storeRepository.Save(ctx, store)
	if err != nil {
		return store, err
	}

	return store, nil
}

func (s *AppStoreService) UpdateStoreLicense(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeID int64,
	form *forms.UpdateStoreLicenseForm,
) (*entities.Store, error) {

	store, err := s.storeRepository.FindByID(ctx, storeID)
	if err != nil {
		return store, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to update store by id=[%v]",
			storeID,
		)
	}

	// tokenInfo := ctxhelper.TokenInfo(ctx)

	// user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	// if err != nil {
	// 	return &entities.Store{}, apperr.Wrap(
	// 		err,
	// 	).AddLogMessagef(
	// 		"Failed to find user by id=[%v]",
	// 		tokenInfo.UserID,
	// 	)
	// }

	// if store.OrganizationID != user.OrganizationID {
	// 	return &entities.Store{}, apperr.Wrap(
	// 		err,
	// 	).AddLogMessagef(
	// 		"User does not have permission to update store id=[%v]",
	// 		storeID,
	// 	)
	// }

	store.IsLicensed = form.IsLicensed

	err = s.storeRepository.Save(ctx, store)
	if err != nil {
		fmt.Printf("update db error: %v\n", err)
		return store, err
	}

	return store, nil
}
