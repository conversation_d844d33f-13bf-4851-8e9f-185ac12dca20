package utils

import (
	"fmt"
	"os"
	"strings"
	"struts-pos-backend/app/entities"

	"github.com/xuri/excelize/v2"
)

func CreateInvoicesExcelFile(excelFile string) error {

	columns := []string{
		"#", "Voucher ID", "Customer Name", "Customer PIN", "Type", "Invoice Date",
		"16% VAT Amount", "Grand Total", "Signed Date",
		"Signature", "CU Serial No", "ETIMS Internal Data", "ETIMS Receipt No", "ETIMS Receipt Signature", "Verification URL",
	}

	// Check if the file exists
	_, err := os.Stat(excelFile)
	if os.IsNotExist(err) {

		f := excelize.NewFile()
		defer func() {
			if err := f.Close(); err != nil {
				fmt.Println(err)
			}
		}()

		// Create bold style
		boldStyle, err := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{Bold: true},
		})
		if err != nil {
			fmt.Println("Error creating style:", err)
			return err
		}

		// Iterate over the columns slice and set column names dynamically
		for i, columnName := range columns {
			// Convert index to Excel column name (e.g., 0 -> A, 1 -> B, ...)
			columnLetter, err := excelize.ColumnNumberToName(i + 1)
			if err != nil {
				fmt.Println("Error converting column number to name:", err)
				return err
			}

			// Set cell value and style
			cell := fmt.Sprintf("%s2", columnLetter)
			f.SetCellValue("Sheet1", cell, columnName)
			f.SetCellStyle("Sheet1", cell, cell, boldStyle)
			f.SetColWidth("Sheet1", columnLetter, columnLetter, 20) // Set column width
		}

		// Save spreadsheet by the given path.
		if err := f.SaveAs(excelFile); err != nil {
			fmt.Println(err)
			return err
		}

		// Close file
		if err := f.Close(); err != nil {
			fmt.Printf("Unable to close excel file after creating it, err=[%v]\n", err)
		}
	}

	return nil
}

func CreateAndAppendInvoiceDataToExcelFile(
	excelFile string,
	invoices []*entities.Invoice,
	invoicesType string,
) error {

	err := CreateInvoicesExcelFile(excelFile)
	if err != nil {
		fmt.Printf("err creating excel file, err=[%v]\n", err)
	}

	// Open an existing Excel file
	f, err := excelize.OpenFile(excelFile)
	if err != nil {
		fmt.Println("Error opening Excel file:", err)
		return err
	}

	// Get the sheet name
	sheetName := "Sheet1"

	// Get the next available row
	rows, err := f.GetRows(sheetName)
	if err != nil {
		fmt.Println("Error getting rows:", err)
		return err
	}

	numRows := len(rows)
	nextRow := numRows + 1

	// Define the mapping of struct fields to Excel columns
	columnMapping := []struct {
		Column string
		Value  func(invoice *entities.Invoice) interface{}
	}{
		{"A", func(i *entities.Invoice) interface{} { return i.InvoiceNumber }},
		{"B", func(i *entities.Invoice) interface{} { return i.InvoiceNumber }},
		{"C", func(i *entities.Invoice) interface{} { return i.CustomerName }},
		{"D", func(i *entities.Invoice) interface{} { return i.CustomerTIN }},
		{"E", func(i *entities.Invoice) interface{} { return invoicesType }},
		{"F", func(i *entities.Invoice) interface{} { return i.CreatedAt }},
		{"G", func(i *entities.Invoice) interface{} { return i.Vat }},
		{"H", func(i *entities.Invoice) interface{} { return i.GrandTotal }},
		{"I", func(i *entities.Invoice) interface{} { return i.SignedDate }},
		{"J", func(i *entities.Invoice) interface{} { return i.Signature }},

		{"K", func(i *entities.Invoice) interface{} {
			if i.EtimsSDCID != nil {
				return fmt.Sprintf("%v/%v", *i.EtimsSDCID, i.InvoiceNumber)
			}
			return ""
		}},

		{"L", func(i *entities.Invoice) interface{} {
			if i.EtimsInternalData != nil {
				return *i.EtimsInternalData
			}
			return ""
		}},

		{"M", func(i *entities.Invoice) interface{} { return i.InvoiceNumber }},
		{"N", func(i *entities.Invoice) interface{} { return i.VerificationURL }},
	}

	for _, invoice := range invoices {
		for _, mapping := range columnMapping {
			cell := fmt.Sprintf("%s%d", mapping.Column, nextRow)

			value := mapping.Value(invoice)

			f.SetCellValue(sheetName, cell, value)
		}
		nextRow++
	}

	// Save the Excel file
	err = f.SaveAs(excelFile)
	if err != nil {
		fmt.Println("Error saving Excel file:", err)
		return err
	}

	if err := f.Close(); err != nil {
		fmt.Printf("Unable to close Excel file after updating it, err=[%v]\n", err)
	}

	fmt.Printf("%v Excel file created successfully.\n", excelFile)
	return nil
}

func CreateExcelFile(excelFile string) error {

	// Check if the file exists
	_, err := os.Stat(excelFile)
	if os.IsNotExist(err) {

		f := excelize.NewFile()
		defer func() {
			if err := f.Close(); err != nil {
				fmt.Println(err)
			}
		}()

		boldStyle, err := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{Bold: true},
		})
		if err != nil {
			fmt.Println("Error creating style:", err)
			return err
		}

		f.SetCellValue("Sheet1", "A2", "#")
		f.SetCellStyle("Sheet1", "A2", "A2", boldStyle)
		f.SetColWidth("Sheet1", "A", "A", 10)

		f.SetCellValue("Sheet1", "B2", "Voucher ID")
		f.SetCellStyle("Sheet1", "B2", "A2", boldStyle)
		f.SetColWidth("Sheet1", "B", "A", 10)

		f.SetCellValue("Sheet1", "C2", "Customer Name")
		f.SetCellStyle("Sheet1", "C2", "C2", boldStyle)
		f.SetColWidth("Sheet1", "C", "C", 50)

		f.SetCellValue("Sheet1", "D2", "Customer TIN")
		f.SetCellStyle("Sheet1", "D2", "D2", boldStyle)
		f.SetColWidth("Sheet1", "D", "D", 20)

		f.SetCellValue("Sheet1", "E2", "Type")
		f.SetCellStyle("Sheet1", "E2", "D2", boldStyle)
		f.SetColWidth("Sheet1", "E", "E", 12)

		f.SetCellValue("Sheet1", "F2", "Invoice Date")
		f.SetCellStyle("Sheet1", "F2", "E2", boldStyle)
		f.SetColWidth("Sheet1", "E", "E", 16)

		f.SetCellValue("Sheet1", "F2", "Net Amount")
		f.SetCellStyle("Sheet1", "F2", "F2", boldStyle)
		f.SetColWidth("Sheet1", "F", "F", 20)

		f.SetCellValue("Sheet1", "G2", "Taxable Amount")
		f.SetCellStyle("Sheet1", "G2", "G2", boldStyle)
		f.SetColWidth("Sheet1", "G", "G", 20)

		f.SetCellValue("Sheet1", "H2", "16% VAT Amount")
		f.SetCellStyle("Sheet1", "H2", "H2", boldStyle)
		f.SetColWidth("Sheet1", "H", "H", 20)

		f.SetCellValue("Sheet1", "I2", "Grand Total")
		f.SetCellStyle("Sheet1", "I2", "I2", boldStyle)
		f.SetColWidth("Sheet1", "I", "I", 20)

		f.SetCellValue("Sheet1", "J2", "Signed Date")
		f.SetCellStyle("Sheet1", "J2", "J2", boldStyle)
		f.SetColWidth("Sheet1", "J", "J", 20)

		f.SetCellValue("Sheet1", "K2", "Signature")
		f.SetCellStyle("Sheet1", "K2", "K2", boldStyle)
		f.SetColWidth("Sheet1", "K", "K", 50)

		f.SetCellValue("Sheet1", "L2", "Verification URL")
		f.SetCellStyle("Sheet1", "L2", "L2", boldStyle)
		f.SetColWidth("Sheet1", "L", "L", 50)

		// Save spreadsheet by the given path.
		if err := f.SaveAs(excelFile); err != nil {
			fmt.Println(err)
			return err
		}

		// Close file
		if err := f.Close(); err != nil {
			fmt.Printf("unable to close excel file after creating it, err=[%v]\n", err)
		}
	}

	return nil
}

func AppendDataToExcelFile(
	excelFile string,
	invoice *entities.Invoice,
) error {

	err := CreateExcelFile(excelFile)
	if err != nil {
		fmt.Printf("err creating excel file, err=[%v]\n", err)
	}

	// Open an existing Excel file
	f, err := excelize.OpenFile(excelFile)
	if err != nil {
		fmt.Println("Error opening Excel file:", err)
		return err
	}

	// Get the sheet name
	sheetName := "Sheet1"

	// Get the next available row
	rows, err := f.GetRows(sheetName)
	if err != nil {
		fmt.Println("Error getting rows:", err)
		return err
	}

	numRows := len(rows)
	nextRow := numRows + 1

	cell := fmt.Sprintf("A%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.InvoiceNumber)

	cell = fmt.Sprintf("B%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.CustomerName)

	cell = fmt.Sprintf("C%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.CustomerTIN)

	cell = fmt.Sprintf("D%d", nextRow)
	f.SetCellValue(sheetName, cell, "Invoice")

	cell = fmt.Sprintf("E%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.InvoiceDate)

	cell = fmt.Sprintf("F%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.NetAmount)

	cell = fmt.Sprintf("G%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.TaxableAmount)

	cell = fmt.Sprintf("H%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.Vat)

	cell = fmt.Sprintf("I%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.GrandTotal)

	cell = fmt.Sprintf("J%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.SignedDate)

	cell = fmt.Sprintf("K%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.Signature)

	cell = fmt.Sprintf("L%d", nextRow)
	f.SetCellValue(sheetName, cell, invoice.VerificationURL)

	// Save the Excel file
	err = f.SaveAs(excelFile)
	if err != nil {
		fmt.Println("Error saving Excel file:", err)
		return err
	}

	if err := f.Close(); err != nil {
		fmt.Printf("unable to close excel file after updating it, err=[%v]\n", err)
	}

	fmt.Println("Excel file updated successfully.")
	return nil
}

func CreateAndAppendCategoriesDataToExcelFile(
	excelFile string,
	categories []*entities.Category,
) error {

	err := CreateExcelFile(excelFile)
	if err != nil {
		fmt.Printf("err creating excel file, err=[%v]\n", err)
	}

	// Open an existing Excel file
	f, err := excelize.OpenFile(excelFile)
	if err != nil {
		fmt.Println("Error opening Excel file:", err)
		return err
	}

	// Get the sheet name
	sheetName := "Sheet1"

	// Get the next available row
	rows, err := f.GetRows(sheetName)
	if err != nil {
		fmt.Println("Error getting rows:", err)
		return err
	}

	numRows := len(rows)
	nextRow := numRows + 1

	for _, category := range categories {

		cell := fmt.Sprintf("A%d", nextRow)
		f.SetCellValue(sheetName, cell, category.Name)

		cell = fmt.Sprintf("B%d", nextRow)
		f.SetCellValue(sheetName, cell, category.Description)

		cell = fmt.Sprintf("C%d", nextRow)
		f.SetCellValue(sheetName, cell, category.ID)

		cell = fmt.Sprintf("D%d", nextRow)
		f.SetCellValue(sheetName, cell, "Invoice")

		cell = fmt.Sprintf("E%d", nextRow)
		f.SetCellValue(sheetName, cell, category.CreatedAt)

		nextRow += 1
	}

	// Save the Excel file
	err = f.SaveAs(excelFile)
	if err != nil {
		fmt.Println("Error saving Excel file:", err)
		return err
	}

	if err := f.Close(); err != nil {
		fmt.Printf("unable to close excel file after updating it, err=[%v]\n", err)
	}

	fmt.Printf("%v Excel file created successfully.\n", excelFile)
	return nil
}

func CreateAndAppendCreditNoteDataToExcelFile(
	excelFile string,
	invoices []*entities.Invoice,
) error {

	err := CreateExcelFile(excelFile)
	if err != nil {
		fmt.Printf("err creating excel file, err=[%v]\n", err)
	}

	// Open an existing Excel file
	f, err := excelize.OpenFile(excelFile)
	if err != nil {
		fmt.Println("Error opening Excel file:", err)
		return err
	}

	// Get the sheet name
	sheetName := "Sheet1"

	// Get the next available row
	rows, err := f.GetRows(sheetName)
	if err != nil {
		fmt.Println("Error getting rows:", err)
		return err
	}

	numRows := len(rows)
	nextRow := numRows + 1

	for _, invoice := range invoices {

		cell := fmt.Sprintf("A%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.InvoiceNumber)

		cell = fmt.Sprintf("B%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.CustomerName)

		cell = fmt.Sprintf("C%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.CustomerTIN)

		cell = fmt.Sprintf("D%d", nextRow)
		f.SetCellValue(sheetName, cell, "Invoice")

		cell = fmt.Sprintf("E%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.InvoiceDate)

		cell = fmt.Sprintf("F%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.NetAmount)

		cell = fmt.Sprintf("G%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.NetAmount)

		cell = fmt.Sprintf("H%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.Vat)

		cell = fmt.Sprintf("I%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.GrandTotal)

		cell = fmt.Sprintf("J%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.SignedDate)

		cell = fmt.Sprintf("K%d", nextRow)
		f.SetCellValue(sheetName, cell, invoice.Signature)

		cell = fmt.Sprintf("L%d", nextRow)
		f.SetCellValue(sheetName, cell, strings.ToLower(invoice.VerificationURL))

		nextRow += 1
	}

	// Save the Excel file
	err = f.SaveAs(excelFile)
	if err != nil {
		fmt.Println("Error saving Excel file:", err)
		return err
	}

	if err := f.Close(); err != nil {
		fmt.Printf("unable to close excel file after updating it, err=[%v]\n", err)
	}

	fmt.Printf("%v Excel file created successfully.\n", excelFile)
	return nil
}

func CreateAndAppendItemsDataToExcelFile(
	excelFile string,
	items []*entities.Item,
) error {

	err := CreateExcelFile(excelFile)
	if err != nil {
		fmt.Printf("err creating excel file, err=[%v]\n", err)
	}

	// Open an existing Excel file
	f, err := excelize.OpenFile(excelFile)
	if err != nil {
		fmt.Println("Error opening Excel file:", err)
		return err
	}

	// Get the sheet name
	sheetName := "Sheet1"

	// Get the next available row
	rows, err := f.GetRows(sheetName)
	if err != nil {
		fmt.Println("Error getting rows:", err)
		return err
	}

	numRows := len(rows)
	nextRow := numRows + 1

	for _, item := range items {

		cell := fmt.Sprintf("A%d", nextRow)
		f.SetCellValue(sheetName, cell, item.Name)

		cell = fmt.Sprintf("B%d", nextRow)
		f.SetCellValue(sheetName, cell, item.Description)

		cell = fmt.Sprintf("C%d", nextRow)
		f.SetCellValue(sheetName, cell, item.ID)

		cell = fmt.Sprintf("D%d", nextRow)
		f.SetCellValue(sheetName, cell, "Invoice")

		cell = fmt.Sprintf("E%d", nextRow)
		f.SetCellValue(sheetName, cell, item.CreatedAt)

		nextRow += 1
	}

	// Save the Excel file
	err = f.SaveAs(excelFile)
	if err != nil {
		fmt.Println("Error saving Excel file:", err)
		return err
	}

	if err := f.Close(); err != nil {
		fmt.Printf("unable to close excel file after updating it, err=[%v]\n", err)
	}

	fmt.Printf("%v Excel file created successfully.\n", excelFile)
	return nil
}
