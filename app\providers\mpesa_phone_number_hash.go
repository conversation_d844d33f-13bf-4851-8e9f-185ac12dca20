package providers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"

	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
)

type (
	MPesaPhoneNumberHashDecoder interface {
		DecodePhoneNumberHash(phoneNumberHash string) (*entities.MPesaPhoneNumberHashDecoderResponse, error)
		TopUpHashAccount(form *forms.PhoneNumberHashTopupForm) (*entities.PhoneNumberHashTopupResponse, error)
	}

	AppMPesaPhoneNumberHashDecoder struct {
		apiKey    string
		apiURL    string
		b2bAPIKey string
	}
)

func NewMPesaPhoneNumberHashDecoder() MPesaPhoneNumberHashDecoder {
	return NewMPesaPhoneNumberHashDecoderWithCredentials(
		os.Getenv("MPESA_HASH_API_KEY"),
		os.Getenv("MPESA_HASH_API_URL"),
		os.Getenv("MPESA_HASH_API_B2B_API_KEY"),
	)
}

func NewMPesaPhoneNumberHashDecoderWithCredentials(apiKey, apiURL, b2bAPIKey string) MPesaPhoneNumberHashDecoder {
	return &AppMPesaPhoneNumberHashDecoder{
		apiKey:    apiKey,
		apiURL:    apiURL,
		b2bAPIKey: b2bAPIKey,
	}
}

func (s *AppMPesaPhoneNumberHashDecoder) DecodePhoneNumberHash(
	phoneNumberHash string,
) (*entities.MPesaPhoneNumberHashDecoderResponse, error) {

	phoneNumberHashRespnse := &entities.MPesaPhoneNumberHashDecoderResponse{}
	url := fmt.Sprintf("%v/hashes/hash/%v", s.apiURL, phoneNumberHash)
	method := "GET"

	client := &http.Client{}
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		fmt.Printf("error initiating phone number hash request, err=[%v]\n", err)
		return phoneNumberHashRespnse, err
	}
	req.Header.Add("x-hash-api-key", s.apiKey)

	res, err := client.Do(req)
	if err != nil {
		fmt.Printf("error sending phone number hash request, err=[%v]\n", err)
		return phoneNumberHashRespnse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		fmt.Printf("error reading phone number hash response, err=[%v]\n", err)
		return phoneNumberHashRespnse, err
	}

	err = json.Unmarshal([]byte(string(body)), &phoneNumberHashRespnse)
	if err != nil {
		fmt.Printf("error unmarshaling phone number hash decode response, err=[%v]\n", err)
		return phoneNumberHashRespnse, err
	}

	if phoneNumberHashRespnse == nil {
		return &entities.MPesaPhoneNumberHashDecoderResponse{
			Hash:        phoneNumberHash,
			PhoneNumber: "",
		}, nil
	}

	return phoneNumberHashRespnse, nil
}

func (s *AppMPesaPhoneNumberHashDecoder) TopUpHashAccount(
	form *forms.PhoneNumberHashTopupForm,
) (*entities.PhoneNumberHashTopupResponse, error) {

	fmt.Printf("topping up hash account=[%v] with amount=[%v]\n", form.AccountNumber, form.Amount)

	phoneNumberHashTopupResponse := &entities.PhoneNumberHashTopupResponse{}
	url := fmt.Sprintf("%v/user_credits/topup", s.apiURL)
	method := "POST"

	fmt.Printf("topup url: [%v]\n", url)

	// Convert form to payload
	topupPayload := map[string]interface{}{
		"account_number": form.AccountNumber,
		"amount":         form.Amount,
		"phone_number":   form.PhoneNumber,
	}

	payload, err := json.Marshal(topupPayload)
	if err != nil {
		fmt.Printf("error marshaling phone number hash topup payload, err=[%v]\n", err)
		return phoneNumberHashTopupResponse, err
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, strings.NewReader(string(payload)))
	if err != nil {
		fmt.Printf("error initiating phone number hash topup request, err=[%v]\n", err)
		return phoneNumberHashTopupResponse, err
	}
	req.Header.Add("x-b2b-api-key", s.b2bAPIKey)
	req.Header.Add("Content-Type", "application/json")

	fmt.Printf("s.b2bAPIKey=[%v]\n", s.b2bAPIKey)

	res, err := client.Do(req)
	if err != nil {
		fmt.Printf("error sending phone number hash topup request, err=[%v]\n", err)
		return phoneNumberHashTopupResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		fmt.Printf("error reading phone number hash topup response, err=[%v]\n", err)
		return phoneNumberHashTopupResponse, err
	}

	fmt.Printf("topup response = [%v]\n", string(body))

	err = json.Unmarshal([]byte(string(body)), &phoneNumberHashTopupResponse)
	if err != nil {
		fmt.Printf("error unmarshaling phone number hash topup response, err=[%v]\n", err)
		return phoneNumberHashTopupResponse, err
	}

	return phoneNumberHashTopupResponse, nil
}
