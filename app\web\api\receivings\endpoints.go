package receivings

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/web/auth"

	"struts-pos-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	receivingService services.ReceivingService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/receivings", createReceiving(transactionsDB, receivingService))
		protectedAPI.GET("/receivings", filterReceivings(transactionsDB, receivingService))
		protectedAPI.GET("/receivings/:id", getReceiving(transactionsDB, receivingService))
		protectedAPI.PUT("/receivings/:id", updateReceiving(transactionsDB, receivingService))
		protectedAPI.GET("/receivings/download", downloadReceivingsExcel(transactionsDB, receivingService))
	}
}
