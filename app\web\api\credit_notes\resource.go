package credit_notes

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/webutils"
)

func downloadCreditNotesExcel(
	transactionsDB *database.AppTransactionsDB,
	creditNoteService services.CreditNoteService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering activities.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		t := time.Now()
		dateString := t.Format("CreditNotes-2006-01-02-150405")
		outputFolder := os.Getenv("OUTPUT_FOLDER")
		excelFileName := fmt.Sprintf("%v.xlsx", dateString)
		excelFilePath := fmt.Sprintf("%v//%v", outputFolder, excelFileName)

		_, err = creditNoteService.DownloadCreditNotes(ctx.Request.Context(), transactionsDB, filter, excelFilePath)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to download creditNotes",
			})
			return
		}

		// ctx.JSON(http.StatusOK, creditNoteList)
		file, err := os.Open(excelFilePath)
		if err != nil {
			ctx.String(http.StatusInternalServerError, fmt.Sprintf("Error opening file: %s", err))
			return
		}
		defer file.Close()

		// Set the headers for the response
		ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		ctx.Header("Content-Disposition", "attachment; filename="+excelFileName)

		// Copy the file content to the response writer
		_, err = io.Copy(ctx.Writer, file)
		if err != nil {
			ctx.String(500, fmt.Sprintf("Error copying file to response: %s", err))
			return
		}
	}
}

func filterCreditNotes(
	transactionsDB *database.AppTransactionsDB,
	creditNoteService services.CreditNoteService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering activities.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		creditNoteList, err := creditNoteService.FilterCreditNotes(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter creditNotes",
			})
			return
		}

		ctx.JSON(http.StatusOK, creditNoteList)
	}
}

func getCreditNote(
	transactionsDB *database.AppTransactionsDB,
	creditNoteService services.CreditNoteService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		creditNoteIDStr := ctx.Param("id")
		creditNoteID, err := strconv.ParseInt(creditNoteIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse creditNoteID=[%v], err=[%v]\n", creditNoteIDStr, err)
		}

		creditNote, err := creditNoteService.FindCreditNoteByID(ctx.Request.Context(), creditNoteID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter creditNotes",
			})
			return
		}

		ctx.JSON(http.StatusOK, creditNote)
	}
}

func getCreditNotePDFFile(
	transactionsDB *database.AppTransactionsDB,
	creditNoteService services.CreditNoteService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		creditNoteIDStr := ctx.Param("id")
		creditNoteID, err := strconv.ParseInt(creditNoteIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse creditNoteID=[%v], err=[%v]\n", creditNoteIDStr, err)
		}

		creditNote, err := creditNoteService.FindCreditNoteByID(ctx.Request.Context(), creditNoteID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter creditNotes",
			})
			return
		}

		ctx.Writer.Header().Set("Content-type", "application/pdf")
		ctx.File(creditNote.FileName)
	}
}

func signCreditNote(
	transactionsDB *database.AppTransactionsDB,
	creditNoteService services.CreditNoteService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var form forms.ETIMSCreditNoteForm
		err := ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind creditNote form while signing creditNote",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		creditNote, err := creditNoteService.SignCreditNote(ctx.Request.Context(), transactionsDB, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to sign creditNote=[%v]",
				form.CreditNoteNumber,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, creditNote)
	}
}
