package invoices

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/web/auth"

	"struts-pos-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	invoiceService services.InvoiceService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/invoices", signInvoice(transactionsDB, invoiceService))
		protectedAPI.GET("/invoices", filterInvoices(transactionsDB, invoiceService))
		protectedAPI.GET("/invoices/:id", getInvoiceByID(transactionsDB, invoiceService))
		protectedAPI.GET("/invoices/invoice_number/:invoiceNumber", getInvoiceByInvoiceNumber(transactionsDB, invoiceService))
		protectedAPI.GET("/invoices/download", downloadInvoicesExcel(transactionsDB, invoiceService))
	}

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/invoices/:id/pdf", getInvoicePDFFile(transactionsDB, invoiceService))
	}
}
