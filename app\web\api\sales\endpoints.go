package sales

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/web/auth"

	"struts-pos-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	saleService services.SaleService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/sales", makeSale(transactionsDB, saleService))
		protectedAPI.GET("/sales", filterSales(transactionsDB, saleService))
		protectedAPI.GET("/store/sales", filterSalesForStore(transactionsDB, saleService))
		protectedAPI.GET("/sales/:id", getSale(transactionsDB, saleService))
		protectedAPI.PUT("/sales/:id", updateSale(transactionsDB, saleService))
		protectedAPI.GET("/sales/download", downloadSalesExcel(transactionsDB, saleService))
	}
}
