package mock

import (
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/logger"
	"struts-pos-backend/app/providers"
)

type (
	MockAfricasTalking struct {
	}
)

func NewAfricasTalking() providers.AfricasTalking {
	return &MockAfricasTalking{}
}

func (s *MockAfricasTalking) SendSms(phoneNumber entities.PhoneNumber, message string, category entities.MessageCategory) error {
	logger.Infof("Send message=[%v] to phone number=[%v], category=[%v]", message, phoneNumber, category)
	return nil
}
