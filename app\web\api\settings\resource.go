package settings

import (
	"context"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/webutils"
)

func saveSetting(
	transactionsDB *database.AppTransactionsDB,
	settingService services.SettingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var form forms.CreateSettingForm
		ctx.Bind(&form)

		setting, err := settingService.CreateSetting(context.Background(), transactionsDB, form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to create setting",
			})
			return
		}

		ctx.JSON(http.StatusOK, setting)
	}
}

func filterSettings(
	transactionsDB *database.AppTransactionsDB,
	settingService services.SettingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering activities.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		settingList, err := settingService.FilterSettings(context.Background(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter settings",
			})
			return
		}

		ctx.JSON(http.StatusOK, settingList)
	}
}

func getSetting(
	transactionsDB *database.AppTransactionsDB,
	settingService services.SettingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		settingIDStr := ctx.Param("id")
		settingID, err := strconv.ParseInt(settingIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse settingID=[%v], err=[%v]\n", settingIDStr, err)
		}

		setting, err := settingService.FindSettingByID(context.Background(), settingID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to retrieve setting",
			})
			return
		}

		ctx.JSON(http.StatusOK, setting)
	}
}
