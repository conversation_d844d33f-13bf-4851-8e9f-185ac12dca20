package ctxhelper

import (
	"context"

	"struts-pos-backend/app/entities"
)

func ApplicationType(ctx context.Context) entities.ApplicationType {
	existing := ctx.Value(entities.ContextKeyApplicationType)
	if existing == nil {
		return entities.ApplicationType("")
	}

	return entities.ApplicationType(existing.(string))
}

func WithApplicationType(ctx context.Context, applicationType string) context.Context {
	return context.WithValue(ctx, entities.ContextKeyApplicationType, applicationType)
}
