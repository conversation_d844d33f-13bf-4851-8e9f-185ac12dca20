package providers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/logger"
	"struts-pos-backend/app/utils"
)

type (
	EtimsVSCUCore interface {
		EchoTest(testStr string) error
		SaveSale(form *forms.CreateEtimsSalesForm) (*entities.EtimsSaveSaleResponse, error)
		SelectInfo() (*entities.EtimsInfoResponse, error)
		SelectNotices() error
		SelectServerTime() (string, error)
	}

	AppEtimsVSCUCore struct {
		apiBaseURL         string
		branchID           string
		deviceSerialNumber string
		pinNumber          string
	}
)

func NewEtimsVSCUCore(
	etimsAPIBaseURL string,
	etimsBranchID string,
	etimsDeviceSerial string,
	etimsPinNumber string,
) EtimsVSCUCore {

	appEtimsVSCUCore := &AppEtimsVSCUCore{
		apiBaseURL:         etimsAPIBaseURL,
		branchID:           etimsBranchID,
		deviceSerialNumber: etimsDeviceSerial,
		pinNumber:          etimsPinNumber,
	}

	return appEtimsVSCUCore
}

func (s *AppEtimsVSCUCore) EchoTest(testStr string) error {

	fmt.Printf("Conducting ETIMS VSCU echo test...\n")

	apiURL := fmt.Sprintf("%v/selectTestEcho", s.apiBaseURL)
	fmt.Printf("apiURL=[%v]\n", apiURL)

	form := forms.EchoTestForm{
		Tin:     s.pinNumber,
		BhfId:   s.branchID,
		TestStr: testStr,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal echoTest form payload, err=[%v]\n", err)
		return err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return utils.NewError(
			err,
			"unable to create echoTest request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return utils.NewError(
			err,
			"unable to process echoTest request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return utils.NewError(
			err,
			"unable to process echoTest response body.",
		)
	}

	responseBodyString := string(body)
	fmt.Printf("responseBodyString=[%v]\n", responseBodyString)

	return nil
}

func (s *AppEtimsVSCUCore) SaveSale(form *forms.CreateEtimsSalesForm) (*entities.EtimsSaveSaleResponse, error) {

	fmt.Printf("Saving ETIMS VSCU sale...\n")
	saveSaleResponse := &entities.EtimsSaveSaleResponse{}

	apiURL := fmt.Sprintf("%v/saveTrnsSalesVsdc", s.apiBaseURL)
	fmt.Printf("apiURL=[%v]\n", apiURL)

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal saveTrnsSalesVsdc form payload, err=[%v]\n", err)
		return saveSaleResponse, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return saveSaleResponse, utils.NewError(
			err,
			"unable to create saveTrnsSalesVsdc request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("tin", s.pinNumber)
	req.Header.Add("bhfId", s.branchID)
	req.Header.Add("cmcKey", "rtZzYYaQvIKeh22Vz4FSzEM2OjLvK+k8GJr3bmPiw/+1oSwnsk8RZ8mGGKm5/qsh2bUfxr8w6OpMVszz/1dU8A==")
	req.Header.Add("intrlKey", "6xfhaIZlW6sLGOPxrAG01PEVyxrcXiYlkiaqO7NzXDiVJvpqyPUfmffmR1ZJ3BZxKeUT7d/xwEP4/WFnO5BHmg==")
	req.Header.Add("signKey", "keLEfz/0LBBEbTnLIYEdfYl1JoIoblpljiA6rt+j5ECD57nTyFuJtZbprFELluoNfE1TasO7fGQwxoQyP+BfNg==")

	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return saveSaleResponse, utils.NewError(
			err,
			"unable to process saveTrnsSalesVsdc request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return saveSaleResponse, utils.NewError(
			err,
			"unable to process saveTrnsSalesVsdc response body.",
		)
	}

	responseBodyString := string(body)
	fmt.Printf("responseBodyString=[%v]\n", responseBodyString)

	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &saveSaleResponse)
	if err != nil {
		return saveSaleResponse, utils.NewError(
			err,
			"unable to unmarshal response body",
		)
	}

	return saveSaleResponse, nil
}

func (s *AppEtimsVSCUCore) SelectInfo() (*entities.EtimsInfoResponse, error) {

	fmt.Printf("Selecting ETIMS VSCU info...\n")
	info := &entities.EtimsInfoResponse{}

	apiURL := fmt.Sprintf("%v/selectInitVsdcInfo", s.apiBaseURL)
	fmt.Printf("apiURL=[%v]\n", apiURL)

	form := forms.CreateSelectInfoForm{
		Tin:      s.pinNumber,
		BhfId:    s.branchID,
		DvcSrlNo: s.deviceSerialNumber,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal selectInitInfo form payload, err=[%v]\n", err)
		return info, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to create selectInfo request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to process SelectInfo request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to process SelectInfo response body.",
		)
	}

	responseBodyString := string(body)
	fmt.Printf("responseBodyString=[%v]\n", responseBodyString)

	fmt.Printf("response Headers=[%+v]\n", res.Request.Header)

	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &info)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to unmarshal response body",
		)
	}

	return info, nil
}

func (s *AppEtimsVSCUCore) SelectNotices() error {

	fmt.Printf("Selecting ETIMS VSCU notices...\n")

	apiURL := fmt.Sprintf("%v/selectNoticeList", s.apiBaseURL)
	fmt.Printf("apiURL=[%v]\n", apiURL)

	form := forms.NoticeRequestForm{
		Tin:       s.pinNumber,
		BhfId:     s.branchID,
		LastReqDt: "20240529232139",
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal selectNoticeList form payload, err=[%v]\n", err)
		return err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return utils.NewError(
			err,
			"unable to create selectNoticeList request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return utils.NewError(
			err,
			"unable to process selectNoticeList request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return utils.NewError(
			err,
			"unable to process selectNoticeList response body.",
		)
	}

	responseBodyString := string(body)
	fmt.Printf("responseBodyString=[%v]\n", responseBodyString)

	return nil
}

func (s *AppEtimsVSCUCore) SelectServerTime() (string, error) {

	fmt.Printf("Selecting ETIMS VSCU server time...\n")

	apiURL := fmt.Sprintf("%v/selectServerTime", s.apiBaseURL)
	fmt.Printf("apiURL=[%v]\n", apiURL)

	req, err := http.NewRequest("POST", apiURL, nil)
	if err != nil {
		return "", utils.NewError(
			err,
			"unable to create selectServerTime request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", utils.NewError(
			err,
			"unable to process selectServerTime request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return "", utils.NewError(
			err,
			"unable to process selectServerTime response body.",
		)
	}

	responseBodyString := string(body)
	fmt.Printf("responseBodyString=[%v]\n", responseBodyString)

	return "", nil
}
