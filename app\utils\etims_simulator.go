package utils

import (
	"fmt"
	"struts-pos-backend/app/entities"
	"time"
)

func SimulateInvoice() (*entities.Invoice, error) {

	var etimsItems []*entities.EtimsItem
	var items []*entities.InvoiceItem

	invoiceNumber := "500012"

	invoice := &entities.Invoice{
		CustomerName:          "Customer",
		DateCreated:           time.Now(),
		DocumentTypeCode:      String("S"), // S for Sale, R for Credit Note
		EtimsItems:            etimsItems,
		FileName:              invoiceNumber + ".pdf",
		GrandTotal:            2120.00,
		InvoiceDate:           "Sat Apr 06 2024 01:38 PM",
		InvoiceItems:          items,
		InvoiceNumber:         invoiceNumber,
		OriginalFileName:      invoiceNumber + ".pdf",
		OriginalInvoiceNumber: invoiceNumber,
		Vat:                   292.41,
	}

	populateItems(invoice)

	return invoice, nil
}

func populateItems(
	invoice *entities.Invoice,
) {

	loadItem(invoice, 1, "1/2 KENYA CANE", 1, 700.00, 700.00)
	loadItem(invoice, 2, "300ML SODA", 1, 80.00, 80.00)
	loadItem(invoice, 3, "500ML SODA", 1, 100.00, 100.00)
	loadItem(invoice, 4, "1 Kg Meat", 1, 900.00, 900.00)
	loadItem(invoice, 5, "Ugali Plain", 2, 70.00, 140.00)
	loadItem(invoice, 6, "Managu Plain", 1, 200.00, 200.00)
}

func loadItem(
	invoice *entities.Invoice,
	itemID int,
	itemName string,
	itemQty float64,
	unitPrice float64,
	totalAmt float64,
) {

	// Load ETIMS Item
	etimsItem := &entities.EtimsItem{
		AddInfo:     GenerateItemCode(itemName, 7),
		ItemCd:      GenerateItemCode(itemName, 20),
		ItemClsCd:   GenerateItemCode(itemName, 5),
		ItemNm:      itemName,
		ItemTyCd:    entities.EtimsProductTypeService.String(),
		ItemStdNm:   itemName,
		BhfID:       "00",
		DftPrc:      unitPrice,
		IsrcAplcbYn: "N",
		ModrID:      "Admin",
		ModrNm:      "Admin",
		OrgnNatCd:   "KE",
		PkgUnitCd:   "BG",
		QtyUnitCd:   "U",
		RegrID:      "Admin",
		RegrNm:      "Admin",
		SftyQty:     1,
		TaxTyCd:     entities.EtimsTaxTypeB.String(),
		Tin:         "P052223456Z",
		UseYn:       "Y",
	}
	etimsItem.Timestamps.Touch()

	invoice.EtimsItems = append(invoice.EtimsItems, etimsItem)

	// Load item
	item := &entities.InvoiceItem{
		Amount:       unitPrice,
		ItemID:       itemID,
		ItemName:     itemName,
		Name:         itemName,
		Quantity:     itemQty,
		SerialNumber: fmt.Sprintf("%v", itemID),
		Total:        totalAmt,
	}
	item.Timestamps.Touch()

	invoice.InvoiceItems = append(invoice.InvoiceItems, item)
}
