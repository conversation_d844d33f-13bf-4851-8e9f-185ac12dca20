package api_keys

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/api_keys", createAPIKey(transactionsDB, apiKeyService))
		protectedAPI.GET("/api_keys", filterAPIKeys(transactionsDB, apiKeyService))
		protectedAPI.GET("/api_keys/:id", getAPIKey(transactionsDB, apiKeyService))
		protectedAPI.PUT("/api_keys/:id", updateAPIKey(transactionsDB, apiKeyService))
		protectedAPI.DELETE("/api_keys/:id", deleteAPIKey(transactionsDB, apiKeyService))
		protectedAPI.POST("/api_keys_t", testAPIKey(transactionsDB, apiKeyService))
	}
}
