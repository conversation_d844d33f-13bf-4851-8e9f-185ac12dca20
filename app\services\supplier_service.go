package services

import (
	"context"
	"errors"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/ctxhelper"

	txns_db "struts-pos-backend/app/database"
)

type SupplierService interface {
	CreateSupplier(ctx context.Context, operations txns_db.TransactionsSQLOperations, supplier *forms.CreateSupplierForm) (*entities.Supplier, error)
	DownloadSuppliers(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.SupplierList, error)
	FilterSuppliers(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.SupplierList, error)
	FindSupplierByID(ctx context.Context, supplierID int64) (*entities.Supplier, error)
	GetSupplierByName(ctx context.Context, supplierNumber string) (*entities.Supplier, error)
	SaveSupplier(ctx context.Context, operations txns_db.TransactionsSQLOperations, supplier *entities.Supplier) error
	UpdateSupplier(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateSupplierForm) (*entities.Supplier, error)
}

type AppSupplierService struct {
	supplierRepository repos.SupplierRepository
	userRepository     repos.UserRepository
}

func NewSupplierService(
	supplierRepo repos.SupplierRepository,
	userRepository repos.UserRepository,
) SupplierService {
	return &AppSupplierService{
		supplierRepository: supplierRepo,
		userRepository:     userRepository,
	}
}

func (s *AppSupplierService) CreateSupplier(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateSupplierForm,
) (*entities.Supplier, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Supplier{}, err
	}

	supplier, err := s.supplierRepository.FindByEmailAndOganization(ctx, form.Email, user.OrganizationID)
	if err != nil && !utils.IsErrNoRows(err) {
		return supplier, errors.New("supplier already exists")
	}

	supplier.Name = form.Name
	supplier.ContactPerson = form.ContactPerson
	supplier.Email = form.Email
	supplier.PhoneNumber = form.PhoneNumber
	supplier.OrganizationID = user.OrganizationID

	err = s.supplierRepository.Save(ctx, supplier)
	if err != nil {
		return supplier, err
	}

	return supplier, nil
}

func (s *AppSupplierService) DownloadSuppliers(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.SupplierList, error) {

	supplierList := &entities.SupplierList{}

	count := s.supplierRepository.CountSuppliers(ctx, filter)

	categories, err := s.supplierRepository.FilterSuppliers(ctx, operations, filter)
	if err != nil {
		return supplierList, err
	}

	supplierList.Suppliers = categories

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	supplierList.Pagination = pagination

	// utils.CreateAndAppendSuppliersDataToExcelFile(filePath, categories)

	return supplierList, nil
}

func (s *AppSupplierService) FilterSuppliers(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.SupplierList, error) {

	supplierList := &entities.SupplierList{}

	count := s.supplierRepository.CountSuppliers(ctx, filter)

	suppliers, err := s.supplierRepository.FilterSuppliers(ctx, operations, filter)
	if err != nil {
		return supplierList, err
	}

	supplierList.Suppliers = suppliers

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	supplierList.Pagination = pagination

	return supplierList, nil
}

func (s *AppSupplierService) FindSupplierByID(
	ctx context.Context,
	supplierID int64,
) (*entities.Supplier, error) {

	supplier, err := s.supplierRepository.FindByID(ctx, supplierID)
	if err != nil {
		return supplier, err
	}

	return supplier, nil
}

func (s *AppSupplierService) GetSupplierByName(
	ctx context.Context,
	supplierNumber string,
) (*entities.Supplier, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Supplier{}, err
	}

	supplier, err := s.supplierRepository.FindByEmailAndOganization(ctx, supplierNumber, user.OrganizationID)
	if err != nil {
		return supplier, err
	}

	return supplier, nil
}

func (s *AppSupplierService) SaveSupplier(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	supplier *entities.Supplier,
) error {

	err := s.supplierRepository.Save(ctx, supplier)
	if err != nil {
		return err
	}

	return nil
}

func (s *AppSupplierService) UpdateSupplier(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	supplierID int64,
	form *forms.UpdateSupplierForm,
) (*entities.Supplier, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	supplier, err := s.supplierRepository.FindByID(ctx, supplierID)
	if err != nil {
		return supplier, errors.New("unable to find supplier")
	}

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Supplier{}, err
	}

	if user.OrganizationID != supplier.OrganizationID {
		return &entities.Supplier{}, errors.New("user does not have permission to update organization supplier")
	}

	supplier.Name = form.Name
	supplier.ContactPerson = form.ContactPerson
	supplier.Email = form.Email
	supplier.PhoneNumber = form.PhoneNumber

	err = s.supplierRepository.Save(ctx, supplier)
	if err != nil {
		return supplier, err
	}

	return supplier, nil
}
