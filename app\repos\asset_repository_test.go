package repos

import (
	"context"
	"testing"

	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/testutils"
	"struts-pos-backend/app/utils"

	esd_db "struts-pos-backend/app/database"

	"github.com/joho/godotenv"
	. "github.com/smartystreets/goconvey/convey"
)

func TestAssetRepository(t *testing.T) {

	// TODO: Setup env picking properly
	err := godotenv.Load("../../.env")
	if err != nil {
		panic("unable to load .env file!")
	}

	// Run migrations in test db

	// databaseURL := os.Getenv("TEST_DATABASE_URL")
	// transactionsDB := esd_db.InitTransactionsSQLTestDB(databaseURL)

	testTxnsDB := esd_db.InitTransactionsTestDB()
	defer testTxnsDB.Close()

	ctx := context.Background()

	assetRepository := NewAssetRepository()

	Convey("AssetRepository", t, testutils.WithTestDBs(ctx, testTxnsDB,
		func(ctx context.Context, txnsDB esd_db.TransactionsDB) {

			// Setup Asset
			asset := &entities.UserAsset{
				Asset: entities.Asset{
					ContentMD5:      "v/YeG0TsmBt67LU0nBwDbg==",
					ContentSize:     2236371,
					ContentType:     "image/png",
					Description:     "Profile picture.",
					Status:          "pending",
					Key:             utils.GenerateAssetUUID(),
					Duration:        utils.Int(0),
					ImageLength:     utils.Int(1280),
					ImageWidth:      utils.Int(13989),
					ThumbnailStatus: "pending",
					ThumbnailKey:    utils.String(utils.GenerateAssetUUID()),
				},
			}

			err := assetRepository.Save(ctx, txnsDB, asset)
			So(err, ShouldBeNil)

			Convey("can find by id", func() {

				asset, err = assetRepository.FindByID(ctx, txnsDB, asset.ID)
				So(err, ShouldBeNil)
				So(asset.Description, ShouldEqual, "Profile picture.")
			})

			Convey("can update an asset", func() {

				asset.Description = "Nike Shoes"
				err = assetRepository.Save(ctx, txnsDB, asset)
				So(err, ShouldBeNil)
			})

			Convey("can delete an asset", func() {

				err = assetRepository.Delete(ctx, txnsDB, asset)
				So(err, ShouldBeNil)
			})

		},
	))
}
