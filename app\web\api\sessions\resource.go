package sessions

import (
	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/services"
)

const tokenHeader = "x-strutspos-token"

func healthCheck(ctx *gin.Context) {
	ctx.JSON(200, gin.H{
		"message": "All services up",
		"status":  "Ok",
	})
}

func loginUser(
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var loginForm forms.UserLoginForm
		ctx.Bind(&loginForm)

		user, token, err := userService.LoginUser(ctx.Request.Context(), transactionsDB, &loginForm)
		if err != nil {
			ctx.JSON(401, gin.H{
				"message": "login failed",
			})

			return
		}

		ctx.Writer.Header().Set(tokenHeader, token.Token)
		ctx.JSON(200, gin.H{"user": user})
	}
}
