package etims_items

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	etimsItemService services.EtimsItemService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyApiKeyUser(transactionsDB, apiKeyRepository))
	{
		protectedAPI.POST("/etims_vscu/items", createEtimsItem(transactionsDB, etimsItemService))
		protectedAPI.GET("/etims_vscu/items", filterEtimsItems(transactionsDB, etimsItemService))
		protectedAPI.GET("/etims_vscu/items/:id", getEtimsItem(transactionsDB, apiKeyService, etimsItemService))
	}
}
