package utils

import (
	"context"
	"database/sql"
	"fmt"
	"struts-pos-backend/app/logger"
	"net/http"
	"os"
	"strings"

	"struts-pos-backend/app/web/ctxhelper"

	"github.com/getsentry/sentry-go"
)

type Error struct {
	err            error // original error
	errorCode      ErrorCode
	errorMessage   string // Contains error message for error that can be displayed to user
	httpStatusCode int
	logMessages    []string // Contains the "stack" of error messages
	notify         bool     // if true, changes log level to error instead of warn
}

// Helper constructors

// NewError is the generic constructor which tries to typecast the err input to an Error struct pointer.
// If successful, it adds the log message according to the format specifier.
// Otherwise, it creates a new Error struct using err as the original error.
func NewError(err error, format string, a ...interface{}) *Error {

	appError, ok := err.(*Error)
	if !ok {
		return NewErrorWithCode(
			err,
			ErrorCodeRequestFailed,
			format,
			a...,
		)
	}

	appError.addLogMessage(err, false, format, a...)

	return appError
}

func NewErrorWithCode(err error, errorCode ErrorCode, format string, a ...interface{}) *Error {

	return NewErrorWithErrorCodeAndMessage(
		err,
		errorCode,
		"",
		format,
		a...,
	)
}

func NewErrorWithErrorCodeAndMessage(
	err error,
	errorCode ErrorCode,
	message string,
	format string,
	a ...interface{},
) *Error {

	if errorCode == "" {
		errorCode = ErrorCodeRequestFailed
	}

	appError, ok := err.(*Error)
	if !ok {
		appError = &Error{
			err: err,
		}

		appError.SetErrorCode(errorCode)
	}

	if message != "" {
		appError.errorMessage = message
	}

	appError.addLogMessage(err, true, format, a...)

	return appError
}

// NewDatabaseError creates the Error pointer for any error caused by data repository methods (Find, Save, etc.)
// If the original error is not sql.ErrNoRows, the log messages will trigger an alert to the dev team.
func NewDatabaseError(err error, format string, a ...interface{}) *Error {

	appError := NewError(err, format, a...)

	if err != sql.ErrNoRows {
		appError.SetErrorCode(ErrorCodeSQLFailed)
		appError.httpStatusCode = http.StatusInternalServerError
		appError.Notify()
	} else {
		appError.httpStatusCode = http.StatusNotFound
	}

	return appError
}

func IsErrNoRows(err error) bool {

	appError, ok := err.(*Error)

	if !ok {
		return err == sql.ErrNoRows
	} else {
		return appError.Err() == sql.ErrNoRows
	}
}

func IsErrUnauthorized(err error) bool {

	appError, ok := err.(*Error)

	if ok && appError.errorCode == ErrorCodeUnauthorized {
		return true
	} else {
		return false
	}
}

func (e *Error) AddErrorMessage(format string, a ...interface{}) *Error {
	e.errorMessage = fmt.Sprintf(format, a...)
	return e
}

func (e *Error) SetHttpStatusCode(httpStatusCode int) *Error {
	e.httpStatusCode = httpStatusCode
	return e
}

// Error interface
func (e *Error) Error() string {

	prefix := "[WARN]"

	if e.notify {
		prefix = "[ERROR]"
	}

	return fmt.Sprintf("%s %s", prefix, strings.Join(e.logMessages, "; "))
}

func (e *Error) UseErrorLoglevel() bool {
	return e.notify
}

func (e *Error) Err() error {
	return e.err
}

// Utility Functions

func (e *Error) HttpStatus() int {

	if e.httpStatusCode != 0 {
		return e.httpStatusCode
	}

	mapHttpStatus, ok := statusCodesMap[e.errorCode]
	if !ok || mapHttpStatus == 0 {
		return http.StatusBadRequest
	}

	return mapHttpStatus
}

func (e *Error) JsonResponse() map[string]string {

	if e.errorMessage == "" {
		e.errorMessage = "Failed to perform request. Please try again."
	}

	return map[string]string{
		"error_code":    e.errorCode.String(),
		"error_message": e.errorMessage,
	}
}

func (e *Error) LogErrorMessages(ctx context.Context) {
	e.doLogErrorMessagesWithEvent(ctx, sentry.NewEvent())
}

func (e *Error) LogErrorMessagesWithRequest(req *http.Request) {
	event := sentry.NewEvent()
	event.Request = sentry.NewRequest(req)

	e.doLogErrorMessagesWithEvent(req.Context(), event)
}

func (e *Error) doLogErrorMessagesWithEvent(ctx context.Context, event *sentry.Event) {
	if e.UseErrorLoglevel() {
		logger.Error(e.Error())
	} else {
		logger.Warn(e.Error())
	}

	if !e.notify {
		return
	}

	event.Environment = os.Getenv("ENVIRONMENT")
	event.Message = e.err.Error()
	event.Exception = append(
		event.Exception,
		sentry.Exception{
			Stacktrace: sentry.ExtractStacktrace(e.err),
		},
	)
	for _, msg := range e.logMessages {
		event.Breadcrumbs = append(event.Breadcrumbs, &sentry.Breadcrumb{Message: msg})
	}

	hub := sentry.CurrentHub()
	if sentry.HasHubOnContext(ctx) {
		hub = sentry.GetHubFromContext(ctx)
	}

	applicationType := ctxhelper.ApplicationType(ctx)
	if applicationType != "" {
		event.Extra["application_type"] = applicationType
	}

	clientIdentifier := ctxhelper.ClientIdentifier(ctx)
	if clientIdentifier != "" {
		event.Extra["client_identifier"] = clientIdentifier
	}

	clientVersion := ctxhelper.ClientVersion(ctx)
	if clientVersion != "" {
		event.Extra["client_version"] = clientVersion
	}

	ipAddress := ctxhelper.IPAddress(ctx)
	if ipAddress != "" {
		event.Extra["ip_address"] = ipAddress
	}

	requestID := ctxhelper.RequestId(ctx)
	if requestID != "" {
		event.Extra["request_id"] = requestID
	}

	userID := ctxhelper.TokenInfo(ctx).UserID
	if userID != 0 {
		event.Extra["user_id"] = userID
	}

	sessionID := ctxhelper.TokenInfo(ctx).SessionID
	if userID != 0 {
		event.Extra["session_id"] = sessionID
	}

	hub.CaptureEvent(event)
}

func (e *Error) Notify() *Error {
	e.notify = true
	return e
}

func (e *Error) SetErrorCode(errorCode ErrorCode) {
	e.errorCode = errorCode
	mapErrorMessage, ok := errorMessagesMap[errorCode]
	if !ok || mapErrorMessage == "" {
		e.errorMessage = defaultErrorMessage
	} else {
		e.errorMessage = mapErrorMessage
	}
}

// Private functions
func (e *Error) addLogMessage(err error, includeErr bool, format string, a ...interface{}) {

	suffix := ""
	if includeErr {
		suffix = fmt.Sprintf(" because err=[%v]", err)
	}

	e.logMessages = append([]string{fmt.Sprintf("%s%s", fmt.Sprintf(format, a...), suffix)}, e.logMessages...)
}
