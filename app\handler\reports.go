package handler

import (
	"log"
	"struts-pos-backend/app/models"
	"struts-pos-backend/app/utils"
	"net/http"
	"time"

	"github.com/jinzhu/gorm"
)

func GetSummaryReport(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	summaryReport := models.SummaryReport{}
	reportsFilter := models.ReportsFilter{}
	summaryReport.AllInvoicesCount = CountInvoices(db, reportsFilter)
	summaryReport.AllInvoicesVatSum = AllInvoicesVatSum(db, reportsFilter)
	summaryReport.AllInvoicesTotalSum = AllInvoicesTotalSum(db, reportsFilter)
	reportsFilter.Status = "failed"
	summaryReport.InvoicesThatFailedToSignCount = CountInvoices(db, reportsFilter)
	respondJSON(w, http.StatusOK, summaryReport)
}

func GetDashboardInvoicesChart(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	apiResponse := []models.DashboardInvoiceChartReport{}

	currentTime := time.Now()
	year := utils.ConvertIntToString(currentTime.Year())

	rows, err := db.Raw("SELECT MONTH(date_created) as date, "+
		" COUNT(*) as count  FROM invoices   WHERE YEAR(date_created) = ?   GROUP BY MONTH(date_created)", year).Rows()

	if err != nil {
		log.Println("Encountered error while retrieving invoice chart data >>> ", err.Error())
	}

	defer rows.Close()
	for rows.Next() {

		var rowRecord models.DashboardInvoiceChartReport

		date := ""
		var count int64
		rows.Scan(&date, &count)

		if len(date) < 2 {
			date = "0" + date
		}

		date = year + "-" + date + "-25"
		rowRecord.Date = date
		rowRecord.RecordsCount = count

		apiResponse = append(apiResponse, rowRecord)
	}

	respondJSON(w, http.StatusOK, apiResponse)

}
