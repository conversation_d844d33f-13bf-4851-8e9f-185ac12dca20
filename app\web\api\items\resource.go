package items

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/services"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/webutils"
)

func createItem(
	transactionsDB *database.AppTransactionsDB,
	itemService services.ItemService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var itemForm forms.CreateItemForm
		err := ctx.Bind(&itemForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind item form while creating item",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		item, err := itemService.CreateItem(ctx.Request.Context(), transactionsDB, &itemForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to create item. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, item)
	}
}

func downloadItemsExcel(
	transactionsDB *database.AppTransactionsDB,
	itemService services.ItemService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering items",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		t := time.Now()
		dateString := t.Format("Items-2006-01-02-150405")
		outputFolder := os.Getenv("OUTPUT_FOLDER")
		excelFileName := fmt.Sprintf("%v.xlsx", dateString)
		excelFilePath := fmt.Sprintf("%v//%v", outputFolder, excelFileName)

		_, err = itemService.DownloadItems(context.Background(), transactionsDB, filter, excelFilePath, filter.StoreID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to download items",
			})
			return
		}

		// ctx.JSON(http.StatusOK, itemList)
		file, err := os.Open(excelFilePath)
		if err != nil {
			ctx.String(http.StatusInternalServerError, fmt.Sprintf("Error opening file: %s", err))
			return
		}
		defer file.Close()

		// Set the headers for the response
		ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		ctx.Header("Content-Disposition", "attachment; filename="+excelFileName)

		// Copy the file content to the response writer
		_, err = io.Copy(ctx.Writer, file)
		if err != nil {
			ctx.String(500, fmt.Sprintf("Error copying file to response: %s", err))
			return
		}
	}
}

func filterItems(
	transactionsDB *database.AppTransactionsDB,
	itemService services.ItemService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering items",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		itemList, err := itemService.FilterItems(context.Background(), transactionsDB, filter, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter items",
			})
			return
		}

		ctx.JSON(http.StatusOK, itemList)
	}
}

func getItem(
	transactionsDB *database.AppTransactionsDB,
	itemService services.ItemService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		itemIDStr := ctx.Param("id")
		itemID, err := strconv.ParseInt(itemIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse itemID=[%v], err=[%v]\n", itemIDStr, err)
		}

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering items",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		item, err := itemService.FindItemByID(ctx.Request.Context(), itemID, filter.StoreID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to get item",
			})
			return
		}

		ctx.JSON(http.StatusOK, item)
	}
}

func updateItem(
	transactionsDB *database.AppTransactionsDB,
	itemService services.ItemService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		itemIDStr := ctx.Param("id")
		itemID, err := strconv.ParseInt(itemIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse itemID=[%v], err=[%v]\n", itemIDStr, err)
		}

		var form forms.UpdateItemForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind item form while updating item",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering items",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		item, err := itemService.UpdateItem(ctx.Request.Context(), transactionsDB, itemID, &form, filter.StoreID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to update item",
			})
			return
		}

		ctx.JSON(http.StatusOK, item)
	}
}
