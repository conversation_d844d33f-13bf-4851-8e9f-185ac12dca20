package repos

import (
	"context"
	db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
)

const (
	selectApplicationsSQL = `SELECT a.id, a.application_type, a.created_at, a.updated_at FROM applications a`

	findApplicationByTypeSQL = selectApplicationsSQL + " WHERE a.application_type=$1"

	findApplicationByTypeAndUserSQL = selectApplicationsSQL + `
		JOIN user_applications ua ON ua.application_id = a.id
		WHERE a.application_type=$1 AND ua.user_id=$2`
)

type (
	ApplicationRepository interface {
		FindByApplicationType(ctx context.Context, operations db.TransactionsSQLOperations, applicationType entities.ApplicationType) (*entities.Application, error)
		FindByApplicationTypeAndUser(ctx context.Context, operations db.TransactionsSQLOperations, applicationType entities.ApplicationType, userID int64) (*entities.Application, error)
	}

	AppApplicationRepository struct {
	}
)

func NewApplicationRepository() *AppApplicationRepository {
	return &AppApplicationRepository{}
}

func (r *AppApplicationRepository) FindByApplicationType(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	applicationType entities.ApplicationType,
) (*entities.Application, error) {

	row := operations.QueryRowContext(ctx, findApplicationByTypeSQL, applicationType)
	return r.scanRowIntoApplication(row)
}

func (r *AppApplicationRepository) FindByApplicationTypeAndUser(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	applicationType entities.ApplicationType,
	userID int64,
) (*entities.Application, error) {

	row := operations.QueryRowContext(ctx, findApplicationByTypeAndUserSQL, applicationType, userID)
	return r.scanRowIntoApplication(row)
}

func (r *AppApplicationRepository) scanRowIntoApplication(
	rowScanner db.RowScanner,
) (*entities.Application, error) {
	var application entities.Application
	err := rowScanner.Scan(
		&application.ID,
		&application.ApplicationType,
		&application.Timestamps.CreatedAt,
		&application.Timestamps.UpdatedAt,
	)

	return &application, err
}
