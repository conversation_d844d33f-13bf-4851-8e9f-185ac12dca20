package repos

import (
	"context"
	"strconv"
	"strings"
	"struts-pos-backend/app/apperr"

	db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
)

const (
	selectUserAssetsSQL = `SELECT id, blur_hash, content_md5, content_size, content_type, description, duration, key, image_length, image_width,
		 url, failed_at, reason, status, uploaded_at, thumbnail_url, thumbnail_content_size, thumbnail_content_type, thumbnail_key, 
		thumbnail_image_length, thumbnail_image_width, thumbnail_status, thumbnail_failed_at, thumbnail_uploaded_at, created_at, updated_at FROM assets`

	findUserAssetByIDSQL = selectUserAssetsSQL + " WHERE id = $1"

	insertUserAssetSQL = `INSERT INTO assets (blur_hash, content_md5, content_size, content_type, description, duration, key, image_length, image_width,
		url, failed_at, reason, status, uploaded_at, thumbnail_url, thumbnail_content_size, thumbnail_content_type, thumbnail_key, 
		thumbnail_image_length, thumbnail_image_width, thumbnail_status, thumbnail_failed_at, thumbnail_uploaded_at, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25) RETURNING id`

	deleteAssetByIDSQLStatement = `DELETE FROM assets WHERE id = $1`

	updateUserAssetSQL = `UPDATE assets SET (blur_hash, description, failed_at, reason, status, uploaded_at, thumbnail_content_size, thumbnail_content_type, 
		thumbnail_image_length, thumbnail_image_width, thumbnail_status, thumbnail_failed_at, thumbnail_uploaded_at, updated_at)
			= ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) WHERE id = $15`
)

type (
	AssetRepository interface {
		Delete(ctx context.Context, operations db.TransactionsSQLOperations, asset *entities.UserAsset) error
		FindByID(ctx context.Context, operations db.TransactionsSQLOperations, assetID int64) (*entities.UserAsset, error)
		FindByIDs(ctx context.Context, operations db.TransactionsSQLOperations, assetIDs []int64) ([]*entities.UserAsset, error)
		Save(ctx context.Context, operations db.TransactionsSQLOperations, asset *entities.UserAsset) error
	}

	AppAssetRepository struct {
	}
)

func NewAssetRepository() AssetRepository {
	return &AppAssetRepository{}
}

func (r *AppAssetRepository) Delete(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	asset *entities.UserAsset,
) error {

	_, err := operations.ExecContext(
		ctx,
		deleteAssetByIDSQLStatement,
		asset.ID,
	)

	return apperr.WrapNilableDatabaseError(err)
}

func (r *AppAssetRepository) FindByID(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	assetID int64,
) (*entities.UserAsset, error) {

	row := operations.QueryRowContext(ctx, findUserAssetByIDSQL, assetID)
	return r.scanRowIntoAsset(row)
}

func (r *AppAssetRepository) FindByIDs(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	assetIDs []int64,
) ([]*entities.UserAsset, error) {

	userAssets := make([]*entities.UserAsset, 0)
	args := make([]interface{}, 0)
	currentIndex := 1

	if len(assetIDs) == 0 {
		return userAssets, nil
	}

	assetIDsQuery := ""
	for _, assetID := range assetIDs {
		assetIDsQuery += "$" + strconv.Itoa(currentIndex) + ","
		args = append(args, assetID)
		currentIndex++
	}
	assetIDsQuery = strings.TrimRight(assetIDsQuery, ",")
	query := selectUserAssetsSQL + " WHERE id IN (" + assetIDsQuery + ")"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return userAssets, apperr.NewDatabaseError(err)
	}

	defer rows.Close()

	for rows.Next() {
		asset, err := r.scanRowIntoAsset(rows)
		if err != nil {
			return userAssets, apperr.NewDatabaseError(err)
		}

		userAssets = append(userAssets, asset)
	}

	return userAssets, apperr.WrapNilableDatabaseError(rows.Err())
}

func (r *AppAssetRepository) Save(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	asset *entities.UserAsset,
) error {

	asset.Timestamps.Touch()

	if asset.IsNew() {
		res := operations.QueryRowContext(
			ctx,
			insertUserAssetSQL,
			asset.BlurHash,
			asset.ContentMD5,
			asset.ContentSize,
			asset.ContentType,
			asset.Description,
			asset.Duration,
			asset.Key,
			asset.ImageLength,
			asset.ImageWidth,
			asset.URL,
			asset.FailedAt,
			asset.Reason,
			asset.Status,
			asset.UploadedAt,
			asset.ThumbnailURL,
			asset.ThumbnailContentSize,
			asset.ThumbnailContentType,
			asset.ThumbnailKey,
			asset.ThumbnailImageLength,
			asset.ThumbnailImageWidth,
			asset.ThumbnailStatus,
			asset.ThumbnailFailedAt,
			asset.ThumbnailUploadedAt,
			asset.Timestamps.CreatedAt,
			asset.Timestamps.UpdatedAt,
		)

		err := res.Scan(&asset.ID)
		return apperr.WrapNilableDatabaseError(err)
	}

	_, err := operations.ExecContext(
		ctx,
		updateUserAssetSQL,
		asset.BlurHash,
		asset.Description,
		asset.FailedAt,
		asset.Reason,
		asset.Status,
		asset.UploadedAt,
		asset.ThumbnailContentSize,
		asset.ThumbnailContentType,
		asset.ThumbnailImageLength,
		asset.ThumbnailImageWidth,
		asset.ThumbnailStatus,
		asset.ThumbnailFailedAt,
		asset.ThumbnailUploadedAt,
		asset.Timestamps.UpdatedAt,
		asset.ID,
	)

	return apperr.WrapNilableDatabaseError(err)
}

func (r *AppAssetRepository) scanRowIntoAsset(
	rowScanner db.RowScanner,
) (*entities.UserAsset, error) {

	var asset entities.UserAsset

	err := rowScanner.Scan(
		&asset.ID,
		&asset.BlurHash,
		&asset.ContentMD5,
		&asset.ContentSize,
		&asset.ContentType,
		&asset.Description,
		&asset.Duration,
		&asset.Key,
		&asset.ImageLength,
		&asset.ImageWidth,
		&asset.URL,
		&asset.FailedAt,
		&asset.Reason,
		&asset.Status,
		&asset.UploadedAt,
		&asset.ThumbnailURL,
		&asset.ThumbnailContentSize,
		&asset.ThumbnailContentType,
		&asset.ThumbnailKey,
		&asset.ThumbnailImageLength,
		&asset.ThumbnailImageWidth,
		&asset.ThumbnailStatus,
		&asset.ThumbnailFailedAt,
		&asset.ThumbnailUploadedAt,
		&asset.CreatedAt,
		&asset.UpdatedAt,
	)

	if err != nil {
		return &asset, apperr.NewDatabaseError(
			err,
		).AddLogMessage(
			"asset scan error",
		)
	}

	return &asset, nil
}
