package repos

import (
	"context"
	"fmt"
	"strings"
	"struts-pos-backend/app/apperr"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
)

const (
	insertPhoneNumberSQL = `INSERT INTO phone_numbers (country_code, mpesa_hash, number, created_at, updated_at) VALUES`

	createPhoneNumberSQL = insertPhoneNumberSQL + ` ($1, $2, $3, $4, $5) RETURNING id`

	selectPhoneNumberSQL = `SELECT id, country_code, mpesa_hash, number, created_at, updated_at FROM phone_numbers`

	findPhoneNumberByIDSQL = selectPhoneNumberSQL + ` WHERE id=$1`

	findPhoneNumberByMpesaHashSQL = selectPhoneNumberSQL + ` WHERE mpesa_hash=$1`

	searchPhoneNumberSQL = selectPhoneNumberSQL + ` WHERE country_code=$1 AND number=$2`

	updatePhoneNumberSQL = `UPDATE phone_numbers SET (country_code, mpesa_hash, number, updated_at)
		= ($1, $2, $3, $4) WHERE id = $5`
)

type (
	PhoneNumberRepository interface {
		FindByID(ctx context.Context, operations txns_db.TransactionsSQLOperations, phoneNumberID int64) (*entities.PhoneNumber, error)
		FindByMpesaHash(ctx context.Context, operations txns_db.TransactionsSQLOperations, phoneNumberHash string) (*entities.PhoneNumber, error)
		Save(ctx context.Context, operations txns_db.TransactionsSQLOperations, phoneNumber *entities.PhoneNumber) error
		SaveMultiple(ctx context.Context, operations txns_db.TransactionsSQLOperations, phoneNumbers []*entities.PhoneNumber) error
		SearchByPhoneNumber(ctx context.Context, operations txns_db.TransactionsSQLOperations, phoneNumber entities.PhoneNumber) (*entities.PhoneNumber, error)
	}

	AppPhoneNumberRepository struct {
	}
)

func NewPhoneNumberRepository() PhoneNumberRepository {
	return &AppPhoneNumberRepository{}
}

func (r *AppPhoneNumberRepository) FindByID(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	phoneNumberID int64,
) (*entities.PhoneNumber, error) {

	row := operations.QueryRowContext(ctx, findPhoneNumberByIDSQL, phoneNumberID)
	return r.scanRowIntoPhoneNumber(row)
}

func (r *AppPhoneNumberRepository) FindByMpesaHash(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	phoneNumberHash string,
) (*entities.PhoneNumber, error) {

	row := operations.QueryRowContext(ctx, findPhoneNumberByMpesaHashSQL, phoneNumberHash)
	return r.scanRowIntoPhoneNumber(row)
}

func (r *AppPhoneNumberRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	phoneNumber *entities.PhoneNumber,
) error {

	phoneNumber.Timestamps.Touch()

	if phoneNumber.IsNew() {
		res := operations.QueryRowContext(
			ctx,
			createPhoneNumberSQL,
			phoneNumber.CountryCode,
			phoneNumber.MpesaHash,
			phoneNumber.Number,
			phoneNumber.Timestamps.CreatedAt,
			phoneNumber.Timestamps.UpdatedAt,
		)
		err := res.Scan(&phoneNumber.ID)
		return apperr.WrapNilableDatabaseError(err)
	}

	_, err := operations.ExecContext(
		ctx,
		updatePhoneNumberSQL,
		phoneNumber.CountryCode,
		phoneNumber.MpesaHash,
		phoneNumber.Number,
		phoneNumber.Timestamps.UpdatedAt,
		phoneNumber.ID,
	)

	return apperr.WrapNilableDatabaseError(err)
}

func (r *AppPhoneNumberRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	phoneNumbers []*entities.PhoneNumber,
) error {
	args := make([]interface{}, len(phoneNumbers)*4)

	insertQuery := insertPhoneNumberSQL
	values := make([]string, len(phoneNumbers))
	argsCurrentCount := 0

	for index, phoneNumber := range phoneNumbers {
		phoneNumber.Touch()
		args[argsCurrentCount] = phoneNumber.CountryCode
		args[argsCurrentCount+1] = phoneNumber.MpesaHash
		args[argsCurrentCount+2] = phoneNumber.Number
		args[argsCurrentCount+3] = phoneNumber.CreatedAt
		args[argsCurrentCount+4] = phoneNumber.UpdatedAt

		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d)", argsCurrentCount+1, argsCurrentCount+2,
			argsCurrentCount+3, argsCurrentCount+4, argsCurrentCount+5)
		argsCurrentCount += 5
	}

	insertQuery += strings.Join(values, ",")
	insertQuery += " ON CONFLICT (country_code, number) DO UPDATE SET updated_at=now() RETURNING id"

	rows, err := operations.QueryContext(ctx, insertQuery, args...)
	if err != nil {
		return apperr.NewDatabaseError(err)
	}

	defer rows.Close()

	rowCount := 0
	for rows.Next() {
		err := rows.Scan(&phoneNumbers[rowCount].ID)
		if err != nil {
			return apperr.NewDatabaseError(err)
		}
		rowCount++
	}

	return apperr.WrapNilableDatabaseError(rows.Err())
}

func (r *AppPhoneNumberRepository) SearchByPhoneNumber(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	coreentityPhoneNumber entities.PhoneNumber,
) (*entities.PhoneNumber, error) {

	row := operations.QueryRowContext(ctx, searchPhoneNumberSQL, coreentityPhoneNumber.CountryCode, coreentityPhoneNumber.Number)
	return r.scanRowIntoPhoneNumber(row)
}

func (r *AppPhoneNumberRepository) scanRowIntoPhoneNumber(
	rowScanner txns_db.RowScanner,
) (*entities.PhoneNumber, error) {

	var phoneNumber entities.PhoneNumber

	err := rowScanner.Scan(
		&phoneNumber.ID,
		&phoneNumber.CountryCode,
		&phoneNumber.MpesaHash,
		&phoneNumber.Number,
		&phoneNumber.Timestamps.CreatedAt,
		&phoneNumber.Timestamps.UpdatedAt,
	)

	if err != nil {
		return &phoneNumber, err
	}

	return &phoneNumber, nil
}
