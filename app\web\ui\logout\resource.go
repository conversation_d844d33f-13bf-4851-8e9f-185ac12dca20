package logout

import (
	"log"
	"net/http"
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/services"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"

	globals "struts-pos-backend/globals"
)

func logout(
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		session := sessions.Default(ctx)
		user := session.Get(globals.Userkey)
		log.Println("logging out user:", user)
		if user == nil {
			log.Println("Invalid session token")
			return
		}
		session.Delete(globals.Userkey)
		if err := session.Save(); err != nil {
			log.Println("Failed to save session:", err)
			return
		}

		ctx.Redirect(http.StatusMovedPermanently, "/")
	}
}
