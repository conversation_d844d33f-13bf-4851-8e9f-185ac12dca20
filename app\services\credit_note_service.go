package services

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"struts-pos-backend/app/apperr"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/logger"
	"struts-pos-backend/app/providers"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/ctxhelper"
	"time"

	txns_db "struts-pos-backend/app/database"

	"github.com/google/uuid"
)

type CreditNoteService interface {
	CreateCreditNote(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoice forms.CreditNoteForm) (*entities.Invoice, error)
	DownloadCreditNotes(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.InvoiceList, error)
	FilterCreditNotes(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.InvoiceList, error)
	FindCreditNoteByID(ctx context.Context, invoiceID int64) (*entities.Invoice, error)
	GetCreditNoteByCreditNoteNumber(ctx context.Context, invoiceNumber string, storeId int64) (*entities.Invoice, error)
	SaveCreditNote(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoice *entities.Invoice) error
	SignCreditNote(ctx context.Context, transactionsDB txns_db.TransactionsDB, form *forms.ETIMSCreditNoteForm) (*entities.InvoiceSigningResult, error)
}

type AppCreditNoteService struct {
	etimsItemRepository       repos.EtimsItemRepository
	etimsProductionAPIURL     string
	etimsSandboxAPIURL        string
	etimsVSCU                 providers.EtimsVSCU
	dashboardAnalyticsService DashboardAnalyticsService
	invoiceItemRepository     repos.InvoiceItemRepository
	invoiceRepository         repos.InvoiceRepository
	storeRepository           repos.StoreRepository
	traVfdService             TRAVFDService
	userRepository            repos.UserRepository
}

func NewCreditNoteService(
	etimsItemRepository repos.EtimsItemRepository,
	etimsProductionAPIURL string,
	etimsSandboxAPIURL string,
	etimsVSCU providers.EtimsVSCU,
	dashboardAnalyticsService DashboardAnalyticsService,
	invoiceItemRepository repos.InvoiceItemRepository,
	invoiceRepo repos.InvoiceRepository,
	storeRepository repos.StoreRepository,
	traVfdService TRAVFDService,
	userRepository repos.UserRepository,
) CreditNoteService {
	return &AppCreditNoteService{
		etimsItemRepository:       etimsItemRepository,
		etimsProductionAPIURL:     etimsProductionAPIURL,
		etimsSandboxAPIURL:        etimsSandboxAPIURL,
		etimsVSCU:                 etimsVSCU,
		dashboardAnalyticsService: dashboardAnalyticsService,
		invoiceItemRepository:     invoiceItemRepository,
		invoiceRepository:         invoiceRepo,
		storeRepository:           storeRepository,
		traVfdService:             traVfdService,
		userRepository:            userRepository,
	}
}

func (s *AppCreditNoteService) CreateCreditNote(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form forms.CreditNoteForm,
) (*entities.Invoice, error) {

	invoice := &entities.Invoice{}

	// do some basic validations
	if form.CreditNoteNumber == "" {
		return invoice, errors.New("invoice service - invoiceNumber required")
	}

	// TODO: Figure out how to populate the storeId
	invoice, err := s.invoiceRepository.GetCreditNoteByCreditNoteNumber(ctx, form.CreditNoteNumber, 0)
	if err != nil && err != sql.ErrNoRows {
		// TODO : Update invoice if it already exists
		return invoice, errors.New("invoice already exists")
	}

	// do some basic normalisation
	invoice.GrossAmount = form.GrossAmount
	invoice.Vat = form.Vat
	invoice.GrandTotal = form.GrandTotal

	err = s.invoiceRepository.Save(ctx, operations, invoice)
	if err != nil {
		return invoice, err
	}

	return invoice, nil
}

func (s *AppCreditNoteService) DownloadCreditNotes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.InvoiceList, error) {

	invoiceList := &entities.InvoiceList{}

	count, err := s.invoiceRepository.CountInvoices(ctx, filter)
	if err != nil {
		return invoiceList, err
	}

	invoices, err := s.invoiceRepository.FilterInvoices(ctx, operations, filter)
	if err != nil {
		return invoiceList, err
	}

	invoiceList.Invoices = invoices

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	invoiceList.Pagination = pagination

	utils.CreateAndAppendCreditNoteDataToExcelFile(filePath, invoices)

	return invoiceList, nil
}

func (s *AppCreditNoteService) FilterCreditNotes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.InvoiceList, error) {

	invoiceList := &entities.InvoiceList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return invoiceList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find user by id=[%v]",
			tokenInfo.UserID,
		)
	}

	filter.UserID = user.ID

	defaultStore, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	if err != nil && utils.IsErrNoRows(err) {
		utils.Log.Infof("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return invoiceList, err
	}

	filter.StoreID = defaultStore.ID

	count, err := s.invoiceRepository.CountInvoices(ctx, filter)
	if err != nil {
		return invoiceList, err
	}

	creditNotes, err := s.invoiceRepository.FilterInvoices(ctx, operations, filter)
	if err != nil {
		return invoiceList, err
	}

	invoiceList.Invoices = creditNotes

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	invoiceList.Pagination = pagination

	return invoiceList, nil
}

func (s *AppCreditNoteService) FindCreditNoteByID(
	ctx context.Context,
	invoiceID int64,
) (*entities.Invoice, error) {

	invoice, err := s.invoiceRepository.GetInvoiceByID(ctx, invoiceID, 1)
	if err != nil {
		return invoice, err
	}

	return invoice, nil
}

func (s *AppCreditNoteService) GetCreditNoteByCreditNoteNumber(
	ctx context.Context,
	invoiceNumber string,
	storeId int64,
) (*entities.Invoice, error) {

	invoice, err := s.invoiceRepository.GetCreditNoteByCreditNoteNumber(ctx, invoiceNumber, storeId)
	if err != nil {
		return invoice, err
	}

	return invoice, nil
}

func (s *AppCreditNoteService) SaveCreditNote(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	invoice *entities.Invoice,
) error {

	err := s.invoiceRepository.Save(ctx, operations, invoice)
	if err != nil {
		return err
	}

	return nil
}

func (s *AppCreditNoteService) SignCreditNote(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
	form *forms.ETIMSCreditNoteForm,
) (*entities.InvoiceSigningResult, error) {

	signingResult := &entities.InvoiceSigningResult{}
	tokenInfo := ctxhelper.TokenInfo(ctx)
	creditNoteNumber := fmt.Sprintf("%v", form.CreditNoteNumber)

	store, err := s.storeRepository.FindByID(ctx, tokenInfo.StoreID)
	if err != nil {
		return signingResult, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find configured store for API Key=[%v]",
			tokenInfo.APIKeyID,
		)
	}

	if !store.IsLicensed {
		utils.Log.Infof("failed to sign creditNote for unlicensed store=[%v]", store.Name)
		return nil, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("failed to sign creditNote for unlicensed store"),
			utils.ErrorCodeUnauthorized,
			"failed to sign creditNote for unlicensed store",
			"cannot sign creditNote=[%v] for unlicensed store",
			form.CreditNoteNumber,
		)
	}

	// originalInvoiceNumber := fmt.Sprintf("%v", form.OriginalInvoiceNumber)
	originalInvoiceNumber := strconv.FormatInt(form.OriginalInvoiceNumber, 10)
	utils.Log.Infof("originalInvoiceNumber=[%v]", originalInvoiceNumber)

	if form.OriginalInvoiceNumber == 0 {
		utils.Log.Infof("the original invoice number is required to sign a credit note=[%v]", form.CreditNoteNumber)
		return nil, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("the original invoice number is required to sign a credit note"),
			utils.ErrorCodeInvalidArgument,
			"the original invoice number is required to sign a credit note",
			"cannot sign creditNote=[%v] without an original invoice number",
			form.CreditNoteNumber,
		)
	}

	// Validate customer PIN
	customerPIN := strings.TrimSpace(form.CustomerPIN)
	if customerPIN != "" {
		if len(customerPIN) != 11 {
			customerPIN = ""

			return nil, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("failed to sign creditNote with invalid customer PIN"),
				utils.ErrorCodeInvalidArgument,
				"failed to sign creditNote with invalid customer PIN",
				"cannot sign creditNote with invalid customer PIN=[%v]",
				form.CustomerPIN,
			)
		}
	}

	invoice := &entities.Invoice{
		CustomerName:          form.CustomerName,
		CustomerTIN:           customerPIN,
		DocumentTypeCode:      utils.String("R"), // S for Sale, R for Credit Note
		GrandTotal:            form.Total,
		InvoiceNumber:         creditNoteNumber,
		IsCreditNote:          true,
		OriginalInvoiceNumber: originalInvoiceNumber,
		FileName:              originalInvoiceNumber,
		OriginalFileName:      originalInvoiceNumber,
		StoreID:               &store.ID,
		Vat:                   form.Vat,
	}

	existingInvoice, err := s.invoiceRepository.GetCreditNoteByCreditNoteNumber(ctx, creditNoteNumber, store.ID)
	if err != nil && !utils.IsErrNoRows(err) {
		utils.Log.Infof("failed to find existing invoice by creditNoteNumber=[%v], err=[%v]\n", creditNoteNumber, err)
		return signingResult, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find existing invoice by creditNoteNumber=[%v]",
			creditNoteNumber,
		)
	}

	if err == nil && existingInvoice.ID > 0 && existingInvoice.EtimsReceiptSignature != nil {
		signingResult.InvoiceNumber = existingInvoice.InvoiceNumber
		invoice = existingInvoice
		invoice.StoreID = &store.ID

		signingResult.ETIMSSDCID = invoice.EtimsSDCID
		signingResult.ETIMSInternalData = invoice.EtimsInternalData
		signingResult.EtimsMrcNumber = invoice.EtimsMrcNumber
		signingResult.ETIMSReceiptSignature = invoice.EtimsReceiptSignature
		signingResult.EtimsResultDateTime = invoice.EtimsResultDateTime
		signingResult.EtimsTotalReceiptNumber = invoice.EtimsTotalReceiptNumber
		signingResult.EtimsVSCUReceiptPublicationDate = invoice.EtimsVSCUReceiptPublicationDate
		signingResult.InvoiceNumber = creditNoteNumber
		signingResult.VerificationURL = invoice.VerificationURL

		return signingResult, nil
	}

	if len(form.InvoiceItems) == 0 {
		utils.Log.Infof("failed to sign creditNote for empty item list=[%v]", form.CreditNoteNumber)
		return nil, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("failed to sign creditNote for empty item list"),
			utils.ErrorCodeInvalidArgument,
			"failed to sign creditNote for empty item list",
			"cannot sign creditNote=[%v] for empty item list",
			form.CreditNoteNumber,
		)
	}

	for i, formInvoiceItem := range form.InvoiceItems {
		err = s.loadItem(
			invoice,
			i+1, // Populate ItemID from automated items count
			formInvoiceItem,
		)

		if err != nil {
			return signingResult, err
		}
	}

	// Save data to DB
	err = s.saveCreditNote(ctx, transactionsDB, invoice)
	if err != nil {
		return signingResult, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to save invoice creditNoteNumber=[%v]",
			creditNoteNumber,
		)
	}

	// Production signing
	utils.Log.Infof("signCreditNoteViaKRAETIMS for store=[%v], id=[%v]", store.Name, store.ID)
	// Sign invoice via KRA E-Tims
	err = s.signCreditNoteViaKRAETIMS(ctx, transactionsDB, store, invoice)
	if err != nil {
		utils.Log.Infof("unable to sign invoice via KRA ETIMS, err=[%v]", err)
	}

	signingResult.ETIMSSDCID = invoice.EtimsSDCID
	signingResult.ETIMSInternalData = invoice.EtimsInternalData
	signingResult.EtimsMrcNumber = invoice.EtimsMrcNumber
	signingResult.ETIMSReceiptSignature = invoice.EtimsReceiptSignature
	signingResult.EtimsResultDateTime = invoice.EtimsResultDateTime
	signingResult.EtimsTotalReceiptNumber = invoice.EtimsTotalReceiptNumber
	signingResult.EtimsVSCUReceiptPublicationDate = invoice.EtimsVSCUReceiptPublicationDate
	signingResult.InvoiceNumber = creditNoteNumber
	signingResult.VerificationURL = invoice.VerificationURL

	// Increment credit note count
	// Wrap in go routine
	go func() {
		err = s.dashboardAnalyticsService.IncrementCreditNoteCount(ctx, store.ID)
		if err != nil {
			utils.Log.Infof("failed to increment credit note count for store=[%v]", store.Name)
		}
	}()

	return signingResult, nil
}

func (s *AppCreditNoteService) loadItem(
	invoice *entities.Invoice,
	itemID int,
	form forms.ETIMSInvoiceItemForm,
) error {

	if !entities.EtimsTaxType(form.TaxRate).IsValid() {
		return utils.NewErrorWithErrorCodeAndMessage(
			errors.New("invalid tax rate"),
			utils.ErrorCodeResourceNotFound,
			"invalid tax rate",
			"taxRate=[%v] not found",
			form.TaxRate,
		)
	}

	itemName := fmt.Sprintf("%v", form.ItemName)

	if form.TaxRate == entities.EtimsTaxTypeB.String() {
		invoice.HasVAT = true
		invoice.ItemThatHasVAT = itemName
	}

	var etimsItemCode string
	formEtimsItemCode := strings.TrimSpace(form.EtimsItemCode)
	if formEtimsItemCode != "" && len(formEtimsItemCode) >= 8 {
		etimsItemCode = formEtimsItemCode
	} else {
		etimsItemCode = utils.GenerateKRAItemCode(itemName, 20)
	}

	itemUUID := uuid.New().String()

	unitPrice := utils.RoundFloat64(form.UnitPrice, 2)

	qty := form.ItemQty
	if qty == 0 {
		qty = 1
	}

	// Load ETIMS Item
	etimsItem := &entities.EtimsItem{
		AddInfo:     utils.GenerateItemCode(itemName, 7),
		BhfID:       "00",
		DcAmt:       utils.RoundFloat64(form.DiscountAmount, 2),
		DcRt:        form.DiscountRate,
		DftPrc:      unitPrice,
		ItemCd:      etimsItemCode,
		ItemClsCd:   utils.GenerateItemCode(itemName, 5),
		ItemNm:      itemName,
		ItemTyCd:    entities.EtimsProductTypeService.String(),
		ItemStdNm:   itemName,
		IsrcAplcbYn: "N",
		ModrID:      "Admin",
		ModrNm:      "Admin",
		OrgnNatCd:   "KE",
		PkgUnitCd:   "BG",
		QtyUnitCd:   "U",
		RegrID:      "Admin",
		RegrNm:      "Admin",
		SftyQty:     qty,
		TaxTyCd:     entities.EtimsTaxTypeB.String(),
		Tin:         "P052223456Z",
		UseYn:       "Y",
		UUID:        itemUUID,
	}
	etimsItem.Timestamps.Touch()

	invoice.EtimsItems = append(invoice.EtimsItems, etimsItem)

	// Load item
	item := &entities.InvoiceItem{
		Amount:       unitPrice,
		ItemID:       itemID,
		ItemName:     itemName,
		Name:         itemName,
		Quantity:     qty,
		SerialNumber: fmt.Sprintf("%v", itemID),
		Total:        form.TotalAmt,
		UUID:         itemUUID,
	}
	item.Timestamps.Touch()

	invoice.InvoiceItems = append(invoice.InvoiceItems, item)

	return nil
}

func (s *AppCreditNoteService) saveCreditNote(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
	invoice *entities.Invoice,
) error {

	err := transactionsDB.InTransaction(ctx, func(ctx context.Context, operations txns_db.TransactionsSQLOperations) error {

		err := s.invoiceRepository.Save(ctx, operations, invoice)
		if err != nil {
			return err
		}

		// save invoice items
		for _, item := range invoice.InvoiceItems {
			// Attach invoice id to invoice item, the rest of the details are already in place
			item.InvoiceID = invoice.ID
		}

		err = s.invoiceItemRepository.SaveMultiple(ctx, operations, invoice.InvoiceItems)
		if err != nil {
			return err
		}

		// Save ETIMS Items
		err = s.etimsItemRepository.SaveMultiple(ctx, operations, invoice.EtimsItems)
		if err != nil {
			return err
		}

		return err
	})

	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to save invoice with invoiceNumber=[%v]",
			invoice.InvoiceNumber,
		)
	}

	return nil
}

func (s *AppCreditNoteService) signCreditNoteViaKRAETIMS(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	store *entities.Store,
	invoice *entities.Invoice,
) error {

	utils.Log.Infof("Signing creditNote=[%v] via etims...", invoice.InvoiceNumber)

	// Load the Africa/Nairobi timezone location
	location, err := time.LoadLocation("Africa/Nairobi")
	if err != nil {
		utils.Log.Infof("Error loading location, err=[%v]", err)
		return err
	}

	// Get the current time in the Nairobi timezone
	timeNow := time.Now().In(location)
	commonDate := utils.FormatTime(timeNow, "20060102030405") // yyyyMMddhhmmss

	receiptNumber := utils.ConvertStringToInt64(invoice.InvoiceNumber)

	itemsCount := len(invoice.InvoiceItems)

	customerName := invoice.CustomerName
	if len(customerName) >= 20 {
		customerName = customerName[:19]
	}

	storeName := store.Name
	if len(storeName) >= 20 {
		storeName = storeName[:19]
	}

	trimmedTraderName := strings.ReplaceAll(store.Name, " ", "")
	if len(trimmedTraderName) >= 20 {
		trimmedTraderName = trimmedTraderName[:19]
	}

	etimsReceiptForm := &forms.EtimsReceiptForm{
		Adrs:         "Nairobi,Kenya",
		BtmMsg:       store.Name,
		CustMblNo:    invoice.CustomerPhone,
		CustTin:      invoice.CustomerTIN,
		PrchrAcptcYn: "Y",
		RptNo:        receiptNumber,
		TopMsg:       storeName,
		TrdeNm:       customerName,
	}

	var etimsItems []*forms.EtimsItemForm
	itemSeqCounter := 1

	for _, etimsItem := range invoice.EtimsItems {

		var vatFloat64 float64

		if etimsItem.TaxTyCd == entities.EtimsTaxTypeB.String() {
			vat := etimsItem.DftPrc * 0.1379 * etimsItem.SftyQty
			vatStr := fmt.Sprintf("%.2f", vat)
			vatFloat64 = utils.ConvertStringToFloat64(vatStr)
		}

		taxableAmount := etimsItem.DftPrc * etimsItem.SftyQty
		taxableAmountStr := fmt.Sprintf("%.2f", taxableAmount)
		taxableAmountFloat64 := utils.ConvertStringToFloat64(taxableAmountStr)

		totalItemAmountFloat64 := taxableAmountFloat64
		// taxableAmountFloat64 = taxableAmountFloat64 - vatFloat64

		etimsItem := &forms.EtimsItemForm{
			ItemSeq:   itemSeqCounter,
			ItemCd:    etimsItem.ItemCd,
			ItemClsCd: etimsItem.ItemClsCd,
			ItemNm:    etimsItem.ItemNm,
			Bcd:       etimsItem.Bcd,
			PkgUnitCd: etimsItem.PkgUnitCd,
			Pkg:       etimsItem.SftyQty,
			QtyUnitCd: etimsItem.QtyUnitCd,
			Qty:       etimsItem.SftyQty,
			Prc:       etimsItem.DftPrc,
			SplyAmt:   taxableAmount,
			DcRt:      0.00,
			DcAmt:     0.00,
			IsrccCd:   "N",
			TaxTyCd:   etimsItem.TaxTyCd,
			TaxblAmt:  utils.RoundFloat64(taxableAmountFloat64, 2),
			TaxAmt:    vatFloat64,
			TotAmt:    totalItemAmountFloat64,
		}

		etimsItems = append(etimsItems, etimsItem)
		itemSeqCounter++
	}

	// invoiceNumber := invoice.InvoiceNumber
	invoiceNumber := utils.ConvertStringToInt64(invoice.InvoiceNumber)

	originalInvoiceNumber := utils.ConvertStringToInt64(invoice.OriginalInvoiceNumber)

	totalVat := utils.RoundFloat64(invoice.Vat, 2)
	taxAmountB := utils.RoundFloat64(invoice.GrandTotal, 2)

	// TODO: Clarify on changes with KRA on Total Taxable Amount B
	// totalTaxableAmount := invoice.GrandTotal - invoice.Vat
	// totalTaxableAmountStr := fmt.Sprintf("%.2f", totalTaxableAmount)
	// totalTaxableAmountFloat64 := utils.ConvertStringToFloat64(totalTaxableAmountStr)

	// totalTaxableAmountFloat64 = utils.RoundFloat64(totalTaxableAmountFloat64, 2)

	// taxAmountB := totalTaxableAmountFloat64

	invoiceGrandTotal := utils.RoundFloat64(invoice.GrandTotal, 2)

	// Construct etims sale form
	form := &forms.CreateEtimsSalesForm{
		Tin:          store.PIN,
		BhfID:        store.EtimsBranch,
		TrdInvcNo:    invoiceNumber,
		InvcNo:       invoiceNumber,
		OrgInvcNo:    originalInvoiceNumber,
		CustTin:      invoice.CustomerTIN,
		CustNm:       invoice.CustomerName,
		SalesTyCd:    "N",
		RcptTyCd:     "R", // S for Sale, R for Credit Note
		PmtTyCd:      entities.ETIMSPaymentMethodCash.String(),
		SalesSttsCd:  entities.TransactionProgressApproved.String(),
		CfmDt:        commonDate,
		SalesDt:      utils.FormatTime(timeNow, "20060102"), // yyyyMMdd
		StockRlsDt:   commonDate,
		TotItemCnt:   itemsCount,
		TaxblAmtA:    0.00,
		TaxblAmtB:    taxAmountB,
		TaxblAmtC:    0.00,
		TaxblAmtD:    0.00,
		TaxblAmtE:    0.00,
		TaxRtA:       0.00,
		TaxRtB:       16.00,
		TaxRtC:       0.00,
		TaxRtD:       0.00,
		TaxRtE:       0.00,
		TaxAmtA:      0.00,
		TaxAmtB:      totalVat,
		TaxAmtC:      0.00,
		TaxAmtD:      0.00,
		TaxAmtE:      0.00,
		TotTaxAmt:    totalVat,
		TotTaxblAmt:  taxAmountB,
		TotAmt:       invoiceGrandTotal,
		TrdeNm:       trimmedTraderName,
		PrchrAcptcYn: "N",
		Remark:       fmt.Sprintf("Sale invoice number %v", invoice.InvoiceNumber),
		RegrID:       "11999",
		RegrNm:       trimmedTraderName,
		ModrID:       "45678",
		ModrNm:       trimmedTraderName,
		Receipt:      etimsReceiptForm,
		ItemList:     etimsItems,
	}

	if invoice.IsCreditNote {
		form.RcptTyCd = "R" // S for Sale, R for Credit Note
	}

	// For debugging purposes only
	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal VFMS sales form payload, err=[%v]", err)
		return err
	}
	utils.PrettyPrintJSON(payloadBytes)

	// Send to etims vscu provider
	response, err := s.etimsVSCU.SaveSale(form)
	if err != nil {
		logger.Errorf("failed to post sale to etims, err=[%v]", err)
		return err
	}

	utils.Log.Infof("etims save sale response code=[%v]", response.ResultCd)
	// utils.PrintStructToConsole(response)

	if response.Status == 400 {
		logger.Errorf("failed to post sale to etims, err=[%v]", err)
		return err
	}

	if response.ResultCd == "000" {
		// Update signature and verification url only on successful KRA response
		invoice.EtimsInternalData = &response.Data.IntrlData
		invoice.EtimsReceiptSignature = &response.Data.RcptSign
		invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
		invoice.EtimsTotalReceiptNumber = &response.Data.RcptNo
		invoice.EtimsReceiptNumber = &receiptNumber
		invoice.EtimsVSCUReceiptPublicationDate = &response.Data.VsdcRcptPbctDate

		// signature := fmt.Sprintf("%v/%v %v %v", response.Data.SdcID, response.Data.RcptNo, response.Data.RcptSign, response.Data.VsdcRcptPbctDate)
		invoice.Signature = response.Data.RcptSign
		invoice.SignedDate = utils.FormatTime(time.Now(), "20060102150405")

	} else if response.ResultCd == "924" && invoice.Signature == "" && invoice.VerificationURL == "" {
		// Update signature and verification url only on successful KRA response
		invoice.EtimsInternalData = &response.Data.IntrlData
		invoice.EtimsMrcNumber = &response.Data.MrcNo
		invoice.EtimsReceiptSignature = &response.Data.RcptSign
		invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
		invoice.EtimsTotalReceiptNumber = &response.Data.RcptNo
		invoice.EtimsVSCUReceiptPublicationDate = &response.Data.VsdcRcptPbctDate

	} else {
		return err
	}

	if response.Data != nil {
		invoice.EtimsInternalData = &response.Data.IntrlData
		invoice.EtimsMrcNumber = &response.Data.MrcNo
		invoice.EtimsReceiptNumber = &response.Data.RcptNo
		invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
		invoice.EtimsResultCode = &response.ResultCd
		invoice.EtimsResultMessage = &response.ResultMsg
		invoice.EtimsSDCID = &response.Data.SdcID
	}

	utils.Log.Infof("invoice.Signature=[%v]", invoice.Signature)

	if invoice.Signature != "" {

		// To check if client is live - if live use prod, otherwise use sandbox
		apiBaseURL := s.etimsSandboxAPIURL

		if store.EtimsEnvironment == "production" {
			apiBaseURL = s.etimsProductionAPIURL
		}

		signatureData := fmt.Sprintf("%v%v%v", store.PIN, store.EtimsBranch, invoice.Signature)
		verificationURL := fmt.Sprintf("%v/common/link/etims/receipt/indexEtimsReceiptData?Data=%v", apiBaseURL, signatureData)

		utils.Log.Infof("verificationURL=[%v]", verificationURL)

		invoice.VerificationURL = verificationURL

		// Save response recieved by updating the current invoice
		err = s.invoiceRepository.Save(ctx, operations, invoice)
		if err != nil {
			logger.Errorf("failed to update signed invoice, err=[%v]", err)
			return err
		}
	}

	return nil
}
