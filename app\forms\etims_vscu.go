package forms

type (
	ETIMSCreditNoteForm struct {
		CustomerName          string                 `json:"customer_name" binding:"required" `
		CustomerPIN           string                 `json:"customer_pin"`
		CreditNoteNumber      int64                  `json:"credit_note_number" binding:"required"`
		InvoiceItems          []ETIMSInvoiceItemForm `json:"invoice_items" binding:"required"`
		OriginalInvoiceNumber int64                  `json:"original_invoice_number"  binding:"required"`
		Total                 float64                `json:"total" binding:"required"`
		Vat                   float64                `json:"vat"`
	}

	ETIMSInvoiceForm struct {
		CustomerName  string                 `json:"customer_name" binding:"required"`
		CustomerPIN   string                 `json:"customer_pin"`
		InvoiceNumber int64                  `json:"invoice_number" binding:"required"`
		InvoiceItems  []ETIMSInvoiceItemForm `json:"invoice_items" binding:"required"`
		Total         float64                `json:"total" binding:"required"`
		Vat           float64                `json:"vat"`
	}

	ETIMSInvoiceItemForm struct {
		CostPrice      float64 `json:"cost_price" default:"0.00"`
		Description    string  `json:"description"`
		DiscountAmount float64 `json:"discount_amount" default:"0.00"`
		DiscountRate   float64 `json:"discount_rate" default:"0.00"`
		EtimsItemCode  string  `json:"etims_item_code"`
		ItemName       string  `json:"item_name" binding:"required"`
		ItemQty        float64 `json:"item_qty" binding:"required"`
		RetailPrice    float64 `json:"retail_price" binding:"required"`
		TaxRate        string  `json:"tax_rate" binding:"required"`
		TaxTypeCode    string  `json:"tax_type_code" binding:"required"`
		TotalAmt       float64 `json:"total_amount"`
		UnitPrice      float64 `json:"unit_price" binding:"required"`
	}
)
