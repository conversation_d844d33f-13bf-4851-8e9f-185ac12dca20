package repos

import (
	"context"
	"database/sql"
	"log"
	"struts-pos-backend/app/entities"
	"time"
)

const (
	createActivitySQL = ` INSERT INTO activities (user_id, activity_type, description, source_ip, resource_id, created_at)
		VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`

	selectActivityByIDSQL = selectActivitySQL + ` WHERE id=$1`

	selectActivitySQL = `SELECT id, user_id, activity_type, description, source_ip, resource_id, created_at FROM activities`
)

type (
	ActivityRepository interface {
		CreateActivity(ctx context.Context, activity *entities.Activity) error
		GetActivityByID(ctx context.Context, activityID int64) (*entities.Activity, error)
	}

	AppActivityRepository struct {
		db *sql.DB
	}
)

func NewActivityRepository(db *sql.DB) ActivityRepository {
	return &AppActivityRepository{db: db}
}

func (r *AppActivityRepository) CreateActivity(
	ctx context.Context,
	activity *entities.Activity,
) error {

	err := r.db.QueryRow(
		createActivitySQL,
		activity.UserID,
		activity.ActivityType,
		activity.Description,
		activity.SourceIP,
		activity.ResourceID,
		time.Now(),
	).Scan(&activity.ID)

	if err != nil {
		log.Printf("error saving activity, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppActivityRepository) GetActivityByID(
	ctx context.Context,
	activityID int64,
) (*entities.Activity, error) {
	row := r.db.QueryRow(selectActivityByIDSQL, activityID)
	return r.scanRowIntoActivity(row)
}

func (r *AppActivityRepository) scanRowIntoActivity(
	row *sql.Row,
) (*entities.Activity, error) {

	var activity entities.Activity

	err := row.Scan(
		&activity.ID,
		&activity.UserID,
		&activity.ActivityType,
		&activity.Description,
		&activity.SourceIP,
		&activity.ResourceID,
	)

	if err != nil {
		log.Printf("error scanning activity,  err=[%v]\n", err.Error())
		return &activity, err
	}

	return &activity, nil
}
