package users

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/web/auth"

	"struts-pos-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.GET("/users", filterUsers(transactionsDB, userService))
		protectedAPI.GET("/users/:id", getUser(transactionsDB, userService))
	}

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.POST("/users", registerUser(transactionsDB, userService))
	}

}
