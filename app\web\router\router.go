package router

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/services"

	"struts-pos-backend/app/web/api/analytics"
	"struts-pos-backend/app/web/api/api_keys"
	"struts-pos-backend/app/web/api/categories"
	"struts-pos-backend/app/web/api/credit_notes"
	"struts-pos-backend/app/web/api/currencies"
	"struts-pos-backend/app/web/api/customers"
	"struts-pos-backend/app/web/api/etims_items"
	"struts-pos-backend/app/web/api/etims_oscu"
	"struts-pos-backend/app/web/api/etims_stock"
	"struts-pos-backend/app/web/api/etims_vscu"
	"struts-pos-backend/app/web/api/excel_file_uploads"
	"struts-pos-backend/app/web/api/instagram"
	"struts-pos-backend/app/web/api/invoices"
	"struts-pos-backend/app/web/api/items"
	"struts-pos-backend/app/web/api/payments"
	"struts-pos-backend/app/web/api/receivings"
	"struts-pos-backend/app/web/api/sales"
	"struts-pos-backend/app/web/api/sessions"
	"struts-pos-backend/app/web/api/settings"
	"struts-pos-backend/app/web/api/stores"
	"struts-pos-backend/app/web/api/suppliers"
	"struts-pos-backend/app/web/api/users"
	"struts-pos-backend/app/web/auth"
	analytics_ui "struts-pos-backend/app/web/ui/analytics"
	dashboard_ui "struts-pos-backend/app/web/ui/dashboard"
	invoices_ui "struts-pos-backend/app/web/ui/invoices"
	login_ui "struts-pos-backend/app/web/ui/login"
	logout_ui "struts-pos-backend/app/web/ui/logout"

	"github.com/gin-gonic/gin"
)

func AddStrutsOptimusEndpoints(
	appRouter *gin.Engine,
	transactionsDB *database.AppTransactionsDB,
	sessionAuthenticator auth.SessionAuthenticator,
	activityRepository repos.ActivityRepository,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	creditNoteService services.CreditNoteService,
	etimsItemService services.EtimsItemService,
	etimsStockService services.EtimsStockService,
	etimsVSCUService services.EtimsVSCUService,
	excelFileUploadService services.ExcelFileUploadService,
	userRepository repos.UserRepository,
	analyticsService services.AnalyticsService,
	categoryService services.CategoryService,
	currencyService services.CurrencyService,
	customerService services.CustomerService,
	invoiceService services.InvoiceService,
	itemService services.ItemService,
	paymentService services.PaymentService,
	paymentTransactionsService services.PaymentTransactionsService,
	receivingService services.ReceivingService,
	saleService services.SaleService,
	settingService services.SettingService,
	sessionService services.SessionService,
	storeRepository repos.StoreRepository,
	storeService services.StoreService,
	supplierService services.SupplierService,
	userService services.UserService,
) {

	strutsRouterGroup := appRouter.Group("/api")

	analytics.AddEndpoints(strutsRouterGroup, transactionsDB, analyticsService, sessionAuthenticator, sessionService, storeRepository)
	api_keys.AddEndpoints(strutsRouterGroup, transactionsDB, apiKeyService, sessionAuthenticator, sessionService)
	categories.AddEndpoints(strutsRouterGroup, transactionsDB, categoryService, sessionAuthenticator, sessionService)

	credit_notes.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		creditNoteService,
		sessionAuthenticator,
		sessionService,
	)

	currencies.AddEndpoints(strutsRouterGroup, transactionsDB, currencyService, sessionAuthenticator, sessionService)
	customers.AddEndpoints(strutsRouterGroup, transactionsDB, customerService, sessionAuthenticator, sessionService)

	etims_oscu.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		apiKeyService,
		etimsVSCUService,
		sessionAuthenticator,
		sessionService,
	)

	etims_vscu.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		apiKeyService,
		etimsVSCUService,
		sessionAuthenticator,
		sessionService,
	)

	etims_items.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		apiKeyService,
		etimsItemService,
		sessionAuthenticator,
		sessionService,
	)

	etims_stock.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		apiKeyService,
		etimsStockService,
		sessionAuthenticator,
		sessionService,
	)

	excel_file_uploads.AddEndpoints(strutsRouterGroup, transactionsDB, excelFileUploadService, sessionAuthenticator, sessionService)

	instagram.AddEndpoints(strutsRouterGroup, transactionsDB, apiKeyService, sessionAuthenticator, sessionService)
	invoices.AddEndpoints(strutsRouterGroup, transactionsDB, invoiceService, sessionAuthenticator, sessionService)
	items.AddEndpoints(strutsRouterGroup, transactionsDB, itemService, sessionAuthenticator, sessionService)

	payments.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		paymentService,
		paymentTransactionsService,
		sessionAuthenticator,
		sessionService,
	)

	receivings.AddEndpoints(strutsRouterGroup, transactionsDB, receivingService, sessionAuthenticator, sessionService)
	sales.AddEndpoints(strutsRouterGroup, transactionsDB, saleService, sessionAuthenticator, sessionService)
	sessions.AddEndpoints(strutsRouterGroup, transactionsDB, userService)
	settings.AddEndpoints(strutsRouterGroup, transactionsDB, settingService, sessionAuthenticator, sessionService)
	stores.AddEndpoints(strutsRouterGroup, transactionsDB, storeService, sessionAuthenticator, sessionService)
	suppliers.AddEndpoints(strutsRouterGroup, transactionsDB, supplierService, sessionAuthenticator, sessionService)
	users.AddEndpoints(strutsRouterGroup, transactionsDB, userService, sessionAuthenticator, sessionService)

	uiRouterGroup := appRouter.Group("/go")

	analytics_ui.AddEndpoints(uiRouterGroup, transactionsDB, analyticsService)
	dashboard_ui.AddEndpoints(uiRouterGroup, transactionsDB, analyticsService)
	invoices_ui.AddEndpoints(appRouter, uiRouterGroup, transactionsDB, analyticsService, invoiceService)
	logout_ui.AddEndpoints(uiRouterGroup, transactionsDB, userService)
	login_ui.AddEndpoints(uiRouterGroup, transactionsDB, userService)
}
