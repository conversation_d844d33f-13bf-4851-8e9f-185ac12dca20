package forms

import "struts-pos-backend/app/entities"

type (
	Create<PERSON>aystackPlanSubscriptionForm struct {
		CustomerCode string `json:"customer_code"`
		PlanCode     string `json:"plan_code"`
	}

	CreatePaymentPlanForm struct {
		Amount           float64                  `binding:"required" json:"amount"`
		CurrencyID       int64                    `binding:"required" json:"currency_id"`
		Description      string                   `json:"description"`
		Interval         entities.PaymentInterval `binding:"required" json:"interval"` // daily, weekly, monthly,quarterly, biannually, annually.
		Name             string                   `binding:"required" json:"name"`
		PaymentPackageID int64                    `binding:"required" json:"payment_package_id"`
	}

	UpdatePaymentPlanForm struct {
		Amount           float64                  `json:"amount"`
		Description      string                   `json:"description"`
		Interval         entities.PaymentInterval `json:"interval"` // daily, weekly, monthly,quarterly, biannually, annually.
		Name             string                   `json:"name"`
		PaymentPackageID int64                    `json:"payment_package_id"`
	}
)
