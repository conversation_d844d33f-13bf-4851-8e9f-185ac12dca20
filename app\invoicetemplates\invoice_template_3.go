package invoicetemplates

import (
	"bufio"
	"log"
	"struts-pos-backend/app/models"
	"struts-pos-backend/app/utils"
	"os"
	"strings"
)

// ProcessTemplateThreeInvoice - ProcessTemplateThreeInvoice
func ProcessTemplateThreeInvoice(textFile string) models.Invoice {

	log.Println("Processing Template THREE Invoice File >>>> ", textFile)
	invoice := models.Invoice{}

	file, err := os.Open(textFile)
	utils.CheckError(err, "Error reading signed input file >>> "+textFile)
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Split(bufio.ScanLines)

	var lines []string
	var lineNumber = 1
	var lastLine = ""

	for scanner.Scan() {

		invoiceLine := scanner.Text()
		invoiceLine = strings.ToUpper(invoiceLine)
		trimedInvoiceLine := strings.Replace(invoiceLine, " ", "", -1)
		trimmedInvoiceLine := strings.TrimSpace(invoiceLine)

		lines = append(lines, invoiceLine)
		log.Println("[TMPL_3] invoiceLine ["+utils.ConvertIntToString(lineNumber)+"]  >>> ", invoiceLine)

		if strings.Contains(invoiceLine, "MONTH") {

			lineArr := strings.Split(invoiceLine, " ")
			invoiceNumber := lineArr[0]
			log.Println("[TMPL_3] invoiceNumber >>> ", invoiceNumber)
			invoice.InvoiceNumber = invoiceNumber

		} else if strings.Contains(invoiceLine, "DAYS") {

			lineArr := strings.Split(invoiceLine, " ")
			invoiceNumber := lineArr[0]
			log.Println("[TMPL_3] invoiceNumber >>> ", invoiceNumber)
			invoice.InvoiceNumber = invoiceNumber

		} else if strings.Contains(invoiceLine, "PREPAYMENT") {

			lineArr := strings.Split(invoiceLine, " ")
			invoiceNumber := lineArr[0]
			log.Println("[TMPL_3] invoiceNumber >>> ", invoiceNumber)
			invoice.InvoiceNumber = invoiceNumber

		} else if strings.Contains(invoiceLine, "DATE:") {

			invoiceLine = strings.TrimSpace(invoiceLine)
			lineArr := strings.Split(invoiceLine, " ")
			invoiceDate := lineArr[1] + " " + lineArr[2] + " " + lineArr[3]
			log.Println("[TMPL_3] invoiceDate   >>> ", invoiceDate)

			formatedDate := utils.FormatDateTypeTwoString(invoiceDate)
			invoice.InvoiceDate = invoiceDate
			invoice.DateCreated = formatedDate

		} else if strings.Contains(invoiceLine, "#") && strings.Contains(invoiceLine, "/") {
			invoiceSignature := strings.Replace(invoiceLine, " ", "", -1)
			invoice.Signature = invoiceSignature
		}

		if len(trimedInvoiceLine) > 1 && !strings.Contains(invoiceLine, "#") && strings.Contains(trimmedInvoiceLine, " ") {
			lastLine = invoiceLine
		}

		lineNumber++
	}

	lastLine = strings.TrimSpace(lastLine)
	log.Println("[TMPL_3] lastLine >>> ", lastLine)
	lineArr := strings.Split(lastLine, " ")
	vat, grandTotal := lineArr[0], lineArr[1]

	vat = strings.Replace(vat, ",", "", -1)
	grandTotal = strings.Replace(grandTotal, ",", "", -1)

	log.Println("[TMPL_3] invoice VAT >>> ", vat)
	log.Println("[TMPL_3] invoice Grand Total >>> ", grandTotal)

	invoice.Vat = vat
	invoice.GrandTotal = grandTotal

	return invoice
}
