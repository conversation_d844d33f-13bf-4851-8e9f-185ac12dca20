// Package strutspos provides ETIMS VSCU core functionality
package providers

import (
	"context"
	"errors"
	"fmt"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/utils"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// Sales and receipt type constants
const (
	SalesTypeN              = "N"
	SalesTypeT              = "T"
	SalesTypeP              = "P"
	SalesTypeC              = "C"
	ReceiptTypeS            = "S"
	ReceiptTypeR            = "R"
	SerialInvNSR            = "serlInvNsr"
	SerialInvTSR            = "serlInvTsr"
	SerialInvPS             = "serlInvPs"
	SerialInvCSR            = "serlInvCsr"
	ResultCodeSuccess       = "000"
	ResultCodeTypeError     = "834"
	ResultCodeSequenceError = "836"
	ResultCodeGeneralError  = "899"
	ErrorTypeMsg            = "SalesType and ReceiptType must be NS-NR-TS-TR-CS-CR-PS check your inputs."
	ErrorSequenceMsg        = "Your Sequences have been altered, Connect to KRA API to get Sequences."
	ErrorDeviceDataMsg      = "Failed to retrieve device data, please check configuration."
)

// Custom errors
var (
	ErrMissingParams = errors.New("missing required parameters")
	ErrTypeError     = errors.New(ErrorTypeMsg)
	ErrSequenceError = errors.New(ErrorSequenceMsg)
	ErrDeviceData    = errors.New(ErrorDeviceDataMsg)
)

// DeviceDataError represents a device data retrieval error
type DeviceDataError struct {
	Msg string
	Err error
}

// Error implements the error interface
func (e *DeviceDataError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Msg, e.Err)
	}
	return e.Msg
}

// Unwrap returns the underlying error
func (e *DeviceDataError) Unwrap() error {
	return e.Err
}

// DeviceManager interface defines methods to get device information
// type DeviceManager interface {
// 	GetDeviceSerialNumber(tinBhfPath string) (string, error)
// 	GetSdcID(tinBhfPath string) (string, error)
// 	GetMrcNo(tinBhfPath string) (string, error)
// }

// SequenceManager interface defines methods to get sequence numbers
type SequenceManager interface {
	GetRcptTypNo(tin, bhfId, dvcSrlNo, invRcptKind, tinBhfPath string) (int64, error)
	GetRcptNo(custTin, bhfId, dvcSrlNo, tinBhfPath string) (int64, error)
	GetRptNo(tinBhfPath string) (int64, error)
}

// InternalManager interface defines methods for internal data and signatures
type InternalManager interface {
	GetInternalData(salesType, receiptType string, taxAmtB float64, rptNo, invcNo int64, tinBhfPath string) (string, error)
	GetSignature(rcptPbctDt, tin, custTin string, invcNo int64, taxblAmtB, taxAmtB, taxblAmtA, taxAmtA float64, salesType, receiptType string, invcNo1, invcNo2 int64, tinBhfPath string) (string, error)
}

// // ApiClient interface defines methods for API interactions
// type ApiClient interface {
// 	// Add methods as needed
// }

// DataResendManager interface defines methods for data resending
type DataResendManager interface {
	// Add methods as needed
}

// CheckApiConnection interface defines methods to check API connection
type CheckApiConnection interface {
	// Add methods as needed
}

// DataZreportManager interface defines methods for Z-report management
type DataZreportManager interface {
	// Add methods as needed
}

// ETIMSService interface defines methods for ETIMS service
type ETIMSService interface {
	SaveReceiptDetailsToDB(req *entities.TrnsSalesSaveWrReq, resData *entities.TrnsSalesSaveWrResData, invRcptKind string) (*EtimsSaveSaleTransaction, error)
}

// EtimsSaveSaleTransaction represents a saved transaction
type EtimsSaveSaleTransaction struct {
	ID int64 `json:"id"`
	// Add other fields as needed
}

// TrnsSalesExecute handles sales transactions
type TrnsSalesExecute struct {
	deviceCache        sync.Map
	deviceManager      DeviceManager
	sequenceManager    SequenceManager
	internalManager    InternalManager
	apiClient          ApiClient
	resendManager      DataResendManager
	checkConnection    CheckApiConnection
	dataZreportManager DataZreportManager
	etimsService       ETIMSService
	logger             zerolog.Logger
}

// NewTrnsSalesExecute creates a new TrnsSalesExecute instance
func NewTrnsSalesExecute(
	deviceManager DeviceManager,
	sequenceManager SequenceManager,
	internalManager InternalManager,
	apiClient ApiClient,
	resendManager DataResendManager,
	checkConnection CheckApiConnection,
	dataZreportManager DataZreportManager,
	etimsService ETIMSService,
) *TrnsSalesExecute {
	return &TrnsSalesExecute{
		deviceManager:      deviceManager,
		sequenceManager:    sequenceManager,
		internalManager:    internalManager,
		apiClient:          apiClient,
		resendManager:      resendManager,
		checkConnection:    checkConnection,
		dataZreportManager: dataZreportManager,
		etimsService:       etimsService,
		logger:             log.With().Str("component", "TrnsSalesExecute").Logger(),
	}
}

// Initialize TrnsSalesExecute
func NewTrnsSalesExecuteInitializer() *TrnsSalesExecute {

	deviceManager, err := NewDeviceManager(DeviceManagerConfig{
		AesKey:         []byte("a1b2c3d4e5f60718293a4b5c6d7e8f90"),
		BaseDevicePath: "C:\\Users\\<USER>\\AppData\\EbmData",
		LogLevel:       zerolog.InfoLevel,
	})

	if err != nil {
		// Handle error
		utils.Log.Infof("failed to create device manager, err=[%v]", err.Error())
		return nil
	}

	// Initialize other managers and services

	return &TrnsSalesExecute{
		deviceManager: *deviceManager,
		logger:        log.With().Str("component", "TrnsSalesExecute").Logger(),
	}
}

// RegisterRoutes registers the HTTP routes with the given router
func (t *TrnsSalesExecute) RegisterRoutes(router *gin.Engine) {
	trnsSales := router.Group("/trnsSales")
	{
		trnsSales.POST("/saveSales", t.SaveTrnsSalesHandler)
	}
}

// SaveTrnsSalesHandler handles the HTTP request for saving a transaction
func (t *TrnsSalesExecute) SaveTrnsSalesHandler(c *gin.Context) {
	var req entities.TrnsSalesSaveWrReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, entities.TrnsSalesSaveWrRes{
			ResultCd:  ResultCodeGeneralError,
			ResultMsg: "Invalid request: " + err.Error(),
		})
		return
	}

	// Perform early validation
	if req.Tin == "" || req.BhfId == "" {
		c.JSON(400, entities.TrnsSalesSaveWrRes{
			ResultCd:  ResultCodeGeneralError,
			ResultMsg: ErrMissingParams.Error(),
		})
		return
	}

	res, err := t.SaveTrnsSales(c.Request.Context(), &req, false)
	if err != nil {
		// Error is already handled inside SaveTrnsSales
		c.JSON(200, res)
		return
	}

	c.JSON(200, res)
}

// getDeviceData safely retrieves cached device data or fetches it if not in cache
func (t *TrnsSalesExecute) getDeviceData(ctx context.Context, cacheKey string, fetchFunc func(string) (string, error), tinBhfPath string, errorMsg string) (string, error) {
	// Check if in cache
	if val, ok := t.deviceCache.Load(cacheKey); ok {
		if strVal, ok := val.(string); ok {
			return strVal, nil
		}
	}

	// Not in cache, fetch the data
	value, err := fetchFunc(tinBhfPath)
	if err != nil {
		t.logger.Error().Err(err).Str("tinBhfPath", tinBhfPath).Msg(errorMsg)
		return "", &DeviceDataError{Msg: errorMsg, Err: err}
	}

	// Cache the result
	if value != "" {
		t.deviceCache.Store(cacheKey, value)
	}

	return value, nil
}

// GetDeviceSerialNumber gets the device serial number
func (t *TrnsSalesExecute) GetDeviceSerialNumber(ctx context.Context, tinBhfPath string) (string, error) {
	cacheKey := "dvcSrlNo_" + tinBhfPath
	return t.getDeviceData(ctx, cacheKey, t.deviceManager.GetDeviceSerialNumber, tinBhfPath,
		"Failed to retrieve device serial number")
}

// GetSdcId gets the SDC ID
func (t *TrnsSalesExecute) GetSdcId(ctx context.Context, tinBhfPath string) (string, error) {
	cacheKey := "sdcId_" + tinBhfPath
	return t.getDeviceData(ctx, cacheKey, t.deviceManager.GetSdcID, tinBhfPath,
		"Failed to retrieve SDC ID")
}

// GetMrcNumber gets the MRC number
func (t *TrnsSalesExecute) GetMrcNumber(ctx context.Context, tinBhfPath string) (string, error) {
	cacheKey := "mrcNo_" + tinBhfPath
	return t.getDeviceData(ctx, cacheKey, t.deviceManager.GetMrcNo, tinBhfPath,
		"Failed to retrieve MRC number")
}

// SaveTrnsSales processes and saves a transaction
func (t *TrnsSalesExecute) SaveTrnsSales(ctx context.Context, req *entities.TrnsSalesSaveWrReq, reSend bool) (*entities.TrnsSalesSaveWrRes, error) {
	res := &entities.TrnsSalesSaveWrRes{}
	tinBhfPath := req.Tin + "_" + req.BhfId
	var dvcSrlNo string
	var invRcptKind string
	var rcptNo int64
	var rcptTpNo int64
	var vsdcRcptPbctDate string

	t.logger.Info().Str("tinBhfPath", tinBhfPath).Msg("Processing transaction")

	if !reSend {
		// Get device serial number
		var err error
		dvcSrlNo, err = t.GetDeviceSerialNumber(ctx, tinBhfPath)
		if err != nil {
			t.logger.Error().Err(err).Str("tinBhfPath", tinBhfPath).Msg("Failed to get device serial number")
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeGeneralError,
				ResultMsg: ErrorDeviceDataMsg + ": " + err.Error(),
			}, err
		}

		// Process based on sales type and receipt type
		salesType := req.SalesTyCd
		receiptType := req.RcptTyCd

		switch salesType {
		case SalesTypeN:
			if receiptType == ReceiptTypeS || receiptType == ReceiptTypeR {
				invRcptKind = SerialInvNSR
				rcptTpNo, err = t.sequenceManager.GetRcptTypNo(req.Tin, req.BhfId, dvcSrlNo, invRcptKind, tinBhfPath)
			} else {
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeTypeError,
					ResultMsg: ErrorTypeMsg,
				}, ErrTypeError
			}
		case SalesTypeT:
			if receiptType == ReceiptTypeS || receiptType == ReceiptTypeR {
				invRcptKind = SerialInvTSR
				rcptTpNo, err = t.sequenceManager.GetRcptTypNo(req.CustTin, req.BhfId, dvcSrlNo, invRcptKind, tinBhfPath)
			} else {
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeTypeError,
					ResultMsg: ErrorTypeMsg,
				}, ErrTypeError
			}
		case SalesTypeP:
			if receiptType == ReceiptTypeS {
				invRcptKind = SerialInvPS
				rcptTpNo, err = t.sequenceManager.GetRcptTypNo(req.CustTin, req.BhfId, dvcSrlNo, invRcptKind, tinBhfPath)
			} else {
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeTypeError,
					ResultMsg: ErrorTypeMsg,
				}, ErrTypeError
			}
		case SalesTypeC:
			if receiptType == ReceiptTypeS || receiptType == ReceiptTypeR {
				invRcptKind = SerialInvCSR
				rcptTpNo, err = t.sequenceManager.GetRcptTypNo(req.CustTin, req.BhfId, dvcSrlNo, invRcptKind, tinBhfPath)
			} else {
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeTypeError,
					ResultMsg: ErrorTypeMsg,
				}, ErrTypeError
			}
		default:
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeTypeError,
				ResultMsg: ErrorTypeMsg,
			}, ErrTypeError
		}

		if err != nil {
			t.logger.Error().Err(err).Msg("Failed to get receipt type number")
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeSequenceError,
				ResultMsg: ErrorSequenceMsg,
			}, ErrSequenceError
		}

		// Check receipt type number validity
		if rcptTpNo <= 0 {
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeSequenceError,
				ResultMsg: ErrorSequenceMsg,
			}, ErrSequenceError
		}
		req.Receipt.CurRcptNo = rcptTpNo

		// Get receipt number
		rcptNo, err = t.sequenceManager.GetRcptNo(req.CustTin, req.BhfId, dvcSrlNo, tinBhfPath)
		if err != nil || rcptNo <= 0 {
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeSequenceError,
				ResultMsg: ErrorSequenceMsg,
			}, ErrSequenceError
		}
		req.Receipt.TotRcptNo = rcptNo

		// Format date
		vsdcRcptPbctDate = time.Now().Format("20060102150405")
		req.Receipt.RcptPbctDt = vsdcRcptPbctDate

		// Get report number
		rptNo, err := t.sequenceManager.GetRptNo(tinBhfPath)
		if err != nil {
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeSequenceError,
				ResultMsg: ErrorSequenceMsg,
			}, ErrSequenceError
		}
		req.Receipt.RptNo = rptNo

		// Skip signature generation for certain sales types
		if salesType == SalesTypeP || salesType == SalesTypeT {
			req.Receipt.IntrlData = ""
			req.Receipt.RcptSign = ""
		} else {
			t.logger.Info().Str("tinBhfPath", tinBhfPath).Msg("Generating signature")

			// Calculate values once
			taxAmtB := req.TaxAmtB
			rptNo := req.Receipt.RptNo
			invcNo := req.InvcNo
			taxblAmtB := req.TaxblAmtB
			taxblAmtA := req.TaxblAmtA
			taxAmtA := req.TaxAmtA

			// Get internal data
			intrlData, err := t.internalManager.GetInternalData(
				salesType, receiptType, taxAmtB, rptNo, invcNo, tinBhfPath)
			if err != nil {
				t.logger.Error().Err(err).Msg("Failed to get internal data")
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeGeneralError,
					ResultMsg: "Failed to generate internal data: " + err.Error(),
				}, err
			}
			req.Receipt.IntrlData = intrlData

			// Get signature data
			signData, err := t.internalManager.GetSignature(
				vsdcRcptPbctDate, req.Tin, req.CustTin,
				invcNo, taxblAmtB, taxAmtB, taxblAmtA, taxAmtA,
				salesType, receiptType, invcNo, invcNo, tinBhfPath)
			if err != nil {
				t.logger.Error().Err(err).Msg("Failed to get signature")
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeGeneralError,
					ResultMsg: "Failed to generate signature: " + err.Error(),
				}, err
			}
			req.Receipt.RcptSign = signData
		}
	}

	// Create response data
	resData := &entities.TrnsSalesSaveWrResData{}

	// Get SDC ID and MRC number with proper error handling
	sdcId, err := t.GetSdcId(ctx, tinBhfPath)
	if err != nil {
		t.logger.Warn().Err(err).Msg("Using empty SDC ID due to error")
		sdcId = ""
	}
	resData.SdcId = sdcId

	mrcNo, err := t.GetMrcNumber(ctx, tinBhfPath)
	if err != nil {
		t.logger.Warn().Err(err).Msg("Using empty MRC number due to error")
		mrcNo = ""
	}
	resData.MrcNo = mrcNo

	// Set receipt data
	resData.RcptNo = req.Receipt.CurRcptNo
	resData.TotRcptNo = req.Receipt.TotRcptNo
	resData.VsdcRcptPbctDate = req.Receipt.RcptPbctDt
	resData.IntrlData = req.Receipt.IntrlData
	resData.RcptSign = req.Receipt.RcptSign

	// Set sales status and type
	req.SalesSttsCd = "02"
	req.SalesTyCd = SalesTypeN

	// Save to database
	savedTransaction, err := t.etimsService.SaveReceiptDetailsToDB(req, resData, invRcptKind)
	if err != nil {
		t.logger.Error().Err(err).Msg("Failed to save transaction to database")
		return &entities.TrnsSalesSaveWrRes{
			ResultCd:  ResultCodeGeneralError,
			ResultMsg: "Failed to save transaction: " + err.Error(),
		}, err
	}

	if savedTransaction.ID == 0 {
		t.logger.Info().Msg("Failed to save ETIMS transaction to DB")
	} else {
		t.logger.Info().Int64("id", savedTransaction.ID).Msg("Saved ETIMS transaction to DB")
	}

	// Set success response
	res.ResultCd = ResultCodeSuccess
	res.ResultMsg = "Successful"
	res.Data = resData

	return res, nil
}

// DateStringFormatter formats a time to string in the expected format
func DateStringFormatter(timestamp int64) string {
	t := time.Unix(timestamp/1000, 0)
	return t.Format("20060102150405")
}
