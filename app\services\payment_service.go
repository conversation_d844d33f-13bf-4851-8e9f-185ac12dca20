package services

import (
	"context"
	"fmt"

	"struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/providers"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
)

type (
	PaymentService interface {
		DecodePhoneNumberHash(ctx context.Context, transactionsDB database.TransactionsDB, hashString string) (*entities.MPesaPhoneNumberHashDecoderResponse, *entities.PhoneNumber, error)
		ProcessMPesaDeposit(ctx context.Context, transactionsDB database.TransactionsDB, form *forms.MPesaDepositForm) error
	}

	AppPaymentService struct {
		mpesaDepositRepository      repos.MPesaDepositRepository
		mpesaPhoneNumberHashDecoder providers.MPesaPhoneNumberHashDecoder
		organizationRepository      repos.OrganizationRepository
		phoneNumberRepository       repos.PhoneNumberRepository
		smtpSender                  providers.SmtpSender
	}
)

func NewPaymentService(
	mpesaDepositRepository repos.MPesaDepositRepository,
	mpesaPhoneNumberHashDecoder providers.MPesaPhoneNumberHashDecoder,
	organizationRepository repos.OrganizationRepository,
	phoneNumberRepository repos.PhoneNumberRepository,
	smtpSender providers.SmtpSender,
) PaymentService {
	return &AppPaymentService{
		mpesaDepositRepository:      mpesaDepositRepository,
		mpesaPhoneNumberHashDecoder: mpesaPhoneNumberHashDecoder,
		organizationRepository:      organizationRepository,
		phoneNumberRepository:       phoneNumberRepository,
		smtpSender:                  smtpSender,
	}
}

func (s *AppPaymentService) DecodePhoneNumberHash(
	ctx context.Context,
	transactionsDB database.TransactionsDB,
	hashString string,
) (*entities.MPesaPhoneNumberHashDecoderResponse, *entities.PhoneNumber, error) {

	decodedResponse := &entities.MPesaPhoneNumberHashDecoderResponse{
		Hash:        hashString,
		PhoneNumber: "",
	}

	var err error

	// Check if phone number already decoded
	phoneNumber, err := s.phoneNumberRepository.FindByMpesaHash(ctx, transactionsDB, hashString)
	if err != nil && !utils.IsErrNoRows(err) {
		return decodedResponse, &entities.PhoneNumber{}, utils.NewError(
			err,
			"Unable to decode phone number hash, err=[%v]",
			err,
		)
	}

	if !phoneNumber.IsNew() {
		decodedResponse.PhoneNumber = phoneNumber.String()
		return decodedResponse, phoneNumber, nil
	}

	// If not decoded, fetch decoded number
	decodedResponse, err = s.mpesaPhoneNumberHashDecoder.DecodePhoneNumberHash(hashString)
	if err != nil {
		return decodedResponse, phoneNumber, utils.NewError(
			err,
			"Unable to decode phone number hash, err=[%v]",
			err,
		)
	}

	if decodedResponse == nil {
		return &entities.MPesaPhoneNumberHashDecoderResponse{
			Hash:        hashString,
			PhoneNumber: "",
		}, phoneNumber, nil
	}

	// Check if phone number exists in db
	searchPhoneNumber, err := utils.ParsePhoneNumber(decodedResponse.PhoneNumber)
	if err != nil {
		return decodedResponse, phoneNumber, utils.NewError(
			err,
			"unable to parse phoneNumber=[%v], err=[%v]",
			decodedResponse.PhoneNumber,
			err,
		)
	}

	fmt.Printf("decodedResponse.Value=[%v], searchPhoneNumber=[%v]\n", decodedResponse.PhoneNumber, searchPhoneNumber.String())

	foundPhoneNumber, err := s.phoneNumberRepository.SearchByPhoneNumber(ctx, transactionsDB, searchPhoneNumber)
	if err != nil && !utils.IsErrNoRows(err) {
		return decodedResponse, phoneNumber, utils.NewError(
			err,
			"Unable to search decoded phone number in db, err=[%v]",
			err,
		)
	}

	if !foundPhoneNumber.IsNew() {
		phoneNumber = foundPhoneNumber
	} else {
		phoneNumber = &searchPhoneNumber
	}

	// Save decoded number
	phoneNumber.MpesaHash = &decodedResponse.Hash
	err = s.phoneNumberRepository.Save(ctx, transactionsDB, phoneNumber)
	if err != nil {
		return decodedResponse, phoneNumber, utils.NewError(
			err,
			"Unable to save phone number hash, err=[%v]",
			err,
		)
	}

	return decodedResponse, phoneNumber, nil
}

func (s *AppPaymentService) ProcessMPesaDeposit(
	ctx context.Context,
	transactionsDB database.TransactionsDB,
	form *forms.MPesaDepositForm,
) error {

	fmt.Printf("processing mpesa topup transID=[%v]\n", form.TransID)

	if form.TransID == "" {
		fmt.Printf("transID is empty, skipping...\n")
		return nil
	}

	phoneNumberDecodeResponse, phoneNumber, err := s.DecodePhoneNumberHash(ctx, transactionsDB, form.MSISDN)
	if err != nil {
		fmt.Printf("Unable to decode phone number hash, err=[%v]\n", err.Error())
	}

	phoneNumberValue := phoneNumberDecodeResponse.PhoneNumber

	floatAmt := utils.ConvertStringToFloat64(form.TransAmount)
	formattedAmount := utils.RenderFloat("#,###.##", floatAmt)

	fmt.Printf("sending payment topup email, phoneNumber=[%v], amount=[%v]...\n", phoneNumberValue, formattedAmount)

	subject := "M-Pesa Deposit"
	to := "<EMAIL>"

	depositorName := fmt.Sprintf("%v %v %v", form.FirstName, form.MiddleName, form.LastName)

	items := map[string]string{
		"TransactionAmount": formattedAmount,
		"TransactionID":     form.TransID,
		"Account":           form.BillRefNumber,
		"Name":              depositorName,
		"PhoneNumber":       phoneNumberValue,
	}

	// Send Email to admin with payment details.
	go s.smtpSender.SendEmailWithTemplate("optimus", "templates/mpesa-topup.html", to, subject, items, nil)

	defaultOrganization, err := s.organizationRepository.GetDefaultOrganization(ctx, transactionsDB)
	if err != nil {
		fmt.Printf("unable to find default organization, err=[%v]\n", err.Error())
	}

	// Save to db and route accordingly depending on the payment account.
	mpesaDeposit := &entities.MPesaDeposit{
		BillRefNumber:     form.BillRefNumber,
		BusinessShortCode: form.BusinessShortCode,
		FirstName:         form.FirstName,
		InvoiceNumber:     form.InvoiceNumber,
		LastName:          form.LastName,
		MiddleName:        form.MiddleName,
		MSISDN:            form.MSISDN,
		OrgAccountBalance: form.OrgAccountBalance,
		OrganizationID:    defaultOrganization.ID,
		PhoneNumberID:     phoneNumber.ID,
		ThirdPartyTransID: form.ThirdPartyTransID,
		TransactionAmount: form.TransAmount,
		TransactionID:     form.TransID,
		TransactionTime:   form.TransTime,
		TransactionType:   form.TransactionType,
	}

	err = s.mpesaDepositRepository.Save(ctx, mpesaDeposit)
	if err != nil {
		fmt.Printf("failed to save mpesa deposit, err=[%v]\n", err.Error())
	}

	fmt.Printf("mpesa deposit saved successfully\n")

	// Check if form.BillRefNumber starts with HS
	if len(form.BillRefNumber) > 2 && form.BillRefNumber[0:2] == "HS" {
		fmt.Printf("routing payment to hash system\n")

		trxAmountFloat := utils.ConvertStringToFloat64(form.TransAmount)

		topupForm := &forms.PhoneNumberHashTopupForm{
			AccountNumber: form.BillRefNumber,
			Amount:        trxAmountFloat,
			PhoneNumber:   phoneNumberValue,
		}

		accTopUpResponse, err := s.mpesaPhoneNumberHashDecoder.TopUpHashAccount(topupForm)
		if err != nil {
			fmt.Printf("unable to topup hash account, err=[%v]\n", err.Error())
		}

		fmt.Printf("hash account topup response: [%+v]\n", accTopUpResponse)
	}

	return nil
}
