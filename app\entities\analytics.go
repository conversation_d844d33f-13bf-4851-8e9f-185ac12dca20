package entities

type (
	ChartSummary struct {
		CustomersCount        int `json:"customers_count"`
		Failed                int `json:"failed"`
		Month                 int `json:"month"`
		ProcessedSuccessfully int `json:"processed_successfully"`
		TotalInvoices         int `json:"total_invoices"`
		TotalSales            int `json:"total_sales"`
	}

	DashboardAnalytics struct {
		CategoriesCount         int            `json:"categories_count"`
		CustomersCount          int            `json:"customers_count"`
		ExcelFileUploadsCount   int            `json:"excel_file_uploads_count"`
		FailedInvoicesCount     int            `json:"failed_invoices_count"`
		ItemsCount              int            `json:"items_count"`
		ProcessingInvoicesCount int            `json:"processing_invoices_count"`
		SalesCount              int            `json:"sales_count"`
		SignedInvoicesCount     int            `json:"signed_invoices_count"`
		StoresCount             int            `json:"stores_count"`
		TotalInvoicesCount      int            `json:"total_invoices_count"`
		TotalCreditNotesCount   int            `json:"total_credit_notes_count"`
		ChartSummary            []ChartSummary `json:"chart_summary"`
	}
)
