package services

import (
	"context"
	"errors"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/utils"
	"time"
)

// Sales and receipt type constants
const (
	SalesTypeN              = "N"
	SalesTypeT              = "T"
	SalesTypeP              = "P"
	SalesTypeC              = "C"
	ReceiptTypeS            = "S"
	ReceiptTypeR            = "R"
	SerialInvNSR            = "serlInvNsr"
	SerialInvTSR            = "serlInvTsr"
	SerialInvPS             = "serlInvPs"
	SerialInvCSR            = "serlInvCsr"
	ResultCodeSuccess       = "000"
	ResultCodeTypeError     = "834"
	ResultCodeSequenceError = "836"
	ResultCodeGeneralError  = "899"
	ErrorTypeMsg            = "SalesType and ReceiptType must be NS-NR-TS-TR-CS-CR-PS check your inputs."
	ErrorSequenceMsg        = "Your Sequences have been altered, Connect to KRA API to get Sequences."
	ErrorDeviceDataMsg      = "Failed to retrieve device data, please check configuration."
)

// Custom errors
var (
	ErrMissingParams = errors.New("missing required parameters")
	ErrTypeError     = errors.New(ErrorTypeMsg)
	ErrSequenceError = errors.New(ErrorSequenceMsg)
	ErrDeviceData    = errors.New(ErrorDeviceDataMsg)
)

// DeviceDataError represents a device data retrieval error
type DeviceDataError struct {
	Msg string
	Err error
}

type EtimsVSCUCoreService interface {
	GetRcptNo(custTin, bhfId, dvcSrlNo, tinBhfPath string) (int64, error)
	GetRcptTypNo(tin, bhfId, dvcSrlNo, invRcptKind, tinBhfPath string) (int64, error)
	GetRptNo(tinBhfPath string) (int64, error)
	SaveTrnsSalesHandler(ctx context.Context, req *entities.TrnsSalesSaveWrReq) error
}

type AppEtimsVSCUCoreService struct {
}

func NewEtimsVSCUCoreService() EtimsVSCUCoreService {
	return &AppEtimsVSCUCoreService{}
}

func (t *AppEtimsVSCUCoreService) GetRcptNo(custTin, bhfId, dvcSrlNo, tinBhfPath string) (int64, error) {
	return utils.GetRcptNo(custTin, bhfId, dvcSrlNo, tinBhfPath)
}

func (t *AppEtimsVSCUCoreService) GetRcptTypNo(tin, bhfId, dvcSrlNo, invRcptKind, tinBhfPath string) (int64, error) {
	return utils.GetRcptTypNo(tin, bhfId, dvcSrlNo, invRcptKind, tinBhfPath)
}

func (t *AppEtimsVSCUCoreService) GetRptNo(tinBhfPath string) (int64, error) {
	return utils.GetRptNo(tinBhfPath)
}

func (t *AppEtimsVSCUCoreService) SaveTrnsSalesHandler(
	ctx context.Context,
	req *entities.TrnsSalesSaveWrReq,
) error {

	// Perform early validation
	if req.Tin == "" || req.BhfId == "" {
		utils.Log.Infof("TIN and bhfId are required")
		return errors.New("TIN and bhfId are required")
	}

	res, err := t.SaveTrnsSales(ctx, req, false)
	if err != nil {
		// Error is already handled inside SaveTrnsSales
		utils.Log.Infof("error saving transaction, err=[%v]", err)
		return err
	}

	utils.Log.Infof("transaction saved successfully")
	utils.PrintStructToConsole(res)

	return nil

}

// SaveTrnsSales processes and saves a transaction
func (t *AppEtimsVSCUCoreService) SaveTrnsSales(ctx context.Context, req *entities.TrnsSalesSaveWrReq, reSend bool) (*entities.TrnsSalesSaveWrRes, error) {
	res := &entities.TrnsSalesSaveWrRes{}
	tinBhfPath := req.Tin + "_" + req.BhfId
	var dvcSrlNo string
	var invRcptKind string
	var rcptNo int64
	var rcptTpNo int64
	var vsdcRcptPbctDate string

	utils.Log.Infof("Processing transaction")
	utils.Log.Infof("tinBhfPath=[%v]", tinBhfPath)

	if !reSend {
		// Get device serial number
		var err error
		dvcSrlNo, err = t.GetDeviceSerialNumber(ctx, tinBhfPath)
		if err != nil {
			utils.Log.Infof("Failed to get device serial number, err=[%v]", err)
			return res, err
		}

		// Process based on sales type and receipt type
		salesType := req.SalesTyCd
		receiptType := req.RcptTyCd

		switch salesType {
		case SalesTypeN:
			if receiptType == ReceiptTypeS || receiptType == ReceiptTypeR {
				invRcptKind = SerialInvNSR
				rcptTpNo, err = utils.GetRcptTypNo(req.Tin, req.BhfId, dvcSrlNo, invRcptKind, tinBhfPath)
			} else {
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeTypeError,
					ResultMsg: ErrorTypeMsg,
				}, ErrTypeError
			}
		case SalesTypeT:
			if receiptType == ReceiptTypeS || receiptType == ReceiptTypeR {
				invRcptKind = SerialInvTSR
				rcptTpNo, err = utils.GetRcptTypNo(req.CustTin, req.BhfId, dvcSrlNo, invRcptKind, tinBhfPath)
			} else {
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeTypeError,
					ResultMsg: ErrorTypeMsg,
				}, ErrTypeError
			}
		case SalesTypeP:
			if receiptType == ReceiptTypeS {
				invRcptKind = SerialInvPS
				rcptTpNo, err = utils.GetRcptTypNo(req.CustTin, req.BhfId, dvcSrlNo, invRcptKind, tinBhfPath)
			} else {
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeTypeError,
					ResultMsg: ErrorTypeMsg,
				}, ErrTypeError
			}
		case SalesTypeC:
			if receiptType == ReceiptTypeS || receiptType == ReceiptTypeR {
				invRcptKind = SerialInvCSR
				rcptTpNo, err = utils.GetRcptTypNo(req.CustTin, req.BhfId, dvcSrlNo, invRcptKind, tinBhfPath)
			} else {
				return &entities.TrnsSalesSaveWrRes{
					ResultCd:  ResultCodeTypeError,
					ResultMsg: ErrorTypeMsg,
				}, ErrTypeError
			}
		default:
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeTypeError,
				ResultMsg: ErrorTypeMsg,
			}, ErrTypeError
		}

		if err != nil {
			utils.Log.Infof("Failed to get receipt type number, err=[%v]", err)
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeSequenceError,
				ResultMsg: ErrorSequenceMsg,
			}, ErrSequenceError
		}

		// Check receipt type number validity
		if rcptTpNo <= 0 {
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeSequenceError,
				ResultMsg: ErrorSequenceMsg,
			}, ErrSequenceError
		}
		req.Receipt.CurRcptNo = rcptTpNo

		// Get receipt number
		rcptNo, err = utils.GetRcptNo(req.CustTin, req.BhfId, dvcSrlNo, tinBhfPath)
		if err != nil || rcptNo <= 0 {
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeSequenceError,
				ResultMsg: ErrorSequenceMsg,
			}, ErrSequenceError
		}
		req.Receipt.TotRcptNo = rcptNo

		// Format date
		vsdcRcptPbctDate = time.Now().Format("20060102150405")
		req.Receipt.RcptPbctDt = vsdcRcptPbctDate

		// Get report number
		rptNo, err := utils.GetRptNo(tinBhfPath)
		if err != nil {
			return &entities.TrnsSalesSaveWrRes{
				ResultCd:  ResultCodeSequenceError,
				ResultMsg: ErrorSequenceMsg,
			}, ErrSequenceError
		}
		req.Receipt.RptNo = rptNo

		// Skip signature generation for certain sales types
		if salesType == SalesTypeP || salesType == SalesTypeT {
			req.Receipt.IntrlData = ""
			req.Receipt.RcptSign = ""
		} else {
			utils.Log.Infof("Generating signature")

			// Calculate values once
			// taxAmtB := req.TaxAmtB
			// rptNo := req.Receipt.RptNo
			// invcNo := req.InvcNo
			// taxblAmtB := req.TaxblAmtB
			// taxblAmtA := req.TaxblAmtA
			// taxAmtA := req.TaxAmtA

			// Get internal data
			intrlData := ""
			// intrlData, err := t.internalManager.GetInternalData(
			// 	salesType, receiptType, taxAmtB, rptNo, invcNo, tinBhfPath)
			// if err != nil {
			// 	utils.Log.Infof("Failed to get internal data")
			// 	return &entities.TrnsSalesSaveWrRes{
			// 		ResultCd:  ResultCodeGeneralError,
			// 		ResultMsg: "Failed to generate internal data: " + err.Error(),
			// 	}, err
			// }
			req.Receipt.IntrlData = intrlData

			// Get signature data
			signData := ""
			// signData, err := t.internalManager.GetSignature(
			// 	vsdcRcptPbctDate, req.Tin, req.CustTin,
			// 	invcNo, taxblAmtB, taxAmtB, taxblAmtA, taxAmtA,
			// 	salesType, receiptType, invcNo, invcNo, tinBhfPath)
			// if err != nil {
			// 	utils.Log.Infof("Failed to get signature")
			// 	return &entities.TrnsSalesSaveWrRes{
			// 		ResultCd:  ResultCodeGeneralError,
			// 		ResultMsg: "Failed to generate signature: " + err.Error(),
			// 	}, err
			// }
			req.Receipt.RcptSign = signData
		}
	}

	// Create response data
	resData := &entities.TrnsSalesSaveWrResData{}

	// Get SDC ID and MRC number with proper error handling
	sdcId := ""
	// sdcId, err := t.GetSdcId(ctx, tinBhfPath)
	// if err != nil {
	// 	utils.Log.Infof("Using empty SDC ID due to error")
	// 	sdcId = ""
	// }
	resData.SdcId = sdcId

	mrcNo := ""
	// mrcNo, err := t.GetMrcNumber(ctx, tinBhfPath)
	// if err != nil {
	// 	utils.Log.Infof("Using empty MRC number due to error")
	// 	mrcNo = ""
	// }
	resData.MrcNo = mrcNo

	// Set receipt data
	resData.RcptNo = req.Receipt.CurRcptNo
	resData.TotRcptNo = req.Receipt.TotRcptNo
	resData.VsdcRcptPbctDate = req.Receipt.RcptPbctDt
	resData.IntrlData = req.Receipt.IntrlData
	resData.RcptSign = req.Receipt.RcptSign

	// Set sales status and type
	req.SalesSttsCd = "02"
	req.SalesTyCd = SalesTypeN

	// Save to database
	// savedTransaction, err := t.etimsService.SaveReceiptDetailsToDB(req, resData, invRcptKind)
	// if err != nil {
	// 	utils.Log.Infof("Failed to save transaction to database")
	// 	return &entities.TrnsSalesSaveWrRes{
	// 		ResultCd:  ResultCodeGeneralError,
	// 		ResultMsg: "Failed to save transaction: " + err.Error(),
	// 	}, err
	// }

	// if savedTransaction.ID == 0 {
	// 	utils.Log.Infof("Failed to save ETIMS transaction to DB")
	// } else {
	// 	utils.Log.Infof("Saved ETIMS transaction to DB")
	// }

	// Set success response
	res.ResultCd = ResultCodeSuccess
	res.ResultMsg = "Successful"
	res.Data = resData

	return res, nil
}

// DateStringFormatter formats a time to string in the expected format
func DateStringFormatter(timestamp int64) string {
	t := time.Unix(timestamp/1000, 0)
	return t.Format("20060102150405")
}

// GetDeviceSerialNumber gets the device serial number
func (t *AppEtimsVSCUCoreService) GetDeviceSerialNumber(ctx context.Context, tinBhfPath string) (string, error) {
	// cacheKey := "dvcSrlNo_" + tinBhfPath
	// return t.getDeviceData(ctx, cacheKey, t.deviceManager.GetDeviceSerialNumber, tinBhfPath,
	// 	"Failed to retrieve device serial number")
	return "", nil
}

// getDeviceData safely retrieves cached device data or fetches it if not in cache
func (t *AppEtimsVSCUCoreService) getDeviceData(ctx context.Context, cacheKey string, fetchFunc func(string) (string, error), tinBhfPath string, errorMsg string) (string, error) {
	// Check if in cache
	// if val, ok := t.deviceCache.Load(cacheKey); ok {
	// 	if strVal, ok := val.(string); ok {
	// 		return strVal, nil
	// 	}
	// }

	// Not in cache, fetch the data
	value, err := fetchFunc(tinBhfPath)
	if err != nil {
		utils.Log.Infof("tinBhfPath", tinBhfPath)
		return "", err
	}

	// Cache the result
	if value != "" {
		// t.deviceCache.Store(cacheKey, value)
	}

	return value, nil
}
