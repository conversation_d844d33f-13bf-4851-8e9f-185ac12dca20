package settings

import (
	"struts-pos-backend/app/database"
	"struts-pos-backend/app/middleware"
	"struts-pos-backend/app/web/auth"

	"struts-pos-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	settingService services.SettingService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/settings", saveSetting(transactionsDB, settingService))
		protectedAPI.GET("/settings", filterSettings(transactionsDB, settingService))
		protectedAPI.GET("/settings/:id", getSetting(transactionsDB, settingService))
		protectedAPI.PUT("/settings/:id", saveSetting(transactionsDB, settingService))
	}

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/settings/:id/pdf", saveSetting(transactionsDB, settingService))
	}
}
