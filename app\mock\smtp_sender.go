package mock

import (
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/providers"
)

type (
	MockSmtpSender struct {
	}
)

func NewSmtpSender() providers.SmtpSender {
	return &MockSmtpSender{}
}

func (s *MockSmtpSender) SendSmtp(to, subject, message string, emailConfig *entities.EmailConfig) error {
	return nil
}

func (s *MockSmtpSender) SendEmailWithTemplate(application, htmlTemplate, to, subject string, items interface{}, attachments []string) error {
	return nil
}
