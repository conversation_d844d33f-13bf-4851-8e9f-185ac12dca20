package services

import (
	"context"
	"fmt"
	"strings"
	"struts-pos-backend/app/apperr"
	txns_db "struts-pos-backend/app/database"
	"struts-pos-backend/app/entities"
	"struts-pos-backend/app/forms"
	"struts-pos-backend/app/providers"
	"struts-pos-backend/app/repos"
	"struts-pos-backend/app/utils"
	"struts-pos-backend/app/web/ctxhelper"
)

type EtimsItemService interface {
	CreateEtimsItem(context.Context, txns_db.TransactionsSQLOperations, *forms.ETIMSInvoiceItemForm) (*entities.EtimsItem, error)
	FilterEtimsItems(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) (*entities.EtimsItemList, error)
	FindByID(context.Context, txns_db.TransactionsSQLOperations, int64) (*entities.EtimsItem, error)
}

type AppEtimsItemService struct {
	etimsVSCU           providers.EtimsVSCU
	etimsItemRepository repos.EtimsItemRepository
	storeRepository     repos.StoreRepository
}

func NewEtimsItemService(
	etimsVSCU providers.EtimsVSCU,
	etimsItemRepository repos.EtimsItemRepository,
	storeRepository repos.StoreRepository,
) EtimsItemService {
	return &AppEtimsItemService{
		etimsVSCU:           etimsVSCU,
		etimsItemRepository: etimsItemRepository,
		storeRepository:     storeRepository,
	}
}

func (s *AppEtimsItemService) CreateEtimsItem(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.ETIMSInvoiceItemForm,
) (*entities.EtimsItem, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	store, err := s.storeRepository.FindByID(ctx, tokenInfo.StoreID)
	if err != nil {
		return &entities.EtimsItem{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find configured store for API Key=[%v]",
			tokenInfo.APIKeyID,
		)
	}

	fmt.Printf("store name=[%v], store pin=[%v], branch=[%v]\n", store.Name, store.PIN, store.EtimsBranch)

	addInfo := strings.TrimSpace(form.ItemName)

	if len(addInfo) > 7 {
		addInfo = addInfo[0:7]
	}

	var etimsItemCode string
	formEtimsItemCode := strings.TrimSpace(form.EtimsItemCode)
	if formEtimsItemCode != "" && len(formEtimsItemCode) >= 8 {
		etimsItemCode = formEtimsItemCode
	} else {
		etimsItemCode = utils.GenerateKRAItemCode(form.ItemName, 20)
	}

	etimsItemForm := &forms.CreateEtimsItemForm{
		Tin:         store.PIN,
		BhfID:       store.EtimsBranch,
		ItemCd:      etimsItemCode,
		ItemClsCd:   utils.GenerateKRAItemCode(form.ItemName, 5),
		ItemNm:      form.ItemName,
		ItemStdNm:   form.ItemName,
		AddInfo:     addInfo,
		ItemTyCd:    entities.EtimsProductTypeService.String(),
		DftPrc:      form.RetailPrice,
		GrpPrcL1:    form.RetailPrice,
		GrpPrcL2:    form.RetailPrice,
		GrpPrcL3:    form.RetailPrice,
		GrpPrcL4:    form.RetailPrice,
		GrpPrcL5:    form.RetailPrice,
		SftyQty:     form.ItemQty,
		TaxTyCd:     entities.EtimsTaxType(form.TaxTypeCode).String(), // A, B, C, D, E
		IsrcAplcbYn: "N",
		ModrID:      "Admin",
		ModrNm:      "Admin",
		OrgnNatCd:   "KE",
		PkgUnitCd:   "BG",
		QtyUnitCd:   "U",
		RegrID:      "Admin",
		RegrNm:      "Admin",
		UseYn:       "Y",
	}

	etimsItem := &entities.EtimsItem{
		Tin:         store.PIN,
		BhfID:       store.EtimsBranch,
		ItemCd:      etimsItemForm.ItemCd,
		ItemClsCd:   etimsItemForm.ItemClsCd,
		ItemTyCd:    etimsItemForm.ItemTyCd,
		ItemNm:      etimsItemForm.ItemNm,
		ItemStdNm:   etimsItemForm.ItemStdNm,
		OrgnNatCd:   etimsItemForm.OrgnNatCd,
		PkgUnitCd:   etimsItemForm.PkgUnitCd,
		QtyUnitCd:   etimsItemForm.QtyUnitCd,
		TaxTyCd:     etimsItemForm.TaxTyCd,
		BtchNo:      etimsItemForm.BtchNo,
		Bcd:         etimsItemForm.Bcd,
		DftPrc:      etimsItemForm.DftPrc,
		GrpPrcL1:    etimsItemForm.GrpPrcL1,
		GrpPrcL2:    etimsItemForm.GrpPrcL2,
		GrpPrcL3:    etimsItemForm.GrpPrcL3,
		GrpPrcL4:    etimsItemForm.GrpPrcL4,
		GrpPrcL5:    etimsItemForm.GrpPrcL5,
		AddInfo:     etimsItemForm.AddInfo,
		SftyQty:     etimsItemForm.SftyQty,
		IsrcAplcbYn: etimsItemForm.IsrcAplcbYn,
		UseYn:       etimsItemForm.UseYn,
		RegrNm:      etimsItemForm.RegrNm,
		RegrID:      etimsItemForm.RegrID,
		ModrNm:      etimsItemForm.ModrNm,
		ModrID:      etimsItemForm.ModrID,
	}

	// Save locally
	err = s.etimsItemRepository.Save(ctx, etimsItem)
	if err != nil {
		fmt.Printf("failed to save etims item, err=[%v] \n", err)
		return etimsItem, err
	}

	utils.PrintStructToConsole(etimsItemForm)

	// Post to ETIMS
	response, err := s.etimsVSCU.SaveItem(etimsItemForm)
	if err != nil {
		fmt.Printf("failed to save item on etims, err=[%v] \n", err)
		return etimsItem, err
	}

	fmt.Printf("response.ResultCd=[%v]\n", response.ResultCd)

	return etimsItem, nil
}

func (s *AppEtimsItemService) FilterEtimsItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.EtimsItemList, error) {

	list := &entities.EtimsItemList{}

	count, err := s.etimsItemRepository.Count(ctx, filter)
	if err != nil {
		return list, err
	}

	notices, err := s.etimsItemRepository.FilterEtimsItems(ctx, operations, filter)
	if err != nil {
		return list, err
	}

	list.EtimsItems = notices

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	list.Pagination = pagination

	return list, nil
}

func (s *AppEtimsItemService) FindByID(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	id int64,
) (*entities.EtimsItem, error) {

	return s.etimsItemRepository.FindByID(ctx, id)
}
