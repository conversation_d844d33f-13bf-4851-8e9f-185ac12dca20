package utils

import (
	"errors"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// SequenceManager handles the management of sequence numbers for receipts, invoices, etc.
type SequenceManager struct {
	checkConn CheckApiConnection
}

// CheckApiConnection interface to check if the API is connected
type CheckApiConnection interface {
	IsConnected() bool
}

// DataResendManager interface for resending data
type DataResendManager interface {
	LoadReSendFile(fileType string, tinBhfPath string) error
}

// InitializeVsdcExcute interface for VSDC initialization
type InitializeVsdcExcute interface {
	SelectTotReceiptNo(req *InitInfoReq) error
}

// InitInfoReq contains the request information for initialization
type InitInfoReq struct {
	Tin      string
	BhfId    string
	DvcSrlNo string
}

// TrnsSalesSaveWrRes contains the response information for sales transactions
type TrnsSalesSaveWrRes struct {
	ResultCd  string
	ResultMsg string
}

// NewSequenceManager creates a new SequenceManager instance
func NewSequenceManager(checkConn CheckApiConnection) *SequenceManager {
	return &SequenceManager{
		checkConn: checkConn,
	}
}

// GetEbmSquencePath gets the path for the EBM sequence files
func GetEbmSquencePath(tinBhfPath string) string {
	// This is a placeholder for the Java SkmmUtil.getEbmSquencePath function
	// In a real implementation, this would need to be properly implemented
	return filepath.Join(tinBhfPath, "ebm", "sequence")
}

// Encrypt encrypts a plain text string
func Encrypt(plainText string) (string, error) {
	// This is a placeholder for the Java AesCoder.encrypt function
	// In a real implementation, this would need to be properly implemented with AES encryption
	return plainText + "_encrypted", nil
}

// Decrypt decrypts an encrypted string
func Decrypt(encryptedText string) (string, error) {
	// This is a placeholder for the Java AesCoder.decrypt function
	// In a real implementation, this would need to be properly implemented with AES decryption
	if strings.HasSuffix(encryptedText, "_encrypted") {
		return strings.TrimSuffix(encryptedText, "_encrypted"), nil
	}
	return "", errors.New("failed to decrypt")
}

// GetDate returns the current date in the specified format
func GetDate(format string) string {
	// This is a placeholder for the Java VsdcUtil.getDate function
	// In a real implementation, this would need to be properly implemented
	now := time.Now()
	if format == "yyyyMMdd" {
		return now.Format("20060102")
	}
	return now.Format(format)
}

// CalDateBetweenAandB calculates the number of days between two dates
func CalDateBetweenAandB(dateA, dateB string) (int64, error) {
	// This is a placeholder for the Java VsdcUtil.calDateBetweenAandB function
	// In a real implementation, this would need to be properly implemented
	t1, err := time.Parse("20060102", dateA)
	if err != nil {
		return 0, err
	}
	t2, err := time.Parse("20060102", dateB)
	if err != nil {
		return 0, err
	}
	return int64(t1.Sub(t2).Hours() / 24), nil
}

// SetRcptNo sets the receipt number
func SetRcptNo(rcptNo int64, tinBhfPath string) error {
	filePath := GetEbmSquencePath(tinBhfPath)
	plainReceipt := strconv.FormatInt(rcptNo, 10)
	encryRcpt, err := Encrypt(plainReceipt)
	if err != nil {
		return err
	}

	return os.WriteFile(filepath.Join(filePath, "rcptNo"), []byte(encryRcpt), 0644)
}

// SetInvcNo sets the invoice number
func SetInvcNo(invcNo int64, tinBhfPath string) error {
	filePath := GetEbmSquencePath(tinBhfPath)
	plainReceipt := strconv.FormatInt(invcNo, 10)
	encryRcpt, err := Encrypt(plainReceipt)
	if err != nil {
		return err
	}

	return os.WriteFile(filepath.Join(filePath, "invcNo"), []byte(encryRcpt), 0644)
}

// SetInvcNoNsr sets the NSR invoice number
func SetInvcNoNsr(invcNo int64, tinBhfPath string) error {
	filePath := GetEbmSquencePath(tinBhfPath)
	plainReceipt := strconv.FormatInt(invcNo, 10)
	encryRcpt, err := Encrypt(plainReceipt)
	if err != nil {
		return err
	}

	return os.WriteFile(filepath.Join(filePath, "serlInvNsr"), []byte(encryRcpt), 0644)
}

// SetInvcNoTsr sets the TSR invoice number
func SetInvcNoTsr(invcNo int64, tinBhfPath string) error {
	filePath := GetEbmSquencePath(tinBhfPath)
	plainReceipt := strconv.FormatInt(invcNo, 10)
	encryRcpt, err := Encrypt(plainReceipt)
	if err != nil {
		return err
	}

	return os.WriteFile(filepath.Join(filePath, "serlInvTsr"), []byte(encryRcpt), 0644)
}

// SetInvcNoPs sets the PS invoice number
func SetInvcNoPs(invcNo int64, tinBhfPath string) error {
	filePath := GetEbmSquencePath(tinBhfPath)
	plainReceipt := strconv.FormatInt(invcNo, 10)
	encryRcpt, err := Encrypt(plainReceipt)
	if err != nil {
		return err
	}

	return os.WriteFile(filepath.Join(filePath, "serlInvPs"), []byte(encryRcpt), 0644)
}

// SetInvcNoCsr sets the CSR invoice number
func SetInvcNoCsr(invcNo int64, tinBhfPath string) error {
	filePath := GetEbmSquencePath(tinBhfPath)
	plainReceipt := strconv.FormatInt(invcNo, 10)
	encryRcpt, err := Encrypt(plainReceipt)
	if err != nil {
		return err
	}

	return os.WriteFile(filepath.Join(filePath, "serlInvCsr"), []byte(encryRcpt), 0644)
}

// GetRcptNo gets the receipt number
func GetRcptNo(tin, bhf, dvcSrlNo, tinBhfPath string) (int64, error) {
	// These would be real implementations in production code
	resendMng := &MockDataResendManager{}
	initVsdc := &MockInitializeVsdcExcute{}
	checkConn := &MockCheckApiConnection{}

	var rcptNo int64
	filePath := GetEbmSquencePath(tinBhfPath)

	rcptNoBytes, err := os.ReadFile(filepath.Join(filePath, "rcptNo"))
	if err != nil {
		return 0, err
	}

	rcptNoTemp := string(rcptNoBytes)
	decryRcpt, err := Decrypt(rcptNoTemp)

	if err != nil {
		req := &InitInfoReq{
			Tin:      tin,
			BhfId:    bhf,
			DvcSrlNo: dvcSrlNo,
		}

		if checkConn.IsConnected() {
			err := resendMng.LoadReSendFile("trnsSales", tinBhfPath)
			if err != nil {
				return 0, err
			}

			err = initVsdc.SelectTotReceiptNo(req)
			if err != nil {
				return 0, err
			}

			return GetRcptNo(tin, bhf, dvcSrlNo, tinBhfPath)
		}

		return 0, err
	}

	if decryRcpt != "" {
		parsedRcptNo, err := strconv.ParseInt(decryRcpt, 10, 64)
		if err != nil {
			return 0, err
		}

		rcptNo = parsedRcptNo + 1
	}

	return rcptNo, nil
}

// GetRcptTypNo gets the receipt type number
func GetRcptTypNo(tin, bhf, dvcSrlNo, invRcptKind, tinBhfPath string) (int64, error) {
	// These would be real implementations in production code
	resendMng := &MockDataResendManager{}
	initVsdc := &MockInitializeVsdcExcute{}
	checkConn := &MockCheckApiConnection{}

	var rcptTpNo int64
	filePath := GetEbmSquencePath(tinBhfPath)

	rcptNoBytes, err := os.ReadFile(filepath.Join(filePath, invRcptKind))
	if err != nil {
		return 0, err
	}

	rcptNoTemp := string(rcptNoBytes)
	decryRcpt, err := Decrypt(rcptNoTemp)

	if err != nil {
		req := &InitInfoReq{
			Tin:      tin,
			BhfId:    bhf,
			DvcSrlNo: dvcSrlNo,
		}

		if checkConn.IsConnected() {
			err := resendMng.LoadReSendFile("trnsSales", tinBhfPath)
			if err != nil {
				return 0, err
			}

			err = initVsdc.SelectTotReceiptNo(req)
			if err != nil {
				return 0, err
			}

			return GetRcptTypNo(tin, bhf, dvcSrlNo, invRcptKind, tinBhfPath)
		}

		return 0, err
	}

	if decryRcpt != "" {
		parsedRcptTpNo, err := strconv.ParseInt(decryRcpt, 10, 64)
		if err != nil {
			return 0, err
		}

		rcptTpNo = parsedRcptTpNo + 1
	}

	return rcptTpNo, nil
}

// GetInvNo gets the invoice number
func GetInvNo(tin, bhf, dvcSrlNo, tinBhfPath string) (int64, error) {
	// These would be real implementations in production code
	resendMng := &MockDataResendManager{}
	initVsdc := &MockInitializeVsdcExcute{}
	checkConn := &MockCheckApiConnection{}

	var invNo int64
	filePath := GetEbmSquencePath(tinBhfPath)

	invNoBytes, err := os.ReadFile(filepath.Join(filePath, "invcNo"))
	if err != nil {
		return 0, err
	}

	invNoTemp := string(invNoBytes)
	decryInv, err := Decrypt(invNoTemp)

	if err != nil {
		req := &InitInfoReq{
			Tin:      tin,
			BhfId:    bhf,
			DvcSrlNo: dvcSrlNo,
		}

		if checkConn.IsConnected() {
			err := resendMng.LoadReSendFile("trnsSales", tinBhfPath)
			if err != nil {
				return 0, err
			}

			err = initVsdc.SelectTotReceiptNo(req)
			if err != nil {
				return 0, err
			}

			return GetInvNo(tin, bhf, dvcSrlNo, tinBhfPath)
		}

		return 0, err
	}

	if decryInv != "" {
		parsedInvNo, err := strconv.ParseInt(decryInv, 10, 64)
		if err != nil {
			return 0, err
		}

		invNo = parsedInvNo + 1
	}

	return invNo, nil
}

// SetBhfOpenDt sets the branch office open date
func SetBhfOpenDt(bhfOpenDt, tinBhfPath string) error {
	filePath := GetEbmSquencePath(tinBhfPath)
	encryBhfDt, err := Encrypt(bhfOpenDt)
	if err != nil {
		return err
	}

	return os.WriteFile(filepath.Join(filePath, "bhfOpenDt"), []byte(encryBhfDt), 0644)
}

// GetRptNo gets the report number
func GetRptNo(tinBhfPath string) (int64, error) {
	var rptNo int64
	filePath := GetEbmSquencePath(tinBhfPath)

	rptNoBytes, err := os.ReadFile(filepath.Join(filePath, "bhfOpenDt"))
	if err != nil {
		return 0, err
	}

	encyptRptNo := string(rptNoBytes)
	bhfOpenDt, err := Decrypt(encyptRptNo)

	if err != nil {
		// Return an error with a specific message
		return 0, errors.New("your Sequences have been altered, Connect to KRA API to get Sequences")
	}

	if bhfOpenDt != "" {
		curDt := GetDate("yyyyMMdd")
		days, err := CalDateBetweenAandB(curDt, bhfOpenDt)
		if err != nil {
			return 0, err
		}

		rptNo = days + 1
	}

	return rptNo, nil
}

// Mock implementations for testing and demonstration purposes
// These would be replaced with real implementations in production code
type MockCheckApiConnection struct{}

func (m *MockCheckApiConnection) IsConnected() bool {
	return false
}

type MockDataResendManager struct{}

func (m *MockDataResendManager) LoadReSendFile(fileType string, tinBhfPath string) error {
	return nil
}

type MockInitializeVsdcExcute struct{}

func (m *MockInitializeVsdcExcute) SelectTotReceiptNo(req *InitInfoReq) error {
	return nil
}
