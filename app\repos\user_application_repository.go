package repos

import (
	"context"
	"database/sql"
	"struts-pos-backend/app/entities"

	db "struts-pos-backend/app/database"
)

const (
	createUserApplicationSQL = `INSERT INTO user_applications (application_id, user_id, created_at, updated_at) VALUES 
		($1, $2, $3, $4) RETURNING id`

	selectUserApplicationSQL = "SELECT id, application_id, user_id, created_at, updated_at FROM user_applications"

	selectUserApplicationByIDSQL = selectUserApplicationSQL + " WHERE id = $1"

	countUserApplicationsByApplicationTypeSQL = `SELECT COUNT(*) FROM user_applications ua, applications a
		WHERE ua.application_id = a.id AND a.application_type = $1`

	selectUserApplicationByUserAndApplicationTypeIDSQL = selectUserApplicationSQL + " WHERE user_id = $1 and application_id = $2"

	updateUserApplicationEmailSQL = "UPDATE user_applications SET (updated_at) = ($1) WHERE id = $2"
)

type (
	UserApplicationRepository interface {
		CountForApplicationType(context.Context, db.TransactionsSQLOperations, entities.ApplicationType) (int, error)
		FindByID(ctx context.Context, operations db.TransactionsSQLOperations, userApplicationID int64) (*entities.UserApplication, error)
		FindByUserAndApplicationTypeID(ctx context.Context, operations db.TransactionsSQLOperations, userID int64, applicationID int64) (*entities.UserApplication, error)
		Save(ctx context.Context, operations db.TransactionsSQLOperations, userApplication *entities.UserApplication) error
		UpdateUserApplicationEmail(ctx context.Context, operations db.TransactionsSQLOperations, userApplication *entities.UserApplication) error
	}

	AppUserApplicationRepository struct {
	}
)

func NewUserApplicationRepository() *AppUserApplicationRepository {
	return &AppUserApplicationRepository{}
}

func (r *AppUserApplicationRepository) CountForApplicationType(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	applicationType entities.ApplicationType,
) (int, error) {
	var count int
	err := operations.QueryRowContext(ctx, countUserApplicationsByApplicationTypeSQL, applicationType).Scan(&count)

	return count, err
}

func (r *AppUserApplicationRepository) FindByID(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	userApplicationID int64,
) (*entities.UserApplication, error) {

	row := operations.QueryRowContext(ctx, selectUserApplicationByIDSQL, userApplicationID)
	return r.scanRowIntoUserApplication(row)
}

func (r *AppUserApplicationRepository) FindByUserAndApplicationTypeID(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	userID int64,
	applicationID int64,
) (*entities.UserApplication, error) {

	row := operations.QueryRowContext(ctx, selectUserApplicationByUserAndApplicationTypeIDSQL, userID, applicationID)
	return r.scanRowIntoUserApplication(row)
}

// Save creates or updates the user record
func (r *AppUserApplicationRepository) Save(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	userApplication *entities.UserApplication,
) error {

	if !userApplication.IsNew() {
		return nil
	}

	userApplication.Timestamps.Touch()

	res := operations.QueryRowContext(
		ctx,
		createUserApplicationSQL,
		userApplication.ApplicationID,
		userApplication.UserID,
		userApplication.Timestamps.CreatedAt,
		userApplication.Timestamps.UpdatedAt,
	)
	err := res.Scan(&userApplication.ID)
	return err
}

func (r *AppUserApplicationRepository) UpdateUserApplicationEmail(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	userApplication *entities.UserApplication,
) error {

	userApplication.Timestamps.Touch()

	_, err := operations.ExecContext(
		ctx,
		updateUserApplicationEmailSQL,
		userApplication.Timestamps.UpdatedAt,
		userApplication.ID,
	)

	return err
}

func (r *AppUserApplicationRepository) scanRowIntoUserApplication(
	row *sql.Row,
) (*entities.UserApplication, error) {

	var userApplication entities.UserApplication

	err := row.Scan(
		&userApplication.ID,
		&userApplication.ApplicationID,
		&userApplication.UserID,
		&userApplication.Timestamps.CreatedAt,
		&userApplication.Timestamps.UpdatedAt,
	)

	if err != nil {
		return &userApplication, err
	}

	return &userApplication, nil
}
