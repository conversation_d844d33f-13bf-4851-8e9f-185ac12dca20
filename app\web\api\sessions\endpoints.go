package sessions

import (
	"struts-pos-backend/app/database"

	"struts-pos-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) {

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.POST("/sessions", loginUser(transactionsDB, userService))
		unauthenticatedAPI.GET("/health", healthCheck)
	}

}
