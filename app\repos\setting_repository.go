package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"struts-pos-backend/app/entities"
	"strings"
	"time"

	txns_db "struts-pos-backend/app/database"
)

const (
	countSettingsSQL = `SELECT COUNT(*) AS count FROM settings WHERE 1=1`

	insertSettingSQL = ` INSERT INTO settings (name, value, created_by, updated_by, created_at, updated_at) VALUES`

	createSettingSQL = insertSettingSQL + ` ($1, $2, $3, $4, $5, $6) 
		ON CONFLICT(name) DO UPDATE SET value = EXCLUDED.value, updated_at=Now() RETURNING id`

	selectSettingByNameSQL = selectSettingSQL + ` AND name=$1`

	selectSettingByIDSQL = selectSettingSQL + ` AND id=$1`

	selectSettingSQL = `SELECT id, name, value, created_by, updated_by, created_at, updated_at FROM settings WHERE 1=1`

	updateSettingSQL = `UPDATE settings SET name=$1, value=$2, updated_by=$3, updated_at=$4 WHERE id=$5`

	updateMultipleSettingsSQL = `UPDATE settings AS s SET (value, updated_by, updated_at) = (u.value, u.updated_by, u.updated_at)`
)

type (
	SettingRepository interface {
		CountSettings(ctx context.Context, filter *entities.PaginationFilter) (int, error)
		FilterSettings(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) ([]*entities.Setting, error)
		GetSettingByName(ctx context.Context, email string) (*entities.Setting, error)
		GetSettingByID(ctx context.Context, settingID int64) (*entities.Setting, error)
		GetZReportTotals(ctx context.Context, operations txns_db.TransactionsSQLOperations, isZeportedCheck bool) (entities.ZReportTotals, error)
		Save(ctx context.Context, operations txns_db.TransactionsSQLOperations, setting *entities.Setting) error
		SaveMultiple(ctx context.Context, operations txns_db.TransactionsSQLOperations, settings []*entities.Setting) error
		UpdateMultipleSettings(ctx context.Context, operations txns_db.TransactionsSQLOperations, settings []*entities.Setting) error
	}

	AppSettingRepository struct {
		db *sql.DB
	}
)

func NewSettingRepository(db *sql.DB) SettingRepository {
	return &AppSettingRepository{db: db}
}

func (r *AppSettingRepository) CountSettings(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countSettingsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count, nil
}

func (r *AppSettingRepository) FilterSettings(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.Setting, error) {

	settings := make([]*entities.Setting, 0)

	args := make([]interface{}, 0)
	query := selectSettingSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return settings, err
	}

	defer rows.Close()

	for rows.Next() {
		setting, err := r.scanRowIntoSetting(rows)
		if err != nil {
			return settings, err
		}

		settings = append(settings, setting)
	}

	return settings, rows.Err()
}

func (r *AppSettingRepository) GetSettingByID(
	ctx context.Context,
	settingID int64,
) (*entities.Setting, error) {
	row := r.db.QueryRow(selectSettingByIDSQL, settingID)
	return r.scanRowIntoSetting(row)
}

func (r *AppSettingRepository) GetZReportTotals(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	isZeportedCheck bool,
) (entities.ZReportTotals, error) {

	var zReportTotals entities.ZReportTotals

	args := []interface{}{}
	query := selectZReportSummarySQL

	if isZeportedCheck {
		query += " AND is_z_reported=false"
	}

	err := operations.QueryRowContext(ctx, query, args...).Scan(
		&zReportTotals.Count,
		&zReportTotals.NetAmount,
		&zReportTotals.VatAmount,
		&zReportTotals.TotalAmount,
	)
	if err != nil {
		return zReportTotals, err
	}
	return zReportTotals, nil
}

func (r *AppSettingRepository) GetSettingByName(
	ctx context.Context,
	email string,
) (*entities.Setting, error) {
	row := r.db.QueryRow(selectSettingByNameSQL, email)
	return r.scanRowIntoSetting(row)
}

func (r *AppSettingRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	setting *entities.Setting,
) error {

	setting.Timestamps.Touch()
	var err error

	if setting.IsNew() {
		res := operations.QueryRowContext(
			ctx,
			createSettingSQL,
			setting.Name,
			setting.Value,
			setting.CreatedBy,
			setting.UpdatedBy,
			setting.CreatedAt,
			setting.UpdatedAt,
		)

		err = res.Scan(&setting.ID)

	} else {

		_, err = operations.ExecContext(
			ctx,
			updateSettingSQL,
			setting.Name,
			setting.Value,
			setting.UpdatedBy,
			setting.UpdatedAt,
			setting.ID,
		)
	}

	if err != nil {
		log.Printf("error saving setting, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppSettingRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	settings []*entities.Setting,
) error {

	query := insertSettingSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(settings))

	for index, setting := range settings {
		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4, currentIndex+5)
		currentIndex += 6
		args = append(args, setting.Name, setting.Value, setting.CreatedBy, setting.UpdatedBy, time.Now(), time.Now())
	}

	query += strings.Join(values, ",")
	query += " ON CONFLICT(name) DO UPDATE SET value = EXCLUDED.value, updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&settings[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppSettingRepository) SelectZReportSummary(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.ZReportTotals, error) {

	zReportTotals := &entities.ZReportTotals{}

	return zReportTotals, nil
}

func (r *AppSettingRepository) UpdateMultipleSettings(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	settings []*entities.Setting,
) error {

	query := updateMultipleSettingsSQL + " FROM (VALUES "
	values := make([]string, len(settings))
	args := make([]interface{}, 0)
	currentIndex := 1

	for index, setting := range settings {
		values[index] = fmt.Sprintf("($%d, $%d::bigint, $%d::timestamptz)",
			currentIndex, currentIndex+1, currentIndex+2)
		currentIndex += 3
		args = append(args, setting.Value, setting.UpdatedBy, time.Now())
	}

	query += strings.Join(values, ",")
	query += ") AS u(id, value, updated_by, updated_at) where u.id = s.id"
	_, err := operations.ExecContext(ctx, query, args...)

	return err
}

func (r *AppSettingRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND LOWER(name) LIKE '%%' || $%d || '%%' ", currentIndex)
		args = append(args, strings.ToLower(filter.Search))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppSettingRepository) scanRowIntoSetting(
	rowScanner txns_db.RowScanner,
) (*entities.Setting, error) {

	var setting entities.Setting

	err := rowScanner.Scan(
		&setting.ID,
		&setting.Name,
		&setting.Value,
		&setting.CreatedBy,
		&setting.UpdatedBy,
		&setting.CreatedAt,
		&setting.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning setting,  err=[%v]\n", err.Error())
		return &setting, err
	}

	return &setting, nil
}
