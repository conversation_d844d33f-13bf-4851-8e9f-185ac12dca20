// Package providers contains service providers for the application
package providers

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// Common error codes for device operations
const (
	ErrCodeInvalidPath      = "DM001"
	ErrCodeFileRead         = "DM002"
	ErrCodeFileWrite        = "DM003"
	ErrCodeEncryption       = "DM004"
	ErrCodeDecryption       = "DM005"
	ErrCodeInvalidKey       = "DM006"
	ErrCodeFileNotFound     = "DM007"
	ErrCodeCreateDirectory  = "DM008"
	ErrCodeInvalidParameter = "DM009"
)

// Device file names
const (
	CmcKeyFile   = "cmcKey"
	IntrlKeyFile = "intrlKey"
	SignKeyFile  = "signKey"
	SdcIdFile    = "sdcId"
	MrcNoFile    = "mrcNo"
	DvcSrlNoFile = "dvcSrlNo"
)

// DeviceError represents an error from device operations
type DeviceError struct {
	Code    string
	Message string
	Err     error
}

// Error implements the error interface
func (e *DeviceError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("Device error %s: %s - %v", e.Code, e.Message, e.Err)
	}
	return fmt.Sprintf("Device error %s: %s", e.Code, e.Message)
}

// Unwrap returns the underlying error
func (e *DeviceError) Unwrap() error {
	return e.Err
}

// KeyInfo contains the various keys used by the system
type KeyInfo struct {
	CmcKey   string
	IntrlKey string
	SignKey  string
}

// DeviceManagerConfig contains configuration for the DeviceManager
type DeviceManagerConfig struct {
	// AesKey is the key used for AES encryption/decryption
	AesKey []byte

	// BaseDevicePath is the base path for device files
	BaseDevicePath string

	// LogLevel sets the logging level
	LogLevel zerolog.Level
}

// PathProvider defines the interface for getting device paths
type PathProvider interface {
	GetEbmDevicePath(tinBhfPath string) (string, error)
}

// Encrypter defines the interface for encryption and decryption
type Encrypter interface {
	Encrypt(plaintext string) (string, error)
	Decrypt(ciphertext string) (string, error)
}

// DefaultPathProvider implements the PathProvider interface
type DefaultPathProvider struct {
	basePath string
}

// NewDefaultPathProvider creates a new default path provider
func NewDefaultPathProvider(basePath string) *DefaultPathProvider {
	return &DefaultPathProvider{
		basePath: basePath,
	}
}

// GetEbmDevicePath returns the device path for the given TIN and BHF
func (p *DefaultPathProvider) GetEbmDevicePath(tinBhfPath string) (string, error) {
	if tinBhfPath == "" {
		return "", &DeviceError{
			Code:    ErrCodeInvalidParameter,
			Message: "TIN and BHF path cannot be empty",
		}
	}

	path := filepath.Join(p.basePath, tinBhfPath)

	// Ensure the directory exists
	if err := os.MkdirAll(path, 0755); err != nil {
		return "", &DeviceError{
			Code:    ErrCodeCreateDirectory,
			Message: "Failed to create device directory",
			Err:     err,
		}
	}

	return path, nil
}

// AesEncrypter implements the Encrypter interface using AES
type AesEncrypter struct {
	key []byte
	mu  sync.Mutex
}

// NewAesEncrypter creates a new AES encrypter
func NewAesEncrypter(key []byte) (*AesEncrypter, error) {
	if len(key) != 16 && len(key) != 24 && len(key) != 32 {
		return nil, &DeviceError{
			Code:    ErrCodeInvalidKey,
			Message: "AES key must be 16, 24, or 32 bytes",
		}
	}

	return &AesEncrypter{
		key: key,
	}, nil
}

// Encrypt encrypts the plaintext
func (a *AesEncrypter) Encrypt(plaintext string) (string, error) {
	a.mu.Lock()
	defer a.mu.Unlock()

	block, err := aes.NewCipher(a.key)
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeEncryption,
			Message: "Failed to create AES cipher",
			Err:     err,
		}
	}

	// Create a new GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeEncryption,
			Message: "Failed to create GCM",
			Err:     err,
		}
	}

	// Create a nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", &DeviceError{
			Code:    ErrCodeEncryption,
			Message: "Failed to create nonce",
			Err:     err,
		}
	}

	// Encrypt and seal
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// Return base64 encoded ciphertext
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt decrypts the ciphertext
func (a *AesEncrypter) Decrypt(ciphertext string) (string, error) {
	a.mu.Lock()
	defer a.mu.Unlock()

	// Decode base64
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeDecryption,
			Message: "Failed to decode base64",
			Err:     err,
		}
	}

	block, err := aes.NewCipher(a.key)
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeDecryption,
			Message: "Failed to create AES cipher",
			Err:     err,
		}
	}

	// Create a new GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeDecryption,
			Message: "Failed to create GCM",
			Err:     err,
		}
	}

	// Extract nonce and ciphertext
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", &DeviceError{
			Code:    ErrCodeDecryption,
			Message: "Ciphertext too short",
		}
	}

	nonce, ciphertextBytes := data[:nonceSize], data[nonceSize:]

	// Decrypt
	plaintextBytes, err := gcm.Open(nil, nonce, ciphertextBytes, nil)
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeDecryption,
			Message: "Failed to decrypt",
			Err:     err,
		}
	}

	return string(plaintextBytes), nil
}

// DeviceManager manages device information
type DeviceManager struct {
	pathProvider PathProvider
	encrypter    Encrypter
	logger       zerolog.Logger
	mu           sync.RWMutex
}

// NewDeviceManager creates a new device manager
func NewDeviceManager(config DeviceManagerConfig) (*DeviceManager, error) {
	// Create encrypter
	encrypter, err := NewAesEncrypter(config.AesKey)
	if err != nil {
		return nil, err
	}

	// Create path provider
	pathProvider := NewDefaultPathProvider(config.BaseDevicePath)

	// Create logger
	logger := log.With().Str("component", "DeviceManager").Logger().Level(config.LogLevel)

	return &DeviceManager{
		pathProvider: pathProvider,
		encrypter:    encrypter,
		logger:       logger,
	}, nil
}

// SetKey sets the keys in the device
func (d *DeviceManager) SetKey(info KeyInfo, tinBhfPath string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	// Get device path
	devicePath, err := d.pathProvider.GetEbmDevicePath(tinBhfPath)
	if err != nil {
		return err
	}

	// Encrypt and write CMC key
	encCmcKey, err := d.encrypter.Encrypt(info.CmcKey)
	if err != nil {
		return err
	}
	if err := os.WriteFile(filepath.Join(devicePath, CmcKeyFile), []byte(encCmcKey), 0644); err != nil {
		return &DeviceError{
			Code:    ErrCodeFileWrite,
			Message: "Failed to write CMC key",
			Err:     err,
		}
	}

	// Encrypt and write internal key
	encIntrlKey, err := d.encrypter.Encrypt(info.IntrlKey)
	if err != nil {
		return err
	}
	if err := os.WriteFile(filepath.Join(devicePath, IntrlKeyFile), []byte(encIntrlKey), 0644); err != nil {
		return &DeviceError{
			Code:    ErrCodeFileWrite,
			Message: "Failed to write internal key",
			Err:     err,
		}
	}

	// Encrypt and write sign key
	encSignKey, err := d.encrypter.Encrypt(info.SignKey)
	if err != nil {
		return err
	}
	if err := os.WriteFile(filepath.Join(devicePath, SignKeyFile), []byte(encSignKey), 0644); err != nil {
		return &DeviceError{
			Code:    ErrCodeFileWrite,
			Message: "Failed to write sign key",
			Err:     err,
		}
	}

	d.logger.Info().Str("tinBhfPath", tinBhfPath).Msg("Keys set successfully")
	return nil
}

// GetKeys gets the keys from the device
func (d *DeviceManager) GetKeys(tinBhfPath string) (KeyInfo, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	var info KeyInfo

	// Get device path
	devicePath, err := d.pathProvider.GetEbmDevicePath(tinBhfPath)
	if err != nil {
		return info, err
	}

	// Read and decrypt CMC key
	encCmcKey, err := os.ReadFile(filepath.Join(devicePath, CmcKeyFile))
	if err != nil {
		return info, &DeviceError{
			Code:    ErrCodeFileRead,
			Message: "Failed to read CMC key",
			Err:     err,
		}
	}
	cmcKey, err := d.encrypter.Decrypt(string(encCmcKey))
	if err != nil {
		return info, err
	}
	info.CmcKey = cmcKey

	// Read and decrypt internal key
	encIntrlKey, err := os.ReadFile(filepath.Join(devicePath, IntrlKeyFile))
	if err != nil {
		return info, &DeviceError{
			Code:    ErrCodeFileRead,
			Message: "Failed to read internal key",
			Err:     err,
		}
	}
	intrlKey, err := d.encrypter.Decrypt(string(encIntrlKey))
	if err != nil {
		return info, err
	}
	info.IntrlKey = intrlKey

	// Read and decrypt sign key
	encSignKey, err := os.ReadFile(filepath.Join(devicePath, SignKeyFile))
	if err != nil {
		return info, &DeviceError{
			Code:    ErrCodeFileRead,
			Message: "Failed to read sign key",
			Err:     err,
		}
	}
	signKey, err := d.encrypter.Decrypt(string(encSignKey))
	if err != nil {
		return info, err
	}
	info.SignKey = signKey

	return info, nil
}

// GetKey gets a specific key from the device
func (d *DeviceManager) GetKey(keyKind string, tinBhfPath string) (string, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	// Get device path
	devicePath, err := d.pathProvider.GetEbmDevicePath(tinBhfPath)
	if err != nil {
		return "", err
	}

	// Read encrypted key
	encKey, err := os.ReadFile(filepath.Join(devicePath, keyKind))
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeFileRead,
			Message: fmt.Sprintf("Failed to read %s", keyKind),
			Err:     err,
		}
	}

	// Decrypt key
	key, err := d.encrypter.Decrypt(string(encKey))
	if err != nil {
		return "", err
	}

	return key, nil
}

// SetSdcID sets the SDC ID in the device
func (d *DeviceManager) SetSdcID(sdcId string, tinBhfPath string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	// Get device path
	devicePath, err := d.pathProvider.GetEbmDevicePath(tinBhfPath)
	if err != nil {
		return err
	}

	// Encrypt SDC ID
	encSdcId, err := d.encrypter.Encrypt(sdcId)
	if err != nil {
		return err
	}

	// Write encrypted SDC ID
	if err := os.WriteFile(filepath.Join(devicePath, SdcIdFile), []byte(encSdcId), 0644); err != nil {
		return &DeviceError{
			Code:    ErrCodeFileWrite,
			Message: "Failed to write SDC ID",
			Err:     err,
		}
	}

	d.logger.Info().Str("tinBhfPath", tinBhfPath).Msg("SDC ID set successfully")
	return nil
}

// GetSdcID gets the SDC ID from the device
func (d *DeviceManager) GetSdcID(tinBhfPath string) (string, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	// Get device path
	devicePath, err := d.pathProvider.GetEbmDevicePath(tinBhfPath)
	if err != nil {
		return "", err
	}

	// Read encrypted SDC ID
	encSdcId, err := os.ReadFile(filepath.Join(devicePath, SdcIdFile))
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeFileRead,
			Message: "Failed to read SDC ID",
			Err:     err,
		}
	}

	// Decrypt SDC ID
	sdcId, err := d.encrypter.Decrypt(string(encSdcId))
	if err != nil {
		return "", err
	}

	return sdcId, nil
}

// SetMrcNo sets the MRC number in the device
func (d *DeviceManager) SetMrcNo(mrcNo string, tinBhfPath string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	// Get device path
	devicePath, err := d.pathProvider.GetEbmDevicePath(tinBhfPath)
	if err != nil {
		return err
	}

	// Encrypt MRC number
	encMrcNo, err := d.encrypter.Encrypt(mrcNo)
	if err != nil {
		return err
	}

	// Write encrypted MRC number
	if err := os.WriteFile(filepath.Join(devicePath, MrcNoFile), []byte(encMrcNo), 0644); err != nil {
		return &DeviceError{
			Code:    ErrCodeFileWrite,
			Message: "Failed to write MRC number",
			Err:     err,
		}
	}

	d.logger.Info().Str("tinBhfPath", tinBhfPath).Msg("MRC number set successfully")
	return nil
}

// GetMrcNo gets the MRC number from the device
func (d *DeviceManager) GetMrcNo(tinBhfPath string) (string, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	// Get device path
	devicePath, err := d.pathProvider.GetEbmDevicePath(tinBhfPath)
	if err != nil {
		return "", err
	}

	// Read encrypted MRC number
	encMrcNo, err := os.ReadFile(filepath.Join(devicePath, MrcNoFile))
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeFileRead,
			Message: "Failed to read MRC number",
			Err:     err,
		}
	}

	// Decrypt MRC number
	mrcNo, err := d.encrypter.Decrypt(string(encMrcNo))
	if err != nil {
		return "", err
	}

	return mrcNo, nil
}

// SetDeviceSerialNumber sets the device serial number in the device
func (d *DeviceManager) SetDeviceSerialNumber(dvcSrlNo string, tinBhfPath string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	// Get device path
	devicePath, err := d.pathProvider.GetEbmDevicePath(tinBhfPath)
	if err != nil {
		return err
	}

	// Encrypt device serial number
	encDvcSrlNo, err := d.encrypter.Encrypt(dvcSrlNo)
	if err != nil {
		return err
	}

	// Write encrypted device serial number
	if err := os.WriteFile(filepath.Join(devicePath, DvcSrlNoFile), []byte(encDvcSrlNo), 0644); err != nil {
		return &DeviceError{
			Code:    ErrCodeFileWrite,
			Message: "Failed to write device serial number",
			Err:     err,
		}
	}

	d.logger.Info().Str("tinBhfPath", tinBhfPath).Msg("Device serial number set successfully")
	return nil
}

// GetDeviceSerialNumber gets the device serial number from the device
func (d *DeviceManager) GetDeviceSerialNumber(tinBhfPath string) (string, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	// Get device path
	devicePath, err := d.pathProvider.GetEbmDevicePath(tinBhfPath)
	if err != nil {
		return "", err
	}

	// Read encrypted device serial number
	encDvcSrlNo, err := os.ReadFile(filepath.Join(devicePath, DvcSrlNoFile))
	if err != nil {
		return "", &DeviceError{
			Code:    ErrCodeFileRead,
			Message: "Failed to read device serial number",
			Err:     err,
		}
	}

	// Decrypt device serial number
	dvcSrlNo, err := d.encrypter.Decrypt(string(encDvcSrlNo))
	if err != nil {
		return "", err
	}

	return dvcSrlNo, nil
}
